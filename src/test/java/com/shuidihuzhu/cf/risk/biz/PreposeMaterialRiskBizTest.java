package com.shuidihuzhu.cf.risk.biz;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cf.risk.service.ICfBlacklistService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PreposeMaterialRiskBizTest{


    @Autowired
    private PreposeMaterialRiskBiz preposeMaterialRiskBiz;
    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private CfMaterialReadClient materialReadClient;
    @Resource
    private ICfBlacklistService blacklistService;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testMarkRisk() {
        BlacklistHitHighRiskDto blackByHighRisk = blacklistService.isBlackByHighRisk(2961388);
        System.out.println(blackByHighRisk);

//        String context = "{\"accidentDetail\":\"\",\"addSickbed\":\"\",\"allowanceImg\":\"\",\"birthday\":\"\",\"car\":1,\"carAmountArea\":6,\"carBrand\":\"\",\"carNum\":1,\"carSellDetail\":\"\",\"carSellingAmount\":200000,\"carSellingAmountArea\":6,\"carSellingCount\":1,\"carValue\":250000,\"comment\":\"\",\"communicationWay\":0,\"content\":\"风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了风险策略没了\",\"department\":\"\",\"diseaseDetail\":\"\",\"diseaseName\":\"风险策略没了\",\"financialAssetsAmount\":250000,\"financialAssetsAmountArea\":5,\"hasCarInsurance\":0,\"hasDebt\":1,\"hasFinancialAssets\":1,\"hasPersonalInsurance\":0,\"hasPoverty\":0,\"hasRaise\":1,\"homeIncome\":250000,\"homeIncomeArea\":6,\"homeOwningAmount\":10000000,\"homeOwningAmountArea\":7,\"hospital\":\"\",\"house\":1,\"houseAmountArea\":7,\"houseNum\":1,\"houseSellDetail\":\"\",\"houseSellingAmount\":1900000,\"houseSellingAmountArea\":7,\"houseSellingCount\":1,\"houseValue\":2000000,\"imageUrlType\":3,\"livingAllowance\":0,\"materialType\":2,\"medicalDisease\":\"风险策略没了\",\"medicalInsurance\":1,\"moneyUseFor\":\"\",\"operatorMis\":\"chenbing\",\"patientIdCard\":\"******************\",\"patientIdCardType\":1,\"patientName\":\"薛献杰\",\"patientRpDisease\":\"风险策略没了\",\"pictureUrl\":\"\",\"povertyImg\":\"\",\"preAuditImageUrl\":\"\",\"preposeType\":1,\"raiseAmount\":200000,\"raiseMaterialPic\":\"\",\"raiseMaterialVedio\":\"\",\"raiseMobile\":\"16688885571\",\"raiseName\":\"陈冰\",\"raisePatientRelation\":14,\"relationVersion\":0,\"remainAmount\":150000,\"riskLevel\":0,\"rpTreatmentInfo\":\"\",\"selfCryptoIdcard\":\"******************\",\"selfHouseAmountArea\":7,\"selfHouseNum\":1,\"selfHouseSellingAmount\":2900000,\"selfHouseSellingAmountArea\":7,\"selfHouseSellingCount\":1,\"selfHouseValue\":3000000,\"sickbed\":\"\",\"sickroom\":\"\",\"supplyDetail\":\"\",\"targetAmount\":150000,\"title\":\"风险策略没了\",\"treatmentInfo\":\"\",\"useForMedical\":1,\"version\":4,\"volunteerUniqueCode\":\"\"}";
//        PreposeMaterialModel.MaterialInfoVo materialInfoVo = JSON.parseObject(context,PreposeMaterialModel.MaterialInfoVo.class);
//        var cfPreposeMaterialRiskVo = preposeMaterialRiskBiz.markRisk(materialInfoVo);
//        System.out.println(cfPreposeMaterialRiskVo.toString());
    }

    @Test
    public void testMarkPropertyRisk() {
        RpcResult<CfPropertyInsuranceInfoModel> insuranceInfoModelRpcResult =
                materialReadClient.selectCfPropertyInsuranceInfo(2861661);
        var cfPreposeMaterialRiskVo = preposeMaterialRiskBiz.markPropertyRisk(insuranceInfoModelRpcResult.getData());
        System.out.println(cfPreposeMaterialRiskVo);
    }
}