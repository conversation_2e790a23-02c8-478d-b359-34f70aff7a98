package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeService;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeV2Service;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel.DiseaseMaterialTypeInModel;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.HouseThresholdParam;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseNameQuery;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseSpecialInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import com.shuidihuzhu.cf.risk.service.DiseaseStrategyService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.util.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class DiseaseControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
    @Autowired
    private DiseaseRpcService diseaseRpcService;
    @Autowired
    private DiseaseController diseaseController;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;
    @Autowired
    private DiseaseStrategyService diseaseStrategyService;
    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;
    @Autowired
    private DiseaseMaterialTypeInModel diseaseMaterialTypeInModel;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    /**
     *
     * Method: decideInfoAmountReasonable(DecideReasonableInfo decideReasonableInfo)
     *
     */
    @Test
    public void testDecideInfoAmountReasonable() throws Exception {
        List<String> s = Lists.newArrayList();
        s.add("烧伤");
        s.add("二尖瓣疾病");
        s.add("三尖瓣疾病");
        s.add("肺动脉瓣疾病");
        //早产儿,白血病,动脉导管未闭,肺癌,
        s = Splitter.on(",").splitToList("脑出血,,,,肝囊肿");

        DecideReasonableInfo decideReasonableInfo1 = new DecideReasonableInfo(s, 1);
        decideReasonableInfo1.setExtContent("疾病名称：肝右叶原发性巨块型肝Ca，肝硬化，急性肝功能损害，重度贫血，凝血功能障碍\n 工单ID：12312\n\n\n\n" +
                "操作人聂江南");
       /* List<DiseaseSpecialInfoVo> diseaseSpecialInfoVos = Lists.newArrayList();
        DiseaseSpecialInfoVo diseaseSpecialInfoVo = new DiseaseSpecialInfoVo();
        diseaseSpecialInfoVo.setSpecialDiseaseName("烧伤");
        diseaseSpecialInfoVo.setBurnLevel(1);
        diseaseSpecialInfoVo.setBurnArea(0);
        diseaseSpecialInfoVos.add(diseaseSpecialInfoVo);
        DiseaseSpecialInfoVo diseaseSpecialInfoVo2 = new DiseaseSpecialInfoVo();
        diseaseSpecialInfoVo2.setSpecialDiseaseName("烧伤");
        diseaseSpecialInfoVo2.setBurnLevel(2);
        diseaseSpecialInfoVo2.setBurnArea(0);
        diseaseSpecialInfoVos.add(diseaseSpecialInfoVo2);*/
        decideReasonableInfo1.setSpecialDiseaseInfo("" +
                "[{\"treatmentType\":1,\"choiceName\":\"选择分期或治疗方案\",\"specialDiseaseName\":\"椎体骨折\",\"choiceInfoList\":[{\"choiceId\":1368,\"choiceType\":1,\"choiceName\":\"保守治疗\",\"noSameTimeIds\":[],\"realCalculateChoice\":true,\"inputValue\":\"\",\"choiceInfoList\":[],\"disabled\":false}],\"disabled\":false,\"answerType\":1,\"answer\":1367}]"
        + "");
        System.out.println(JSON.toJSONString(diseaseRpcService.decideInfoAmountReasonable(10000000,
               JSONObject.parseObject(
                      "{\"caseId\":2722120,\"diseaseNameList\":[\"脑出血\"],\"extContent\":\"疾病名称：脑出血\\n工单ID：0\\n操作人筹款顾问-北京顾问团队-孙莹佳\",\"specialDiseaseInfo\":[{\"treatmentType\":2,\"choiceName\":\"选择治疗方式\",\"specialDiseaseName\":\"脑出血\",\"choiceInfoList\":[{\"choiceId\":0,\"choiceType\":1,\"choiceName\":\"知道\",\"noSameTimeIds\":[],\"realCalculateChoice\":false,\"inputValue\":null,\"specialBurn\":null,\"choiceInfoList\":[{\"choiceId\":670,\"choiceType\":2,\"choiceName\":\"保守治疗\",\"noSameTimeIds\":[671],\"realCalculateChoice\":true,\"inputValue\":\"\",\"specialBurn\":null,\"choiceInfoList\":[],\"disabled\":false},{\"choiceId\":672,\"choiceType\":2,\"choiceName\":\"ICU治疗\",\"noSameTimeIds\":null,\"realCalculateChoice\":true,\"inputValue\":\"\",\"specialBurn\":null,\"choiceInfoList\":[],\"disabled\":false},{\"choiceId\":1419,\"choiceType\":2,\"choiceName\":\"康复治疗\",\"noSameTimeIds\":null,\"realCalculateChoice\":true,\"inputValue\":\"\",\"specialBurn\":null,\"choiceInfoList\":[],\"disabled\":false}],\"disabled\":false,\"answerType\":2,\"answer\":[670,672,1419]}],\"disabled\":false,\"answerType\":1,\"answer\":0}]\"\",\"specialRaiseChoiceInfo\":\"\",\"diseaseResourceList\":[\"脑出血\"]}"
                       ,DecideReasonableInfo.class))));
//        JSONObject.parseObject(
//                "{\"caseId\":2722120,\"diseaseNameList\":[\"先天性心脏病\",\"肺动脉瓣狭窄\"],\"extContent\":\"疾病名称：先天性心脏病,肺动脉瓣狭窄\\n工单ID：7777943\\n操作人筹款顾问-北京顾问团队-孙莹佳\",\"specialDiseaseInfo\":\"\",\"specialRaiseChoiceInfo\":\"\",\"diseaseResourceList\":[\"先天性心脏病\",\"肺动脉瓣狭窄\"]}",DecideReasonableInfo.class);

    }


    @Resource
    private HighRiskJudgeV2Service highRiskJudgeService;
    /**
     *
     * Method: buildChoiceInfoList(int choiceType)
     *
     */
    @Test
    public void testBuildChoiceInfoList() throws Exception {
        HouseThresholdParam houseThresholdParam = new HouseThresholdParam();
        houseThresholdParam.setCaseId(3151630);
        highRiskJudgeService.getMResultByParam(houseThresholdParam);
        System.out.println(1);
        DiseaseStrategyRequest diseaseStrategyRequest =
                JSON.parseObject(
                        "{\"caseId\":2863218,\"executeStrategyEnum\":1,\"userId\":0,\"workOrderId\":0}",
                        DiseaseStrategyRequest.class );
        diseaseStrategyRequest.setCaseId(2863218);
        //diseaseStrategyRequest.setDiseaseNameList(Lists.newArrayList("白血病"));
        System.out.println(JSON.toJSONString(diseaseStrategyService.diseaseStrategy(diseaseStrategyRequest)));
    }

    @Test
    public void testLogList() throws Exception {
        DiseaseNameQuery diseaseNameQuery  = new DiseaseNameQuery(1, 10, "椎");
        Page<String> response =  riskDiseaseDataRpcBiz.findByDiseaseName(diseaseNameQuery);
        System.out.println(JSON.toJSONString(response));
    }
     
}
