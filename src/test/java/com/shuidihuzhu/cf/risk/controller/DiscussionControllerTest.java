package com.shuidihuzhu.cf.risk.controller;

import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.pf.common.v2.util.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class DiscussionControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
 
    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    /**
     *
     * Method: geDiscussionStatus(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid)
     *
     */
    @Test
    public void testGeDiscussionStatus() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "a57c3e7c-d6dd-4c57-8ff5-bc52f55a38e8git c");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,1300181,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/status/get", httpHeaders);

    }
     
    /**
     *
     * Method: discussionSubmit(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid, @ApiParam("评议标题") @RequestParam(name = "discussionTitle", defaultValue = "") String discussionTitle, @ApiParam("评议描述") @RequestParam(name = "discussionDesc", defaultValue = "") String discussionDesc, @ApiParam("评议图片列表") @RequestParam(name = "discussionImages", defaultValue = "") String discussionImages)
     *
     */
    @Test
    public void testDiscussionSubmit() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        params.add("discussionTitle", "测试");
        params.add("discussionDesc", "啊啊啊啊啊啊啊啊啊啊啊啊啊啊");
        params.add("discussionImages", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd,123123");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/submit", httpHeaders);
    }
     
    /**
     *
     * Method: getDiscussionContent(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid)
     *
     */
    @Test
    public void testGetDiscussionContent() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/content/get", httpHeaders);
    }
     
    /**
     *
     * Method: getDiscussionUserStatus(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid)
     *
     */
    @Test
    public void testGetDiscussionUserStatus() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/user-status/get", httpHeaders);
    }
     
    /**
     *
     * Method: discussionResult(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid, @ApiParam("案例编号") @RequestParam(name = "discussionResult")Boolean discussionResult)
     *
     */
    @Test
    public void testDiscussionResult() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        params.add("discussionResult", "true");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/result", httpHeaders);
    }
     
    /**
     *
     * Method: discussionDetail(@ApiParam("案例编号") @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid)
     *
     */
    @Test
    public void testDiscussionDetail() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/detail", httpHeaders);
    }
     
    /**
     *
     * Method: addDiscussionComment(@ApiParam("案例编号")
                                                   @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid, @ApiParam("评论内容")
                                               @RequestParam(name = "discussionCommentContent", defaultValue = "") String discussionCommentContent)
     *
     */
    @Test
    public void testAddDiscussionComment() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "9255904f-f7f5-4344-8bb1-f9cc6e2ecadd");
        params.add("discussionCommentContent", "这是一条评论");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/comment/add", httpHeaders);
    }
     
    /**
     *
     * Method: commentPraise(@ApiParam("评论内容")
                                            @RequestParam(name = "discussionCommentId", defaultValue = "") long discussionCommentId)
     *
     */
    @Test
    public void testCommentPraise() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("discussionCommentId", "1");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/comment/praise", httpHeaders);
    }
     
    /**
     *
     * Method: commentList(@ApiParam("案例编号")
                                          @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid, @ApiParam("案例编号")
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @ApiParam("案例编号")
                                          @RequestParam(name = "current", defaultValue = "1") Integer current)
     *
     */
    @Test
    public void testCommentList() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("infoUuid", "0e8eeb50-66f4-4c35-9f95-4e2558e20267");
        params.add("pageSize", "10");
        params.add("current", "1");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk/discussion/comment/list", httpHeaders);
    }
     
     
}
