package com.shuidihuzhu.cf.risk.controller;

import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistPreTrialAdoptionDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistReportDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cf.risk.service.ICfBlacklistService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum.*;

/** 
* BlacklistVerifyController Tester. 
* 
* <AUTHOR> 
* @since <pre>8月 6, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class BlacklistVerifyControllerTest {

    @Resource
    private ICfBlacklistService blacklistService;
    @Autowired
    protected MockMvc mockMvc;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 

    @Test
    public void testController(){
        List<BlacklistVerifyDto> blacklistVerifyDtoList = new ArrayList<>();
        BlacklistVerifyDto blacklistVerifyDto1 = new BlacklistVerifyDto();
        blacklistVerifyDto1.setVerifyType(BlacklistVerifyTypeEnum.MOBILE.getCode());
        blacklistVerifyDto1.setVerifyData(shuidiCipher.decrypt("ScThBmW4mQX4oig3iOYImQ=="));
        blacklistVerifyDtoList.add(blacklistVerifyDto1);

        BlacklistVerifyDto blacklistVerifyDto2 = new BlacklistVerifyDto();
        blacklistVerifyDto2.setVerifyType(BlacklistVerifyTypeEnum.BORN_CARD.getCode());
        blacklistVerifyDto2.setVerifyData(shuidiCipher.decrypt("ScThBmW4mQX4oig3iOYImQ=="));
        blacklistVerifyDtoList.add(blacklistVerifyDto2);

        BlacklistVerifyDto blacklistVerifyDto3 = new BlacklistVerifyDto();
        blacklistVerifyDto3.setVerifyType(BlacklistVerifyTypeEnum.ID_CARD.getCode());
        blacklistVerifyDto3.setVerifyData(shuidiCipher.decrypt("EibOUzNv/rPa+aEOgokxEw=="));
        blacklistVerifyDtoList.add(blacklistVerifyDto3);

        BlacklistVerifyDto blacklistVerifyDto4 = new BlacklistVerifyDto();
        blacklistVerifyDto4.setVerifyType(BlacklistVerifyTypeEnum.USER_ID.getCode());
        blacklistVerifyDto4.setVerifyData("9828");
        blacklistVerifyDtoList.add(blacklistVerifyDto4);
        blacklistService.checkHit(blacklistVerifyDtoList);
        System.out.println(blacklistVerifyDtoList);
    }

    @Test
    public void testPreTrialAdoption(){
        BlacklistPreTrialAdoptionDto blacklistVerifyDtoList = new BlacklistPreTrialAdoptionDto(2293900, "2020-09-15 12:02:40",
                BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL.getCode(), "15703820386",
                365892064, "******************",
                "******************", 1);
        blacklistService.checkPreTrialAdoption(blacklistVerifyDtoList);
        System.out.println(blacklistVerifyDtoList);
    }

    @Test
    public void reportQuestioner() {

    }

}
