package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.cf.risk.service.UserIncomeService;
import com.shuidihuzhu.cf.risk.util.third.BairongUtil;
import com.shuidihuzhu.cf.risk.util.third.ShangyongUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-02-12
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class UserIncomeTest {

    @Autowired
    private BairongUtil bairongUtil;
    @Autowired
    private ShangyongUtil shangyongUtil;
    @Autowired
    private UserIncomeService userIncomeService;

    @Test
    public void testUserIncome() {
        Map<String, Map<String, String>> thirdData = this.userIncomeService.getThirdData("320102199702173410",
                "15588785887", "测一", "00");
        log.info("responseJson:{}", thirdData);
    }

    @Test
    public void testGetBairongPortrayalData() {
        Map<String, String> bairongData = this.userIncomeService.getBairongData("123456199910221345",
                "15588785887", "张三");
        log.info("responseJson:{}", JSON.toJSONString(bairongData));
    }

    @Test
    public void testGetShangrongPortrayalData() {
        Map<String, String> shangyongData = this.userIncomeService.getShangyongData("320102199702173410",
                "15588785887", "测一", "00");
        log.info("responseJson:{}", JSON.toJSONString(shangyongData));
    }
}
