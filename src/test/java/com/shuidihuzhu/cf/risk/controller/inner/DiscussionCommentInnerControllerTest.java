package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.app.Application;
import com.shuidihuzhu.pf.common.v2.util.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class DiscussionCommentInnerControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
 
    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }
     
    /**
     *
     * Method: findById(@RequestParam("commentIdList") List<Long> commentIdList)
     *
     */
    @Test
    public void testFindById() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("commentIdList", "1,2");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/innerapi/cf-risk/discussion/comment/find-by-id", httpHeaders);

    }
     
     
}
