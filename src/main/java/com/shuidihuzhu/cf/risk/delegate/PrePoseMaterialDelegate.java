package com.shuidihuzhu.cf.risk.delegate;

import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.pay.enums.BankCardVerifyEnum;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ReportRelation;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2023/1/11 14:18
 * @Description:
 */
@Slf4j
@Service
public class PrePoseMaterialDelegate {

    @Resource
    private PreposeMaterialClient preposeMaterialClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;


    public PreposeMaterialModel.MaterialInfoVo selectLatelyByCaseId(int caseId) {
        Response<ReportRelation> reportRelationResponse = clewPreproseMaterialFeignClient.getByCaseId(caseId);
        ReportRelation reportRelation = Optional.ofNullable(reportRelationResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(reportRelation)) {
            log.info("PrePoseMaterialDelegate selectLatelyByCaseId reportRelation is null");
            return null;
        }
        long prePoseMaterialId = reportRelation.getPreposeMaterialId();
        RpcResult<PreposeMaterialModel.MaterialInfoVo> materialInfoVoRpcResult = preposeMaterialClient.selectMaterialsById(prePoseMaterialId);
        log.info("PrePoseMaterialDelegate selectMaterialsById {} {}", prePoseMaterialId, materialInfoVoRpcResult.getCode());
        return Optional.of(materialInfoVoRpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
    }
}
