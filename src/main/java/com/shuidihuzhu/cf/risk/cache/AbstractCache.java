package com.shuidihuzhu.cf.risk.cache;

/**
 * <AUTHOR>
 * @date 2020/8/26 21:24
 */

import com.alibaba.fastjson.JSON;
import com.google.common.base.Ticker;
import com.google.common.cache.*;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * @date 2018-03-16
 * @param <K>
 * @param <V>
 *
 */
@Slf4j
public abstract class AbstractCache<K, V> {

    private static final int nThreads = 4;

    private LoadingCache<K, V> cache;
    @Resource(name = "asyncThreadPool")
    private ThreadPoolTaskExecutor taskExecutor;

    private LoadingCache<K, V> getCache() {
        if(cache != null) {
            return cache;
        }
        synchronized (this) {
            if(cache != null) {
                return cache;
            }
            log.info("init AbstractCache...");
            cache = CacheBuilder.newBuilder().concurrencyLevel(nThreads).maximumSize(3000)
                    .expireAfterWrite(getDefaultExpireAfterWriteMin(), TimeUnit.MINUTES)
                    .refreshAfterWrite(getDefaultRefreshAfterWriteMin(), TimeUnit.MINUTES)
                    .ticker(Ticker.systemTicker()).recordStats()
                    .removalListener(new RemovalListener<Object, Object>() {
                        @Override
                        public void onRemoval(RemovalNotification<Object, Object> notification) {
                            log.warn("key {} was removed, cause is {} ", notification.getKey(), notification.getCause());
                        }
                    }).build(new CacheLoader<K, V>() {
                        @Override
                        public V load(K key) {
                            log.info("load-, key={}", key);
                            log.info("cache[{}] stats[{}]", cache, getCacheStat().toString());
                            V v = queryData(key);
                            cache.put(key, v);
                            return v;
                        }

                        @Override
                        public ListenableFuture<V> reload(K key, V oldValue) throws Exception {
                            ListenableFutureTask<V> task = ListenableFutureTask.create(() -> {
                                try {
                                    return load(key);
                                } catch (Exception e) {
                                    log.error("", e);
                                }
                                return oldValue;
                            });
                            taskExecutor.execute(task);//这里将这个task放到自定义的线程池中去执行，返回一个futrue，可以通过future获取线程执行获取的值
                            return task;
                        }
                    });

            return cache;
        }
    }

    protected abstract V queryData(@NotNull final K key);

    protected abstract int getExpireAfterWriteMin();

    protected abstract int getRefreshAfterWriteMin();

    private int getDefaultExpireAfterWriteMin() {
        int expireAfterWriteMin = getExpireAfterWriteMin();
        if(expireAfterWriteMin <= 0) {
            return 15;
        }
        return expireAfterWriteMin;
    }

    private int getDefaultRefreshAfterWriteMin() {
        int refreshAfterWriteMin = getRefreshAfterWriteMin();
        if(refreshAfterWriteMin <= 0) {
            return 5;
        }
        return refreshAfterWriteMin;
    }

    protected V getValue(@NotNull final K key) throws ExecutionException {
        log.debug("localCache getValue, key={}", JSON.toJSONString(key));
        return getCache().get(key);
    }

    protected void put(@NotNull final K key, @NotNull final V value) throws ExecutionException {
        getCache().put(key, value);
    }

    protected void invalidateAll(List<K> keys) {
        getCache().invalidateAll(keys);
    }

    protected void invalidateAll() {
        getCache().invalidateAll();
    }

    protected void refreshCache(K key) {
        getCache().refresh(key);
    }

    protected CacheStats getCacheStat() { return cache.stats(); }
}

