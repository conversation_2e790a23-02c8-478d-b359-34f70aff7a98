package com.shuidihuzhu.cf.risk.cache.blacklist;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.risk.cache.AbstractCache;
import com.shuidihuzhu.cf.risk.model.vo.blacklist.BlackListResult;
import com.shuidihuzhu.cf.risk.model.vo.blacklist.RiskBlacklistDataVo;
import com.shuidihuzhu.cf.risk.service.IBlacklistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CfBlackListCacheService extends AbstractCache<String, Map<Integer, Map<String, BlacklistContent>>> {

    private final static String KEY = "blacklist";

    @Resource
    private IBlacklistService blacklistService;

    @PostConstruct
    public void init(){
        //初始化是加载，防止启服影响接口调用
        getCacheAll();
    }

    public Map<Integer, Map<String, BlacklistContent>> getCacheAll(){
        try {
            return getValue(KEY);
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return Collections.emptyMap();
    }

    /**
     * 黑名单有修改，同步缓存单条更新
     * @param dataId
     */
    public void onBlackDataUpdate(long dataId) {

    }

    /**
     * @param key constant
     * @return {@code Map<BlacklistVerifyTypeEnum, Map<Object, BlacklistContent>>}
     */
    @Override
    protected Map<Integer, Map<String, BlacklistContent>> queryData(String key) {
        log.info("BlackListCacheService setCache!");
        long id = 0;int size = 5000;

        List<RiskBlacklistDataVo> finalBlacklist = Lists.newArrayListWithCapacity(size);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        while (true){
            BlackListResult blackListResult = blacklistService.listByLimit(id, size);
            List<RiskBlacklistDataVo> riskBlacklistDataVos = blackListResult.getListVos();
            finalBlacklist.addAll(riskBlacklistDataVos);

            int resize = riskBlacklistDataVos.size();
            if(resize <= 0){
                break;
            }
            id = blackListResult.getLastRefId();
        }

        stopWatch.stop();

        log.info("BlackListCacheService size:{}, cost:{}", finalBlacklist.size(), stopWatch.getTotalTimeMillis());

        return finalBlacklist.parallelStream().collect(
                Collectors.groupingBy(RiskBlacklistDataVo::getVerifyType,
                        Collectors.toMap(RiskBlacklistDataVo::getData, BlacklistContent::new,
                                (b1, b2) -> {
                                    Set<Long> actionSet = Sets.newHashSet(b1.getActions());
                                    actionSet.addAll(b2.getActions());
                                    if (b1.getRefId() > b2.getRefId()) {
                                        b1.setActions(Lists.newArrayList(actionSet));
                                        return b1;
                                    }
                                    b2.setActions(Lists.newArrayList(actionSet));
                                    return b2;
                        })));
    }

    @Override
    public void invalidateAll() {
        super.invalidateAll();
    }

    public void refresh() {
        refreshCache(KEY);
    }

    public String getLocalCacheStat() {
        return getCacheStat().toString();
    }

    @Override
    protected int getExpireAfterWriteMin() {
        return 0;
    }

    @Override
    protected int getRefreshAfterWriteMin() {
        return 3;
    }

}
