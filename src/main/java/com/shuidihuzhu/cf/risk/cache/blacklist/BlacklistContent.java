package com.shuidihuzhu.cf.risk.cache.blacklist;

import com.shuidihuzhu.cf.risk.model.vo.blacklist.RiskBlacklistDataVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public final class BlacklistContent {
    public BlacklistContent(RiskBlacklistDataVo riskBlacklistDataVo) {
        this.refId = riskBlacklistDataVo.getId();
        this.typeIds = riskBlacklistDataVo.getTypeIds();
        this.actions = riskBlacklistDataVo.getActionIds();
        this.blacknessReason = riskBlacklistDataVo.getOperateReason();
        this.userName = riskBlacklistDataVo.getUserName();
    }

    private Long refId;
    private List<Long> typeIds;
    private List<Long> actions;
    private String userName;
    private String blacknessReason;
}
