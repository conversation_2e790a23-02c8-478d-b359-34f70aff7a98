package com.shuidihuzhu.cf.risk.dao.executestrategy;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyRel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskExecuteStrategyRelDao {


    List<RiskExecuteStrategyRel> getByStrategyId(@Param("strategyId") long strategyId);
}
