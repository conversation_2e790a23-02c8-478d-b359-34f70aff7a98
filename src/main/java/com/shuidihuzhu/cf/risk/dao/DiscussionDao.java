package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.Discussion;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface DiscussionDao {

    int save(Discussion discussion);


    Discussion getByCaseId(int caseId);

    int updateStatus(@Param("newStatus") int newStatus,
                     @Param("oldStatus") int oldStatus,
                     @Param("id") long id);

    int updateCheckStatus(@Param("newStatus") int newStatus,
                          @Param("oldStatus") int oldStatus,
                          @Param("discussionId") long discussionId);


    Discussion findById(long id);

    int updateDelById(long id);

    int submit(@Param("discussionTitle") String discussionTitle,
               @Param("discussionDesc") String discussionDesc,
               @Param("discussionImgList") String discussionImgList,
               @Param("caseId") int caseId,
               @Param("checkStatus") int checkStatus);


    List<Discussion> findByCaseId(@Param("caseIdList") List<Integer> caseIdList);


    int countByCaseId(int caseId);
}
