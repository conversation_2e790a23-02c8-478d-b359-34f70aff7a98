package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/12/29 17:40
 * @Description:
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface DiseaseAmountResultRecordDao {

    void insert(DiseaseAmountResultRecord diseaseAmountResultRecord);

    List<DiseaseAmountResultRecord> getRecordByCaseIdAndReasonTypeList(@Param("caseId") int caseId, List<Integer> reasonTypeList);
}
