package com.shuidihuzhu.cf.risk.dao.highrisk;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDO;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface HighRiskRecordDao {

    int insert(HighRiskRecordDO data);

    HighRiskRecordDO getLastByCondition(@Param("caseId") int caseId, @Param("workOrderId") long workOrderId, @Param("source") int source);

    List<HighRiskRecordDO> getListByCaseId(@Param("caseId") int caseId);
}
