package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface SpecialDiseaseRuleDao {


    SpecialDiseaseRule getByClassName(@Param("diseaseClassName") String diseaseClassName);


    List<SpecialDiseaseRule> findByClassNameList(@Param("diseaseClassNameList") List<String> diseaseClassNameList);


    List<String> findAllSpecialClassName();



    List<SpecialDiseaseRule> findByClassNameListAndType(@Param("diseaseClassNameList") List<String> diseaseClassNameList,
                                                        @Param("type") int type);
}
