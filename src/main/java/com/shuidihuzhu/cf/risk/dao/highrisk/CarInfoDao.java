package com.shuidihuzhu.cf.risk.dao.highrisk;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.highrisk.CarInfoDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface CarInfoDao {

    CarInfoDO getCarRiskInfo(@Param("carInfo") String carInfo);


}
