package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskDiseaseTreatmentProjectRpcDao {


    List<RiskDiseaseTreatmentProject> findByDiseaseIdList(@Param("diseaseIdList") List<Long> diseaseIdList);


    List<RiskDiseaseTreatmentProject> findById(@Param("treatmentIds") List<Long> treatmentIds);

    RiskDiseaseTreatmentProject findByTreatmentNameAndDiseaseId(@Param("treatmentName") String treatmentName,
                                                                @Param("diseaseId") long diseaseId);

    List<RiskDiseaseTreatmentProject> findByTreatmentName(@Param("treatmentName") String treatmentName);

    List<RiskDiseaseTreatmentProject> findByLikeTreatmentName(@Param("likeTreatmentName") String likeTreatmentName);

    RiskDiseaseTreatmentProject findByLikeTreatmentNameAndDiseaseId(@Param("treatmentName") String likeTreatmentName,
                                                                @Param("diseaseId") long diseaseId);

}
