package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.CfRiskComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface CfRiskCommentDao {


    List<CfRiskComment> getByCommentIds(@Param("commentIdList") List<Long> commentIdList);
}
