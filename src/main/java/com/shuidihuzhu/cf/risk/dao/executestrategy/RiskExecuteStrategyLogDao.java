package com.shuidihuzhu.cf.risk.dao.executestrategy;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskExecuteStrategyLogDao {

    int saveList(@Param("riskExecuteStrategyLogs") List<RiskExecuteStrategyLog> riskExecuteStrategyLogs);


    List<RiskExecuteStrategyLog> getByCaseId(@Param("caseId") int caseId);
}
