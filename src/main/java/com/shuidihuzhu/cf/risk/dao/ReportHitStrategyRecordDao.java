package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface ReportHitStrategyRecordDao {

    int batchInsert(@Param("reportHitStrategyRecordList") List<ReportHitStrategyRecord> reportHitStrategyRecordList);

    List<ReportHitStrategyRecord> getByReportIds(@Param("reportIds") List<Integer> reportIds);

    List<ReportHitStrategyRecord> listByCursor(@Param("lastId") long lastId, @Param("limit") Integer limit);

    int updateEncryptById(@Param("id") long id, @Param("encryptMobile") String encryptMobile,
                          @Param("encryptReportMobile") String encryptReportMobile, @Param("encryptIdCard") String encryptIdCard);

    int updateMobileById(@Param("id") long id, @Param("mobile") String mobile,
                         @Param("reportMobile") String reportMobile, @Param("idCard") String idCard);
}
