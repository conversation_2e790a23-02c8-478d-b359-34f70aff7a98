package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.UserIncomeThird;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface UserIncomeThirdDao {

    int save(UserIncomeThird userIncomeThird);

    UserIncomeThird getByIdCard(String idCard, String realName, int thirdType, int thirdStatus, String modelType);

}
