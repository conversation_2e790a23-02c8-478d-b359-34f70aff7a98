package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.CfRiskComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface CfRiskAccidentCaseRecordDao {

    int save(@Param("triggerTiming") String triggerTiming,@Param("caseId") int caseId,
             @Param("operatorId") int operatorId,@Param("workOrderId") long workOrderId,
             @Param("callResult") String callResult,@Param("caseTitle") String caseTitle,@Param("caseContent") String caseContent);
}
