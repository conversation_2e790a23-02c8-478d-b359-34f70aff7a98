package com.shuidihuzhu.cf.risk.dao.blacklist;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataActionRef;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskBlacklistDataActionRefDao {
    RiskBlacklistDataActionRef selectByPrimaryKey(Long id);

    List<RiskBlacklistDataActionRef> listByLimitOptionalActionIds(@Param("actionIds") List<Long> actionIds, @Param("previousId") Long previousId, @Param("limit") Integer limit);

    RiskBlacklistDataActionRef listByDataIdAndActionId(@Param("dataIds") List<Long> dataIds, @Param("actionId") Long actionId);
}