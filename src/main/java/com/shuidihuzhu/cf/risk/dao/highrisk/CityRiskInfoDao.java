package com.shuidihuzhu.cf.risk.dao.highrisk;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.highrisk.CityRiskInfoDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface CityRiskInfoDao {
        CityRiskInfoDO getCityRiskInfo(@Param("province") String province, @Param("city") String city, @Param("county") String county);

        Integer getMinAmountCityRiskInfo();
}
