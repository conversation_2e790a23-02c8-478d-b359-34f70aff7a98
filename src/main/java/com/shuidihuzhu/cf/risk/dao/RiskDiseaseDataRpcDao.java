package com.shuidihuzhu.cf.risk.dao;

import com.github.pagehelper.Page;
import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.ibatis.annotations.Param;
import org.redisson.api.RList;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskDiseaseDataRpcDao {



    RiskDiseaseData getByClassName(@Param("diseaseClassName") String diseaseClassName);


    RiskDiseaseData getById(@Param("diseaseId") long diseaseId);


    List<RiskDiseaseData> getByClassNameList(@Param("diseaseNameList") List<String> diseaseNameList);


    List<String> findAllDiseaseName();


    List<String> findByDiseaseName(@Param("diseasNameKeyWord") String diseasNameKeyWord);

    List<RiskDiseaseData> getByIdList(@Param("diseaseId") List<Long> idList);
}
