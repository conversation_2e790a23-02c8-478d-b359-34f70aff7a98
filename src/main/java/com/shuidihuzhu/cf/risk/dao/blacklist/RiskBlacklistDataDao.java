package com.shuidihuzhu.cf.risk.dao.blacklist;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.model.po.blacklist.query.BlacklistDataQuery;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskBlacklistDataDao {
    RiskBlacklistData selectByPrimaryKey(Long id);

    List<RiskBlacklistData> listByIds(Collection<Long> ids);

    List<RiskBlacklistData> getByUserIdOrUserIdBind(@Param("userId") Long userId);

    List<RiskBlacklistData> listByQuery(BlacklistDataQuery blacklistDataQuery);

}