package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.TrustPersonelInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2022/8/3 5:44 下午
 */
@DataSource(RiskDS.CF_RISK_SPIDER)
public interface TrustPersonelInfoDao {

    int addInfo(@Param("name") String name, @Param("idCard") String idCard, @Param("trustMsg") String trustMsg,
                @Param("thirdType") int thirdType);

    TrustPersonelInfo getByNameAndCard(@Param("name") String name, @Param("idCard") String idCard);

    int updateInfo(@Param("name") String name, @Param("idCard") String idCard, @Param("trustMsg") String trustMsg);

}
