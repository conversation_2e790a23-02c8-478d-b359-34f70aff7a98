package com.shuidihuzhu.cf.risk.dao.hit;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskStrategyHitRecordDao {
    List<RiskStrategyHitRecord> listByCaseIdPhase(@Param("caseId") Integer caseId, @Param("hitPhase") Integer hitPhase);
}