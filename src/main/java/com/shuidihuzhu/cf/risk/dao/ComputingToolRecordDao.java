package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("CF_RISK_DATASOURCE_RW")
public interface ComputingToolRecordDao {
    int insert(ComputingToolRecordDO record);

    List<ComputingToolRecordDO> selectByExtNameAndExtId(@Param("extName") String extName, @Param("extId") long extId);

    ComputingToolRecordDO selectById(@Param("id") long id);
}
