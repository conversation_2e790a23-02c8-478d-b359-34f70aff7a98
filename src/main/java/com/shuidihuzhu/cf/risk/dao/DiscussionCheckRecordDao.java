package com.shuidihuzhu.cf.risk.dao;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.DiscussionCheckRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface DiscussionCheckRecordDao {

    DiscussionCheckRecord findLastByDiscussId(@Param("discussionId") long discussionId);
}
