package com.shuidihuzhu.cf.risk.dao.blacklist;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataTypeRef;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.Collection;
import java.util.List;

@DataSource(RiskDS.CF_RISK_DATASOURCE_RW)
public interface RiskBlacklistDataTypeRefDao {

    RiskBlacklistDataTypeRef selectByPrimaryKey(Long id);

    List<RiskBlacklistDataTypeRef> listByDataIds(Collection<Long> dataIds);

}