package com.shuidihuzhu.cf.risk.app.configuration;

import brave.Tracing;
import brave.okhttp3.TracingInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/9/2 16:13
 */
@Slf4j
@Configuration
public class ClientConfiguration {

    /**
     * -- okHttp3 config 请求设置
     */
    private static final int SYNC_WRITE_TIMEOUT = 5000;
    private static final int SYNC_CONNECT_TIMEOUT = 5000;
    private static final int SYNC_READ_TIMEOUT = 5000;
    private static final int MAX_IDLE_CONNECTION = 100;
    private static final int HTTP_KEEP_ALIVE_DURATION = 5;

    @Autowired
    private Interceptor interceptor;
    @Autowired
    private Tracing tracing;

    @Bean("okHttpClient")
    public OkHttpClient okHttpClient() {
        X509TrustManager trustAllManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            }
            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            }
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
        return new OkHttpClient.Builder()
                .sslSocketFactory(createTrustAllSSLFactory(trustAllManager), trustAllManager)
                .hostnameVerifier((s, sslSession) -> true)
                .connectTimeout(SYNC_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .readTimeout(SYNC_READ_TIMEOUT, TimeUnit.MILLISECONDS)
                .writeTimeout(SYNC_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(false)
                .dispatcher(new Dispatcher(
                        tracing.currentTraceContext()
                                .executorService(new Dispatcher().executorService())
                ))
                .addInterceptor(TracingInterceptor.create(tracing))
                .addInterceptor(interceptor)
                .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTION, HTTP_KEEP_ALIVE_DURATION, TimeUnit.MINUTES))
                .build();
    }

    private SSLSocketFactory createTrustAllSSLFactory(X509TrustManager trustAllManager) {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, new TrustManager[]{trustAllManager}, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
            log.error("", e);
        }
        return ssfFactory;
    }

}
