package com.shuidihuzhu.cf.risk.app;

import com.shuidihuzhu.cf.enhancer.subject.druid.EnableDruidMonitor;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.EnableDynamicThreadPool;
import com.shuidihuzhu.eb.grafana.configuration.plugin.groupdatasource.check.EnableGroupDataSourceUseCheck;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeCommonsClient;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeConsulHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDataSourceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDiskSpaceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeRedissonHealth;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {
        "com.shuidihuzhu",
},
        exclude = {
                DataSourceAutoConfiguration.class,
                ElasticsearchRestClientAutoConfiguration.class
        })
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.shuidihuzhu.client",
        "com.shuidihuzhu.data",
        "com.shuidihuzhu.cf.risk",
        "com.shuidihuzhu.client",
        "com.shuidihuzhu.cf.finance",
        "com.shuidihuzhu.cf.client",
        "com.shuidihuzhu.client.cf",
        "com.shuidihuzhu.alps.feign.ocean",
        "com.shuidihuzhu.auth"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableFakeRedissonHealth
@EnableFakeDataSourceHealth
@EnableFakeCommonsClient
@EnableFakeConsulHealth
@EnableFakeDiskSpaceHealth
@EnableGroupDataSourceUseCheck
@EnableAsync
@EnableDynamicThreadPool(globalModel = true)
@EnableDruidMonitor
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
