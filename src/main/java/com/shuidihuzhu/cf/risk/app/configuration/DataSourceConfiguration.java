package com.shuidihuzhu.cf.risk.app.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfiguration {

    @Bean(RiskDS.CF_RISK_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-risk-rpc-api-risk.cf-risk-rpc-api")
    public DataSource riskRiskDatasource() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(RiskDS.CF_RISK_SLAVE_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-risk-rpc-api-risk-data-slave.cf-risk-rpc-api")
    public DataSource riskRiskSlaveDatasource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(RiskDS.CF_RISK_SPIDER)
    @ConfigurationProperties("spring.datasource.druid.cf-gather-information-cf-spider.cf-risk-rpc-api")
    public DataSource riskRiskSpiderDatasource() {
        return DruidDataSourceBuilder.create().build();
    }

}
