package com.shuidihuzhu.cf.risk.app.configuration;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.util.redisson.RedissonHandlerWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/12.
 */
@Configuration
public class RedissonConfiguration {

    @Bean(RiskDS.CF_RISK_REDIS)
    @ConfigurationProperties("redisson-handler.cf-risk-rpc-api-cf-risk-redisson-handler.cf-risk-rpc-api")
    public RedissonHandler cfRiskRedissonHandler(){
        return new RedissonHandlerWrapper();
    }

}
