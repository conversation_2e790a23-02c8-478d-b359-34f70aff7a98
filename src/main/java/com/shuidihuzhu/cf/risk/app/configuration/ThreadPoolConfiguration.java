package com.shuidihuzhu.cf.risk.app.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2019-06-05 10:20
 */
@Configuration
public class ThreadPoolConfiguration {

    private static final int WAIT_TERMINAL_SECOND = 120;

    /**
     * job thread pool config
     */
    private static final int JOB_CORE_POOL_SIZE = 30;
    public static final int JOB_MAX_POOL_SIZE = 200;
    public static final int JOB_QUEUE_CAPACITY = 1000;
    private static final int JOB_KEEP_ALIVE_SECONDS = 1800;

    @Bean(value = "asyncThreadPool",destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor jobThreadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(JOB_CORE_POOL_SIZE);
        threadPoolTaskExecutor.setMaxPoolSize(JOB_MAX_POOL_SIZE);
        threadPoolTaskExecutor.setQueueCapacity(JOB_QUEUE_CAPACITY);
        threadPoolTaskExecutor.setKeepAliveSeconds(JOB_KEEP_ALIVE_SECONDS);
        //允许核心线程空闲时退出
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setThreadNamePrefix("AsyncExecutor-");
        //设置优雅关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(WAIT_TERMINAL_SECOND);
        return threadPoolTaskExecutor;
    }


}
