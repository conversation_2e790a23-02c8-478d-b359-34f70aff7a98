package com.shuidihuzhu.cf.risk.app.configuration;

import com.shuidihuzhu.common.web.filter.LogRequestFilter;
import com.shuidihuzhu.common.web.interceptor.OptionalSessionKeyValidateInterceptor;
import com.shuidihuzhu.common.web.interceptor.SessionKeyValidateInterceptor;
import com.thetransactioncompany.cors.CORSConfiguration;
import com.thetransactioncompany.cors.CORSConfigurationException;
import com.thetransactioncompany.cors.CORSFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.FileCleanerCleanup;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.Collections;
import java.util.Properties;

/**
 * Created by zhouyou on 2017/12/8.
 */
@Slf4j
@Configuration
public class CFMvcConfigurerAdapter extends WebMvcConfigurerAdapter {

    @Bean
    public SessionKeyValidateInterceptor sessionKeyValidateInterceptor() {
        return new SessionKeyValidateInterceptor();
    }

    @Bean
    public OptionalSessionKeyValidateInterceptor optionalSessionKeyValidateInterceptor() {
        return new OptionalSessionKeyValidateInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionKeyValidateInterceptor())
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/cf/nowpay/**", "/api/wx/**");
        registry.addInterceptor(optionalSessionKeyValidateInterceptor())
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/cf/nowpay/**", "/api/wx/**");
    }

    @Bean
    public ServletListenerRegistrationBean<FileCleanerCleanup> fileCleanerCleanup() {
        return new ServletListenerRegistrationBean<>(new FileCleanerCleanup());
    }

//    @Bean
//    public FilterRegistrationBean logIdFilter() {
//        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogIdFilter());
//        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
//        filterRegistrationBean.setOrder(1);
//        return filterRegistrationBean;
//    }

    @Bean
    public FilterRegistrationBean logRequestFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogRequestFilter());
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean corsFilter() throws CORSConfigurationException {
        Properties prop = new Properties();
        prop.setProperty("cors.maxAge", "3600");
        CORSConfiguration corsConfig = new CORSConfiguration(prop);
        CORSFilter filter = new CORSFilter(corsConfig);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(filter);
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(3);
        return filterRegistrationBean;
    }

//    @Bean
//    public UserIdentityInjectFilter sessionKeyParseFilter(){
//        return new UserIdentityInjectFilter();
//    }

//    @Bean
//    public FilterRegistrationBean sessionKeyParseFilterBean(UserIdentityInjectFilter sessionKeyParseFilter) {
//        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(sessionKeyParseFilter);
//        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
//        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 4);
//        return filterRegistrationBean;
//    }

}
