package com.shuidihuzhu.cf.risk.app.configuration;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;


@Slf4j
@Component
public class RobotKeyConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        HashMap<String, String> map = Maps.newHashMap();
        map.put("34fc0c09-c785-43ce-b450-3aa0894109b9", "7d3289bd-bb6b-4857-bb3e-94c967a83e2a");
        AlarmBotService.setFeishu2WeworkKeyMap(map);
    }

}