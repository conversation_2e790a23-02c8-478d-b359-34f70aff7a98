package com.shuidihuzhu.cf.risk.app.configuration;

import com.shuidihuzhu.common.jdbc.group.GroupDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @author: cuikexiang
 */
@Configuration
public class ReadWriteSeparationDataSourceConfiguration {

    @Resource(name = RiskDS.CF_RISK_DATASOURCE)
    private DataSource riskRiskDatasource;

    @Resource(name = RiskDS.CF_RISK_SLAVE_DATASOURCE)
    private DataSource riskRiskSlaveDatasource;


    @Bean(name = RiskDS.CF_RISK_DATASOURCE_RW)
    public DataSource riskDatasource() {
        return new GroupDataSource(riskRiskSlaveDatasource, riskRiskDatasource);
    }
}
