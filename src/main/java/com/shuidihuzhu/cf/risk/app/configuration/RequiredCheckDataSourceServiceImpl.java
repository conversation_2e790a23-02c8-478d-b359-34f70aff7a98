package com.shuidihuzhu.cf.risk.app.configuration;

import com.google.common.collect.Sets;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.IRequiredCheckDataSourceService;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * @Author: jiangnan
 * @Date: 2019-04-04
 * 健康检查的收缩
 */
@Service
public class RequiredCheckDataSourceServiceImpl implements IRequiredCheckDataSourceService {

    /**
     * 这里面的才是健康检查需要的，其他一律返回up
     *
     * @return
     */
    public static Set<String> requiredDBList() {
        return Sets.newHashSet(RiskDS.CF_RISK_DATASOURCE, RiskDS.CF_RISK_SLAVE_DATASOURCE);
    }

    /**
     * 获取当前系统中直接依赖的reidsson bean NAME
     * <p>
     * 这个方法会会被健康检查用到，只有在set中返回的才会做真正的健康检查
     *
     * @return
     */
    public static Set<String> requiredRedisName() {
        return Sets.newHashSet(RiskDS.CF_RISK_REDIS);
    }


    @Override
    public Set<String> requiredDbDataSource() {
        return requiredDBList();
    }

    @Override
    public Set<String> requiredRedissonDataSource() {
        return requiredRedisName();
    }
}
