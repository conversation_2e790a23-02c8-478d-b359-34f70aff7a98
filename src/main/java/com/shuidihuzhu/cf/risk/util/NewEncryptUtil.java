package com.shuidihuzhu.cf.risk.util;

import com.shuidihuzhu.cipher.OldShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NewEncryptUtil {

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public String encrypt(String str) {
        try {
            return StringUtils.trimToEmpty(oldShuidiCipher.aesEncrypt(str));
        } catch (Exception e) {
            log.error("", e);
        }
        return "";
    }
}
