package com.shuidihuzhu.cf.risk.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class SignUtil {

    private static final String KEY_ALGORITHM = "RSA";
    private static final String ENCODING = "UTF-8";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    /**
     * SHA256WithRSA签名
     */
    public static byte[] sign256(String data, PrivateKey privateKey) throws NoSuchAlgorithmException, InvalidKeyException,
            SignatureException, UnsupportedEncodingException {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKey);
        signature.update(data.getBytes(ENCODING));
        return signature.sign();
    }

    public static boolean verify256(String data, byte[] sign, PublicKey publicKey){
        if(data == null || sign == null || publicKey == null){
            return false;
        }

        try {
            Signature signetcheck = Signature.getInstance(SIGNATURE_ALGORITHM);
            signetcheck.initVerify(publicKey);
            signetcheck.update(data.getBytes(ENCODING));
            return signetcheck.verify(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 二进制数据编码为BASE64字符串
     */
    public static String encodeBase64(byte[] bytes){
        return new String(org.apache.commons.codec.binary.Base64.encodeBase64(bytes));
    }

    /**
     * BASE64解码
     */
    public static byte[] decodeBase64(String encodedStr) {
        byte[] result = null;
        try {
            result = org.apache.commons.codec.binary.Base64.decodeBase64(encodedStr);
        } catch (Exception e) {
            return null;
        }
        return result;
    }

    /**
     * 生产公钥
     */
    public static PublicKey getPublicKey(String key) {
        try {
            byte[] keyBytes;
            keyBytes = Base64.getDecoder().decode(key);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    /**
     * 生产私钥
     */
    public static PrivateKey getPrivateKey(String key) {
        try {
            byte[] keyBytes;
            keyBytes = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }
}