package com.shuidihuzhu.cf.risk.util.okhttp;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/25 10:46
 */
@Validated
@Slf4j
@Component
public class OkHttpCommon {

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * 通过okHttp请求，获取响应内容
     * <AUTHOR>
     * @date 2019/9/3 12:29
     * @param httpParam 请求参数
     * @return HttpResponse 如果responseType没有指定，那么返回的responseBody是String类型，否则，返回指定类型
     */
    public HttpResponse httpRequest(@Valid OkHttpParam httpParam) {
        try {
            log.info("okHttp请求参数:{}", httpParam);
            if (httpParam.getHttpMethod() == HttpMethod.GET) {
                return assembleResponse(get(httpParam.getUrl()));
            }
            if (httpParam.getHttpMethod() == HttpMethod.POST) {
                return assembleResponse(post(httpParam));
            }
        } catch (Exception e) {
            log.error("okHttp请求失败", e);
            return new HttpResponse(false, "", 500, e.getMessage());
        }
        return null;
    }

    public Response get(@NotBlank(message = "url不能为空") String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public Response post(@Valid OkHttpParam okHttpParam) throws IOException {
        String param = okHttpParam.getParam();
        Request.Builder requestBuilder = new Request.Builder().url(okHttpParam.getUrl());
        RequestBody requestBody = null;
        if (okHttpParam.getReqMediaType() == MediaTypeEnum.JSON) {
            requestBody = RequestBody.create(MediaType.parse(MediaTypeEnum.JSON.getMediaInfo()), param);
        }
        if (okHttpParam.getReqMediaType() == MediaTypeEnum.XML) {
            requestBody = RequestBody.create(MediaType.parse(MediaTypeEnum.XML.getMediaInfo()), param);
        }
        if (okHttpParam.getReqMediaType() == MediaTypeEnum.FORM) {
//            requestBody = RequestBody.create(MediaType.parse(MediaTypeEnum.FORM.getMediaInfo()), param);
            List<String> params = Splitter.on("&").splitToList(param);
            FormBody.Builder builder = new FormBody.Builder();
            for (String s : params) {
                List<String> str = Splitter.on("=").splitToList(s);
                builder.add(str.get(0), str.get(1));
            }
            requestBody = builder.build();
        }
        if (requestBody == null) {
            requestBody = RequestBody.create(MediaType.parse(MediaTypeEnum.NONE.getMediaInfo()), param);
        }
        requestBuilder.post(requestBody);

        return okHttpClient.newCall(requestBuilder.build()).execute();
    }

    private HttpResponse assembleResponse(Response response) {
        if (!response.isSuccessful()) {
            log.error("request failed. isSuccessful false");
            return new HttpResponse(false, "", response.code(), response.message());
        }
        try {
            log.info("okHttp请求成功，code:{}", response.code());
            ResponseBody body = response.body();
            if(body == null) {
                log.error("request failed. body is null");
                return new HttpResponse(false, "", response.code(), response.message());
            }
            String bodyString = body.string();
            if (log.isDebugEnabled()) {
                log.debug("okHttp请求结果:{}", bodyString);
            }
            return new HttpResponse(true, bodyString, response.code(), null);
        } catch (Exception e) {
            log.error("okHttp请求失败", e);
            return new HttpResponse(false, "", response.code(), e.getMessage());
        }
    }

}