package com.shuidihuzhu.cf.risk.util.okhttp;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/9/2 17:00
 */
@AllArgsConstructor
@Data
public class HttpResponse {

    /**
     * 是否请求成功
     */
    private boolean success;

    /**
     * 响应结果
     */
    private String responseBody;

    /**
     * http请求响应code
     */
    private int code;

    /**
     * 请求失败信息
     */
    private String errorMsg;

}
