package com.shuidihuzhu.cf.risk.util.third;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.util.okhttp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description: 失信人
 * @Author: panghairui
 * @Date: 2022/8/3 5:14 下午
 */
@Slf4j
@Component
public class ShiXinUtil {

    @Resource
    private OkHttpCommon okHttpCommon;

    private static final String URL = "https://sp0.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php";

    public String okHttpShiXinUrl(String idCard, String name) {
        String maskCard = idCard.replaceAll("(\\w{10})(\\w+)(\\w{4})","$1****$3");
        String url = URL + "?resource_id=6899&query=失信被执行人名单&cardNum=" + maskCard + "&iname=" + name + "&ie=utf-8&oe=utf-8&format=json";
        OkHttpParam okHttpParam = new OkHttpParam(url, HttpMethod.POST, "", MediaTypeEnum.JSON);
        HttpResponse httpResponse = this.okHttpCommon.httpRequest(okHttpParam);
        log.info("ShiXinUtil httpResponse:{}", JSONObject.toJSONString(httpResponse));
        return httpResponse.getResponseBody();
    }

}
