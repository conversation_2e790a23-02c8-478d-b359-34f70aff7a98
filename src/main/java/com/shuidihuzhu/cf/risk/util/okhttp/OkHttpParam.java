package com.shuidihuzhu.cf.risk.util.okhttp;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 回调消息体
 * <AUTHOR>
 * @date 2018/10/8 上午11:50
 */
@NoArgsConstructor
@Data
public class OkHttpParam {

    /**
     * 请求url
     */
    @NotBlank(message = "url不能为空")
    private String url;
    /**
     * 请求方式，目前支持：get post
     */
    @NotNull(message = "httpMethod不能为空")
    private HttpMethod httpMethod;
    /**
     * 回调request body
     */
    private String param;
    /**
     * 参数类型：默认json
     */
    private MediaTypeEnum reqMediaType = MediaTypeEnum.JSON;

    public OkHttpParam(@NotBlank(message = "url不能为空") String url,
                       @NotNull(message = "httpMethod不能为空") HttpMethod httpMethod, String param,
                       MediaTypeEnum reqMediaType) {
        this.url = url;
        this.httpMethod = httpMethod;
        this.param = param;
        this.reqMediaType = reqMediaType;
    }
}
