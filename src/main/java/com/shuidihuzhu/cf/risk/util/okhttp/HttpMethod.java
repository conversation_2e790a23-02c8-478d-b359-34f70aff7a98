package com.shuidihuzhu.cf.risk.util.okhttp;

/**
 * 回调服务支持的method
 * <AUTHOR>
 * @date 2018/10/16 下午4:05
 */
public enum HttpMethod {
    /**
     * method get
     */
    GET,
    /**
     * method post
     */
    POST;

    public static HttpMethod getHttpMethodByOrdinal(int ordinal){
        for(HttpMethod httpMethod : values()){
            if (httpMethod.ordinal() == ordinal) {
                return httpMethod;
            }
        }
        return null;
    }

}
