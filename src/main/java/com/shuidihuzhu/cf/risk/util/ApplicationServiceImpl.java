
package com.shuidihuzhu.cf.risk.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ApplicationServiceImpl implements ApplicationService, EnvironmentAware {
    private static final String ENV_PRODUCTION = "production";
    private Environment environment;

    public ApplicationServiceImpl() {
    }

    public boolean isDevelopment() {
        return !this.isProduction();
    }

    public boolean isProduction() {
        return this.environment.acceptsProfiles(Profiles.of(ENV_PRODUCTION));
    }

    public String get(String production, String develop) {
        return this.isProduction() ? production : develop;
    }

    public String getActiveProfile() {
        String[] profiles = this.environment.getActiveProfiles();
        return StringUtils.join(profiles, ",");
    }

    public void setEnvironment(final Environment environment) {
        this.environment = environment;
    }

    public Environment getEnvironment() {
        return this.environment;
    }
}
