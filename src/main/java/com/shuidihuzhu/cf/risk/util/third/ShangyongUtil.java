package com.shuidihuzhu.cf.risk.util.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.shuidihuzhu.cf.risk.model.enums.UserInComeEnum;
import com.shuidihuzhu.cf.risk.model.vo.UserIncomeThirdVo;
import com.shuidihuzhu.cf.risk.util.SignUtil;
import com.shuidihuzhu.cf.risk.util.okhttp.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-02-13
 **/
@Slf4j
@Component
@RefreshScope
public class ShangyongUtil {

    private static final String CODE = "0041";
    private static final PrivateKey PRIVATE_KEY = SignUtil.getPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2VzxabEN5hG5RE2vetvKA9gG1PGIob9Kan1ROQ5MIA+9hpnHi3eyh/JDp404xsAGW+7hj48F1YMTkSUb50QSt/2rUVEvVoOYQNe3FLZcw2OtwU2MuLLetpfTWRINZ1on4WWpP0qWD2iPtVXzd5pshJPb4Cmm7QlUSUCdpCTqk4yyZb8rHtQ4IO2EnUCysAqmY2+4FizeQZJIF/R3/WtlzZ9rmhwyfs6A0kyU74PAxoaQJoJewbJFdvDyyr9Xvp1pSVXf/sPbQJ0ew8KIJFLyDEzJ/rdQI+9czYGdUwOEGTzSr2Tj8H9sIlLrgey+0jbjyEBE11yUnfzPIbTjjw2nfAgMBAAECggEBAKbc8ig1hxH/NQ0Q3tgzR3zoo+t81L74hhxlzjrI8BaXs0sDjJj2SgR7IwpGr1odHXdT6AvRc0q9QbE/taoLs2KfqN2iPrvCwIb5MxwMep2DBQIH7W1mErjgdExY6nMeN6iD+xwNQtaEm/gnEVvoGvKb5z5aGSP6setOWqJuhbI5fu0jfwy47oPxeCCPE6v0mP3R2AXLWt/VEtYYbPjoLjuAf2pUTxBR46BtaHl9V20nc0Wqi7DzoI5z7AoOeM/WOPUG5Zhxhm50RIuU2IXTbWHC95vGOuAxxakbucujGBKH1PdD/mcuOg+weF4A05a0O2N7BNXspfyUFUubiHObWSECgYEA4J4NXZhOjprklzJ988caI+LflIFko6PX8T8X+ndXbQhICg5D5Jm2FHSO7IOQsfrlaFcqIFIa0KT49FByseWtQ/KTDdUs9DqYXtxN2XWXO1/Fnp8nbHQdQtAxMuLnU4vHNF3cNtGR0N97WewLDDPpoFB/rFPoO3sHGQSL5IXGCicCgYEAz9EQidoBKGwnBh3e4bWnmBkN78cvplvjuFEsK78svOX+oxDQFGlBRTlqzqbsntelZRJEIEr4A31RQpHzO2LtqaHuO46DgK9PvV6FJHlz2AoxsslW0QDnIDY0lE9OIIfXRQpG6jtev21+3eI9k1kh45VNmVat67V1Vs6znQ4cDYkCgYA6TkzB4uthcSutY9n2EdTjQ90tu3jYUYuYf/xFc/0pvGCW26UcDhqJrMf89Rn2WV57e0TgJu/3ARvur4NWUnxriuhJSvDkZOFwnldG/5NGQlIWHtIpAOsnkMt0nhnWQg5vtX2YbUYgpiJq8S7c+AzJJJw/q3g5GHJ7OAboRyFlOQKBgQCAebtDvebhWpt1iJn16BvISBT5m6a1rjFGDcjE8tdxoK6fmRFKzRdw+GIzh7bZLHAv3+8GF9LkNWE7SoeYuzw6lsV1RFHACKG/bOPa/MrFUOgQo7BsXaaFn306Ax1FtzU6lXPp8vJMXiEEg42VvpDY5cCnf5LeGbyFAhzAh6yIIQKBgEBdCuU7LNumNoW+NHSnMjmvt+vwXyn1h+Y2IC09TPjA+j4LTYM6KovH24B92KjlaVe1pPXPARna3N59i+b/eWUh9Klz75pg6J/Oj+WaIunX2lIJXpGHXofQikgMHMHSermT01cXm5vnWbbNKTm2TzqRQHN9rbYgp816ptpDW36y");

    @Autowired
    private OkHttpCommon okHttpCommon;
    @Value("${third.shangyong.portrayal.url:https://rcm.xinjifamily.com/rcm/inq/queryRiskInfo}")
    private String portrayalUrl = "";

    public UserIncomeThirdVo getPortrayalData(String idCard, String mobile, String name, String modelType, String orderNo, String seqNo) {
        try {
            JSONObject personInfo = new JSONObject();
            personInfo.put("cardNo", idCard);
            personInfo.put("name", name);
            if(StringUtils.isNotBlank(mobile)) {
                personInfo.put("mobile", mobile);
            }
            String portrayalDataHttp = getPortrayalDataHttp(personInfo, modelType, orderNo, seqNo);
            UserIncomeThirdVo userIncomeThirdVo = new UserIncomeThirdVo();
            userIncomeThirdVo.setThirdNo("");
            userIncomeThirdVo.setType(UserInComeEnum.ThirdType.SHANGYONG);
            userIncomeThirdVo.setModelType(modelType);
            JSONObject jsonObject = JSON.parseObject(portrayalDataHttp);
            if (jsonObject.containsKey("isSuccess") && "00".equals(jsonObject.getString("isSuccess"))) {
                userIncomeThirdVo.setStatus(UserInComeEnum.ThirdStatus.SUCCESS);
            } else {
                userIncomeThirdVo.setStatus(UserInComeEnum.ThirdStatus.FAILED);
            }
            userIncomeThirdVo.setData(portrayalDataHttp);
            return userIncomeThirdVo;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    private String getPortrayalDataHttp(JSONObject personInfo, String modelType, String orderNo, String seqNo) throws Exception {
        JSONObject policyInfo = new JSONObject();
        policyInfo.put("productName", "水滴筹财富画像");
        policyInfo.put("productCode", "SDCCFHX");
        policyInfo.put("modelType", modelType);
        JSONObject riskReqJson = new JSONObject();
        riskReqJson.put("companyName", "北京水滴互保科技有限公司");
        riskReqJson.put("companyCode", "BJSDHBKJYXGS");
        riskReqJson.put("orderNo", orderNo);
        riskReqJson.put("seqNo", seqNo);
//        riskReq.put("authCode", "0027202001061234567890");
        riskReqJson.put("personInfo", personInfo);
        riskReqJson.put("policyInfo", policyInfo);
        String riskReq = riskReqJson.toJSONString();
        String sign = getSign(ImmutableMap.of("riskReq", riskReq));
        String params = "riskReq=" + riskReq + "&sign=" + sign;
        log.info("params:{}", JSONObject.toJSONString(params));
        OkHttpParam okHttpParam = new OkHttpParam(portrayalUrl, HttpMethod.POST, params, MediaTypeEnum.FORM);
        HttpResponse httpResponse = this.okHttpCommon.httpRequest(okHttpParam);
        log.info("httpResponse:{}", JSONObject.toJSONString(httpResponse));
        return httpResponse.getResponseBody();

    }

    private String getSign(Map<String, String> map) throws Exception {
        ArrayList<String> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (null != entry.getValue() && !"".equals(entry.getValue())) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String[] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < size; i++) {
            sb.append(arrayToSort[i]);
        }
        String result = sb.toString();
        result = result.substring(0, result.length() - 1);
        log.info("result : {}", result);
        result = SignUtil.encodeBase64(SignUtil.sign256(result, PRIVATE_KEY));
        return result;
    }

}
