package com.shuidihuzhu.cf.risk.util.okhttp;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/11/26 18:19
 */
@Component
@Slf4j
@RefreshScope
public class OkHttpMeterInterceptor implements Interceptor {

    private static final int PEEK_READ_RESPONSE_SIZE_BYTE = 1024 * 1024;

    @Value("${rocketmq.group-list.query.url:}")
    private String mqGroupQueryUrl;

    @Override
    public Response intercept(Chain chain) throws IOException {
        long startMilli = System.currentTimeMillis();
        Request request = chain.request();
        String url = request.url().toString();
        Response response;
        try {
            response = chain.proceed(request);
            //上传日志
            printMeter(url, response, startMilli);
        } catch (Exception e) {
            printExpMeter(url, e, startMilli);
            log.info("request header:{}", request.headers());
            log.error("okHttp request failed", e);
            throw e;
        }
        return response;
    }

    private boolean needHandle(String url){
        if (Objects.equals(url, mqGroupQueryUrl)) {
            return false;
        }
        return true;
    }

    private void printMeter(String url, Response response, long startMilli) throws IOException {
        if (!needHandle(url)) {
            return;
        }
        //上传日志
        /*if (response.isSuccessful()) {
            String peekResp = response.peekBody(PEEK_READ_RESPONSE_SIZE_BYTE).string();
            if (HttpCommon.isSuccess(peekResp)) {
                meterUtil.pushMeterCounter(url, HttpRespTypeEnum.OK);
                meterUtil.pushMeterTimer(url, HttpRespTypeEnum.OK, System.currentTimeMillis() - startMilli);
            } else {
                meterUtil.pushMeterCounter(url, HttpRespTypeEnum.HANDLE_FAILED);
                meterUtil.pushMeterTimer(url, HttpRespTypeEnum.HANDLE_FAILED, System.currentTimeMillis() - startMilli);
            }
        } else {
            meterUtil.pushMeterCounter(url, HttpRespTypeEnum.SERVER_EXP);
            meterUtil.pushMeterTimer(url, HttpRespTypeEnum.SERVER_EXP, System.currentTimeMillis() - startMilli);
        }*/
    }

    private void printExpMeter(String url, Exception e, long startMilli) throws IOException {
        if (!needHandle(url)) {
            return;
        }
        //上传日志
        /*if (e instanceof SocketTimeoutException) {
            meterUtil.pushMeterCounter(url, HttpRespTypeEnum.TIMEOUT);
            meterUtil.pushMeterTimer(url, HttpRespTypeEnum.TIMEOUT, System.currentTimeMillis() - startMilli);
        } else {
            meterUtil.pushMeterCounter(url, HttpRespTypeEnum.INNER_ERROR);
            meterUtil.pushMeterTimer(url, HttpRespTypeEnum.INNER_ERROR, System.currentTimeMillis() - startMilli);
        }*/
    }
}
