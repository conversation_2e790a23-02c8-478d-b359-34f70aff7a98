package com.shuidihuzhu.cf.risk.util.okhttp;

import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 回调支持的请求参数媒体类型枚举
 * <AUTHOR>
 * @date 2018/10/9 下午4:35
 */

@Getter
@AllArgsConstructor
public enum MediaTypeEnum {
    /**
     * 无需指定类型，默认的mediaInfo为纯文本
     */
    NONE((byte)-1, "none", "text/plain"),
    /**
     * 普通文本
     */
    FORM((byte)0, "form", "application/x-www-form-urlencoded"),
    /**
     * json格式
     */
    JSON((byte)1, "json", "application/json"),
    /**
     * xml格式
     */
    XML((byte)2, "xml", "application/xml");

    private Byte code;
    @Setter
    private String typeName;
    @Setter
    private String mediaInfo;

    private static ImmutableList<String> typeNames;
    static {
        ImmutableList.Builder builder = ImmutableList.builder();
        for(MediaTypeEnum callbackMediaTypeEnum : values()){
            builder.add(callbackMediaTypeEnum.typeName);
        }
        typeNames = builder.build();
    }

    /**
     * 获取媒体类型名称
     * <AUTHOR>
     * @date 2018/10/9 下午8:31
     * @params []
     * @return com.google.common.collect.ImmutableList<java.lang.String>
     */
    public static ImmutableList<String> getTypeNames(){
        return typeNames;
    }

    public static MediaTypeEnum getEnumByCode(Byte code) {
        for(MediaTypeEnum callbackMediaTypeEnum : values()){
            if(Objects.equals(code, callbackMediaTypeEnum.getCode())){
                return callbackMediaTypeEnum;
            }
        }

        throw new IllegalArgumentException();
    }


}
