package com.shuidihuzhu.cf.risk.util.third;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.model.enums.UserInComeEnum;
import com.shuidihuzhu.cf.risk.model.vo.UserIncomeThirdVo;
import com.shuidihuzhu.cf.risk.util.okhttp.*;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-02-12
 **/
@Slf4j
@Component
@RefreshScope
public class BairongUtil {

    private final static String USER_NAME = "sdcsStr";
    private final static String PASSWORD = "sdcsStr";
    private final static String API_CODE = "3001705";

    @Value("${third.bairong.login.url:https://api.100credit.cn/bankServer2/user/login.action}")
    private String loginUrl = "";
    @Value("${third.bairong.portrayal.url:https://api.100credit.cn/huaxiang/v1/get_report}")
    private String portrayalUrl = "";

    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private OkHttpCommon okHttpCommon;

    public UserIncomeThirdVo getPortrayalData(String idCard, String mobile, String name) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("meal", "CreditModelEva");
            jsonObject.put("id", idCard);
            jsonObject.put("cell", "***********");
            jsonObject.put("name", name);
            String jsonString = jsonObject.toJSONString();
            String tokenId = getToken();
            String responseBody = getPortrayalDataHttp(jsonString, tokenId);
            if (StringUtils.isNotBlank(responseBody)) {
                JSONObject json = JSONObject.parseObject(responseBody);
                if (json.containsKey("code") && "100007".equals(json.getString("code"))) {
                    tokenId = setToken();
                    responseBody = getPortrayalDataHttp(jsonString, tokenId);
                }
            }
            UserIncomeThirdVo userIncomeThirdVo = new UserIncomeThirdVo();
            userIncomeThirdVo.setType(UserInComeEnum.ThirdType.BAIRONG);
            userIncomeThirdVo.setData(responseBody);
            JSONObject json = JSONObject.parseObject(responseBody);
            userIncomeThirdVo.setThirdNo(StringUtils.trimToEmpty(json.getString("swift_number")));
            if (json.containsKey("code") && "00".equals(json.getString("code"))) {
                userIncomeThirdVo.setStatus(UserInComeEnum.ThirdStatus.SUCCESS);
            } else {
                userIncomeThirdVo.setStatus(UserInComeEnum.ThirdStatus.FAILED);
            }
            return userIncomeThirdVo;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    private String getPortrayalDataHttp(String jsonString, String tokenId) {
        String checkCode = MD5Util.getMD5HashValue(jsonString + MD5Util.getMD5HashValue(API_CODE + tokenId));
        String params = "tokenid=" + tokenId + "&apiCode=" + API_CODE + "&jsonData=" + jsonString + "&checkCode=" + checkCode;
        OkHttpParam okHttpParam = new OkHttpParam(portrayalUrl, HttpMethod.POST, params, MediaTypeEnum.FORM);
        HttpResponse httpResponse = this.okHttpCommon.httpRequest(okHttpParam);
        log.info("httpResponse:{}", JSONObject.toJSONString(httpResponse));
        return httpResponse.getResponseBody();
    }

    private String setToken() {
        String token = generateToken();
        this.cfRiskRedissonHandler.setEX("third-bairong-token", token, RedissonHandler.ONE_HOUR);
        return token;
    }

    private String getToken() {
        String token = this.cfRiskRedissonHandler.get("third-bairong-token", String.class);
        if(StringUtils.isNotBlank(token)) {
            return token;
        }
        return this.setToken();
    }

    private String generateToken() {
        String params = "userName=" + USER_NAME + "&password=" + PASSWORD + "&apiCode=" + API_CODE;
        OkHttpParam okHttpParam = new OkHttpParam(loginUrl, HttpMethod.POST, params, MediaTypeEnum.FORM);
        HttpResponse httpResponse = this.okHttpCommon.httpRequest(okHttpParam);
        log.info("httpResponse:{}", JSONObject.toJSONString(httpResponse));
        String responseBody = httpResponse.getResponseBody();
        String token = "";
        try {
            if (StringUtils.isNotBlank(responseBody)) {
                JSONObject loginJson = JSONObject.parseObject(responseBody);
                if (loginJson.containsKey("tokenid")) {
                    token = loginJson.getString("tokenid");
                } else {
                    log.error("返回结果异常，无token!结果为:{}", responseBody);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return token;
    }

}
