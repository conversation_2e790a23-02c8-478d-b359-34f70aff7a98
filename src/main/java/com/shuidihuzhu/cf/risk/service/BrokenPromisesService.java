package com.shuidihuzhu.cf.risk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.dao.TrustPersonelInfoDao;
import com.shuidihuzhu.cf.risk.model.TrustPersonelInfo;
import com.shuidihuzhu.cf.risk.model.enums.TrustInfoThirdTypeEnum;
import com.shuidihuzhu.cf.risk.util.third.ShiXinUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.IdcardValidator;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/8/3 5:38 下午
 */
@Slf4j
@Service
public class BrokenPromisesService {

    private static final int SHORT_CARD_LENGTH = 15;

    @Resource
    private ShiXinUtil shiXinUtil;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private TrustPersonelInfoDao trustPersonelInfoDao;

    public Response<Boolean> isBrokenPromises(String idCard, String name) {

        //15位转18位
        if (StringUtils.length(idCard) == SHORT_CARD_LENGTH) {
            IdcardValidator idcardValidator = new IdcardValidator();
            idCard = idcardValidator.convertIdcarBy15bit(idCard);
        }
        if (IdCardUtil.illegal(idCard) || StringUtils.isBlank(idCard)) {
            return NewResponseUtil.makeSuccess(false);
        }

        JSONArray jsonArray = null;
        try {
            String responseJson = shiXinUtil.okHttpShiXinUrl(idCard, name);
            if (StringUtils.isBlank(responseJson)) {
                return NewResponseUtil.makeSuccess(false);
            }

            JSONObject jsonObject = JSON.parseObject(responseJson);
            if (jsonObject == null || jsonObject.getJSONArray("data") == null) {
                return NewResponseUtil.makeSuccess(false);
            }

            jsonArray = jsonObject.getJSONArray("data");
            idCard = oldShuidiCipher.aesEncrypt(idCard);
            if (jsonArray != null && StringUtils.isNotBlank(idCard)) {
                TrustPersonelInfo info = trustPersonelInfoDao.getByNameAndCard(name, idCard);
                if (Objects.isNull(info)) {
                    trustPersonelInfoDao.addInfo(name, idCard, jsonArray.toJSONString(), TrustInfoThirdTypeEnum.BAIDU.getCode());
                } else {
                    trustPersonelInfoDao.updateInfo(name, idCard, jsonArray.toJSONString());
                }
            }
        } catch (Exception e) {
            log.info("BrokenPromisesService isBrokenPromises ex : {}, {}, {} ", e.getMessage(), idCard, name);
            return NewResponseUtil.makeFail("第三方接口调用失败");
        }

        return NewResponseUtil.makeSuccess(jsonArray != null && !jsonArray.isEmpty());

    }

}
