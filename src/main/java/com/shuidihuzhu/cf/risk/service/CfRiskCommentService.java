package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.risk.model.CfRiskComment;
import com.shuidihuzhu.cf.risk.model.CommentVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
public interface CfRiskCommentService {

    List<CfRiskComment> findByCommentIds(List<Long> commentIdList);

    Response<List<CommentVO>> findByCommentIdsInner(List<Long> commentIdList);
}
