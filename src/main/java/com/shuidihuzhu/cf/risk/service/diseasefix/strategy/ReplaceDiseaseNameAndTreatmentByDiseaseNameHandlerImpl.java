package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplaceDiseaseNameAndTreatmentByDiseaseNameHandlerImpl implements IStrategyHandler {

    @Resource
    private DiseaseDecideAmountService diseaseDecideAmountService;

    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {

        DecideReasonableInfo needFixRequest = diseaseFixContext.getNeedFixRequest();
        String diseaseName = logic.getDiseaseName();
        String diseaseNameForReplace = logic.getDiseaseNameForReplace();


        // 判断是否是该疾病的替换方案
        List<String> diseaseNameList = needFixRequest.getDiseaseNameList();
        if (!diseaseNameList.contains(diseaseName)) {
            return diseaseFixContext;
        }
        List<String> needFixDisease = Lists.newArrayList();
        // 删除归一的疾病名
        for (String name : diseaseNameList) {
            if (name.equals(diseaseName)) {
                needFixDisease.add(diseaseNameForReplace);
                continue;
            }
            needFixDisease.add(name);
        }
        needFixRequest.getDiseaseNameList().removeAll(diseaseNameList);
        needFixRequest.setDiseaseNameList(needFixDisease);

        // 所有疾病治疗方案选项信息
        final Map<String, List<Long>> getAllChoiceMap = diseaseDecideAmountService.getAllChoiceMap(needFixRequest);
        Map<String, List<Long>> fixChoiceMap = Maps.newHashMap();
        fixChoiceMap.putAll(getAllChoiceMap);
        fixChoiceMap.remove(diseaseName);


        // 获取替换疾病id
        RiskDiseaseData diseaseInfo = riskDiseaseDataRpcDao.getByClassName(diseaseNameForReplace);
        if (diseaseInfo == null) {
            log.info("disease:{} is not exist", diseaseName);
            return diseaseFixContext;
        }

        // 获取治疗方案id
        String treatmentNameForReplace = logic.getTreatmentNameForReplace();
        RiskDiseaseTreatmentProject treatmentInfo =
                riskDiseaseTreatmentProjectRpcDao.findByTreatmentNameAndDiseaseId(treatmentNameForReplace, diseaseInfo.getId());
        if (treatmentInfo == null) {
            log.info("treatment:{} is not exist", treatmentNameForReplace);
            return diseaseFixContext;
        }

        fixChoiceMap.put(diseaseNameForReplace, Lists.newArrayList(treatmentInfo.getId()));
        needFixRequest.setGetAllChoiceMap(fixChoiceMap);
        diseaseFixContext.setNeedFixRequest(needFixRequest);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "replaceDiseaseNameAndTreatmentByDiseaseName";
    }
}
