package com.shuidihuzhu.cf.risk.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DiseaseDecideAmountService {

    public Map<String, List<Long>> getAllChoiceMap(DecideReasonableInfo decideReasonableInfo) {
        Map<String, List<Long>> choiceMap = getChoiceMap(decideReasonableInfo.getSpecialDiseaseInfo());
        Map<String, List<Long>> specialRaiseChoiceMap = getChoiceMap(decideReasonableInfo.getSpecialRaiseChoiceInfo());
        if (MapUtils.isNotEmpty(specialRaiseChoiceMap)) {
            choiceMap.putAll(specialRaiseChoiceMap);
        }
        return choiceMap;
    }

    public Map<String, List<Long>> getChoiceMap(String choiceInfo) {
        final List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> choiceInfos = getChoiceList(choiceInfo);
        return getChoiceMap(choiceInfos);
    }

    public List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> getChoiceList(String choiceInfo) {
        return JSONObject.parseArray(choiceInfo, SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
    }

    /**
     * @param specialDiseaseChoiceInfos 选择的特殊信息
     * @return key为疾病名称 value为治疗方案id的Map
     */
    public Map<String, List<Long>> getChoiceMap(
            List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos) {
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return Maps.newHashMap();
        }
        Map<String, List<Long>> choiceMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return choiceMap;
        }
        //特殊可发起治疗方案的展示
        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : specialDiseaseChoiceInfos) {
            List<Long> treatmentProjectIdList = getTreatmentProjectId(specialDiseaseChoiceInfo.getChoiceInfoList());
            choiceMap.put(specialDiseaseChoiceInfo.getSpecialDiseaseName(), treatmentProjectIdList);
        }
        return choiceMap;
    }

    /**
     * 获取需要计算的id
     *
     * @return 疾病对应的治疗方案idList
     */
    private List<Long> getTreatmentProjectId(List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList) {
        if (CollectionUtils.isEmpty(choiceInfoList)) {
            return Lists.newArrayList();
        }
        List<Long> treatmentProjectIds = Lists.newArrayList();
        for (SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo : choiceInfoList) {
            if (choiceInfo.isRealCalculateChoice()) {
                treatmentProjectIds.add(choiceInfo.getChoiceId());
            }
            if (CollectionUtils.isEmpty(choiceInfo.getChoiceInfoList())) {
                continue;
            }
            treatmentProjectIds.addAll(getTreatmentProjectId(choiceInfo.getChoiceInfoList()));
        }
        return treatmentProjectIds;
    }

    /**
     *
     * @param allChoiceList        用户选择的信息
     * @param filterDiseaseNameList         需要查询的疾病类型
     * @return 需要处理的特殊格式
     */
    public List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo>  filterSpecialChoice(
            List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> allChoiceList,
            final List<String> filterDiseaseNameList) {
        if (CollectionUtils.isEmpty(allChoiceList)) {
            return Lists.newArrayList();
        }
        return allChoiceList.stream().filter(v -> filterDiseaseNameList.contains(v.getSpecialDiseaseName()))
                .collect(Collectors.toList());
    }

    public Map<String, List<Long>> getAllChoiceMapByContext(DiseaseDecideContext diseaseDecideContext) {
        if (diseaseDecideContext.getAllChoiceMap() == null) {
            diseaseDecideContext.setAllChoiceMap(getAllChoiceMap(diseaseDecideContext.getDecideReasonableInfo()));
        }
        return diseaseDecideContext.getAllChoiceMap();
    }
}
