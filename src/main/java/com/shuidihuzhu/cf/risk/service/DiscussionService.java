package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.risk.model.DiscussionDTO;
import com.shuidihuzhu.cf.risk.model.DiscussionVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

public interface DiscussionService {

    Response<List<DiscussionVO>> findDiscussionInfoByCaseId(List<Integer> caseIdList);

    DiscussionDTO findByCaseId(int caseId);

    Boolean hasDiscussion(int caseId);
}
