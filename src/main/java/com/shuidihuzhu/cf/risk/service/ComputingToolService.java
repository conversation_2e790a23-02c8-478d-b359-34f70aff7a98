package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.risk.ComputingRecord;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ComputingToolService {

    Response<ComputingToolResult> getResultAndSubmit(String extName, long extId, long operatorId, String submitJson);

    ComputingToolRecordDO getRecordById(String extName, long extId, int recordId);

    Response<List<ComputingRecord>> getRecordList(String extName, long extId);
}
