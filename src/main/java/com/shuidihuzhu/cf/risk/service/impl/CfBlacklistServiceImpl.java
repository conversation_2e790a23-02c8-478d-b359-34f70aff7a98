package com.shuidihuzhu.cf.risk.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.risk.cache.blacklist.BlacklistContent;
import com.shuidihuzhu.cf.risk.cache.blacklist.CfBlackListCacheService;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataActionRefDao;
import com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataDao;
import com.shuidihuzhu.cf.risk.dao.hit.RiskStrategyHitRecordDao;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cf.risk.model.enums.BooleanEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataActionRef;
import com.shuidihuzhu.cf.risk.model.po.blacklist.query.BlacklistDataQuery;
import com.shuidihuzhu.cf.risk.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.model.risk.*;
import com.shuidihuzhu.cf.risk.service.ICfBlacklistService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/9/23 下午8:49
 * @desc
 */
@Slf4j
@Service
public class CfBlacklistServiceImpl implements ICfBlacklistService {

    @Resource
    private RiskStrategyHitRecordDao riskStrategyHitRecordDao;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CfBlackListCacheService cfBlackListCacheService;
    @Resource
    private Producer producer;
    @Resource
    private RiskBlacklistDataDao blacklistDataDao;
    @Resource
    private RiskBlacklistDataActionRefDao dataActionRefDao;
    @Resource
    private UserInfoServiceClient userInfoServiceClient;
    @Resource
    private CfFirstApproveClient cfFirstApproveClient;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Override
    public void checkHit(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        if (CollectionUtils.isEmpty(blacklistVerifyDtos)) {
            return;
        }

        doCheckHit(blacklistVerifyDtos);
    }

    @Override
    public void checkHitV2(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        if (CollectionUtils.isEmpty(blacklistVerifyDtos)) {
            return;
        }

        doCheckHitV2(blacklistVerifyDtos);
    }

    @Override
    public List<BlacklistVerifyDto> checkFriendReport(BlacklistReportDto blacklistReportDto, Predicate<Long> limitActionFilter) {
        List<BlacklistVerifyDto> verifyDtos = Lists.newArrayListWithCapacity(2);
        verifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                BlacklistVerifyTypeEnum.USER_ID.getCode(), String.valueOf(blacklistReportDto.getReportUserId())));
        verifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.REPORTER.getCode(),
                BlacklistVerifyTypeEnum.USER.getCode(), blacklistReportDto.getReporterName()));
        Optional.ofNullable(blacklistReportDto.getReportMobile()).filter(StringUtils::isNotBlank).ifPresent(mobile -> {
            verifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.REPORTER.getCode(),
                    BlacklistVerifyTypeEnum.MOBILE.getCode(), mobile));
        });
        Optional.ofNullable(blacklistReportDto.getIdentity()).filter(StringUtils::isNotBlank).ifPresent(mobile -> {
            BlacklistVerifyDto blacklistVerifyDto = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.REPORTER.getCode(),
                    BlacklistVerifyTypeEnum.ID_CARD.getCode(), "");
            blacklistVerifyDto.setVerifyEncryptData(blacklistReportDto.getIdentity());
            verifyDtos.add(blacklistVerifyDto);
        });

        //补充发起人账号系统手机号
        if (blacklistReportDto.getReportUserId() > 0) {
            try {
                UserInfoModel userInfoModel = userInfoServiceClient.getUserInfoByUserId(blacklistReportDto.getReportUserId());
                log.info("call account feign resp:{}", userInfoModel);
                Optional.ofNullable(userInfoModel).map(UserInfoModel::getMobile).filter(StringUtils::isNotBlank).ifPresent(
                        mobile-> verifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                                BlacklistVerifyTypeEnum.MOBILE.getCode(), mobile))
                );
            } catch (Exception e) {
                log.error("", e);
            }
        }

        doCheckHit(verifyDtos);

        return verifyDtos.stream().peek(blacklistVerifyDto -> {
            if (blacklistVerifyDto.isHit()) {
                List<Long> hitReportActions = blacklistVerifyDto.getLimitActionIds()
                        .stream().filter(limitActionFilter).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hitReportActions)) {
                    blacklistVerifyDto.setLimitActionIds(hitReportActions);
                } else {
                    blacklistVerifyDto.resetResult();
                }
            }
        }).collect(Collectors.toList());
    }

    private void doCheckHit(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        Map<Integer, Map<String, BlacklistContent>> cacheAll = cfBlackListCacheService.getCacheAll();
        blacklistVerifyDtos.parallelStream().forEach(blacklistVerifyDto -> {
            Map<String, BlacklistContent> contentMap = cacheAll.get(blacklistVerifyDto.getVerifyType());
            if (MapUtils.isNotEmpty(contentMap)) {
                String tempData = blacklistVerifyDto.getVerifyData();
                if (BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())) {
                    tempData = oldShuidiCipher.aesEncrypt(tempData);
                }
                BlacklistContent content = contentMap.get(tempData);
                if (content != null) {
                    blacklistVerifyDto.setHit(true);
                    blacklistVerifyDto.setLimitActionIds(content.getActions());
                    blacklistVerifyDto.setTypeIds(content.getTypeIds());
                    blacklistVerifyDto.setUserName(content.getUserName());
                    blacklistVerifyDto.setBlacknessReason(content.getBlacknessReason());
                }
            }
        });
    }

    private void doCheckHitV2(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        Map<Integer, Map<String, BlacklistContent>> cacheAll = cfBlackListCacheService.getCacheAll();
        blacklistVerifyDtos.parallelStream().forEach(blacklistVerifyDto -> {
            Map<String, BlacklistContent> contentMap = cacheAll.get(blacklistVerifyDto.getVerifyType());
            if (MapUtils.isEmpty(contentMap)){
                log.info("contentMap is empty, blacklistVerifyDto is:{}", blacklistVerifyDto);
                return;
            }
            if(StringUtils.isAllBlank(blacklistVerifyDto.getVerifyData(), blacklistVerifyDto.getVerifyEncryptData())){
                log.info("verifyData and verifyEncryptData is blank, blacklistVerifyDto is:{}", blacklistVerifyDto);
                return;
            }
            String tempData = blacklistVerifyDto.getVerifyData();
            if (BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())) {
                tempData = StringUtils.isNotBlank(blacklistVerifyDto.getVerifyEncryptData()) ?
                        blacklistVerifyDto.getVerifyEncryptData() : oldShuidiCipher.aesEncrypt(tempData);
            }
            BlacklistContent content = contentMap.get(tempData);
            if (content != null) {
                blacklistVerifyDto.setHit(true);
                blacklistVerifyDto.setLimitActionIds(content.getActions());
                blacklistVerifyDto.setTypeIds(content.getTypeIds());
                blacklistVerifyDto.setUserName(content.getUserName());
                blacklistVerifyDto.setBlacknessReason(content.getBlacknessReason());
            }
        });
    }

    @Override
    public boolean checkPreTrialAdoption(BlacklistPreTrialAdoptionDto blacklistExtendDto) {
        List<BlacklistVerifyDto> blacklistVerifyDtos = Lists.newArrayListWithCapacity(5);
        if(StringUtils.isNotBlank(blacklistExtendDto.getInitiatorMobile())) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                    BlacklistVerifyTypeEnum.MOBILE.getCode(), blacklistExtendDto.getInitiatorMobile()));
        }
        blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                        BlacklistVerifyTypeEnum.USER_ID.getCode(), blacklistExtendDto.getUserId()+""));
        blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                BlacklistVerifyTypeEnum.ID_CARD.getCode(), blacklistExtendDto.getInitiatorIdCard()));
        int verifyType = blacklistExtendDto.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode()
                ? BlacklistVerifyTypeEnum.ID_CARD.getCode()
                : BlacklistVerifyTypeEnum.BORN_CARD.getCode();
        blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),
                verifyType, blacklistExtendDto.getPatientIdNumber())
        );
        //与业务方之前的验证保持一致，如果是出生证号则验证与黑名单的身份证号&出生证号是否匹配
        if (blacklistExtendDto.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.BIRTH_CERTIFICATE.getCode()) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),
                    BlacklistVerifyTypeEnum.ID_CARD.getCode(), blacklistExtendDto.getPatientIdNumber()));
        }

        doCheckHit(blacklistVerifyDtos);

        return decideHitAndSendMQ(blacklistExtendDto, verifyType, blacklistVerifyDtos, LimitActionEnum.PRE_TRIAL_PASS);
    }

    @Override
    public boolean checkPayeeLimit(BlacklistPayeeLimitDto payeeLimitDto) {
        List<BlacklistVerifyDto> blacklistVerifyDtos = Lists.newArrayList();
        blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PAYEE.getCode(),
                BlacklistVerifyTypeEnum.ID_CARD.getCode(), payeeLimitDto.getPayeeIdCard()));
        //目前有部分接口无法提供手机号
        if (StringUtils.isNotBlank(payeeLimitDto.getPayeeMobile())) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PAYEE.getCode(),
                    BlacklistVerifyTypeEnum.MOBILE.getCode(), payeeLimitDto.getPayeeMobile()));
        }

        doCheckHit(blacklistVerifyDtos);

        return decideHitAndSendMQ(payeeLimitDto, null, blacklistVerifyDtos, LimitActionEnum.PAYEE_PASS);
    }

    @Override
    public boolean checkLaunchCase(BlacklistLunchCaseDto lunchCaseDto) {
        List<BlacklistVerifyDto> blacklistVerifyDtos = Lists.newArrayListWithCapacity(5);
        if (StringUtils.isNotBlank(lunchCaseDto.getInitiatorMobile())) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                    BlacklistVerifyTypeEnum.MOBILE.getCode(), lunchCaseDto.getInitiatorMobile()));
        }
        if (lunchCaseDto.getUserId() > 0) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                    BlacklistVerifyTypeEnum.USER_ID.getCode(), lunchCaseDto.getUserId()+""));
        }
        if (StringUtils.isNotBlank(lunchCaseDto.getInitiatorIdCard())) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(),
                    BlacklistVerifyTypeEnum.ID_CARD.getCode(), lunchCaseDto.getInitiatorIdCard()));
        }
        Integer verifyType = null;
        if (StringUtils.isNotBlank(lunchCaseDto.getPatientIdNumber())) {
            verifyType = lunchCaseDto.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode()
                    ? BlacklistVerifyTypeEnum.ID_CARD.getCode()
                    : BlacklistVerifyTypeEnum.BORN_CARD.getCode();
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),
                    verifyType, lunchCaseDto.getPatientIdNumber()));
        }
        //与业务方之前的验证保持一致，如果是出生证号则验证与黑名单的身份证号&出生证号是否匹配
        if (lunchCaseDto.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.BIRTH_CERTIFICATE.getCode()) {
            blacklistVerifyDtos.add(new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),
                    BlacklistVerifyTypeEnum.ID_CARD.getCode(), lunchCaseDto.getPatientIdNumber()));
        }

        doCheckHit(blacklistVerifyDtos);

        if (blacklistVerifyDtos.stream().anyMatch(blacklistVerifyDto -> blacklistVerifyDto.isHit() &&
                blacklistVerifyDto.getLimitActionIds().contains(LimitActionEnum.FUNDRAISING.getId()))) {
            blacklistVerifyDtos = reorganizeVerifyDate(verifyType, blacklistVerifyDtos, LimitActionEnum.FUNDRAISING);
            producer.send(Message.of(CfRiskMQTopicCons.CF_RISK_TOPIC, CfRiskMQTagCons.BLACKLIST_HIT, UUID.randomUUID().toString(),
                    new BlacklistExtendDto(lunchCaseDto.getCaseId(), lunchCaseDto.getCaseInitiateTime(),
                            lunchCaseDto.getBlacklistCallPhase(), blacklistVerifyDtos, true)));
            return true;
        }

        return false;
    }

    private boolean decideHitAndSendMQ(BlacklistVerifyBaseDto baseDto, Integer casePatientCardType,
                                       List<BlacklistVerifyDto> blacklistVerifyDtos, LimitActionEnum limitActionEnum) {
        if (blacklistVerifyDtos.stream().anyMatch(blacklistVerifyDto -> blacklistVerifyDto.isHit() &&
                blacklistVerifyDto.getLimitActionIds().contains(limitActionEnum.getId()))) {
            blacklistVerifyDtos = reorganizeVerifyDate(casePatientCardType, blacklistVerifyDtos, limitActionEnum);
            List<RiskStrategyHitRecord> hitRecords = riskStrategyHitRecordDao.listByCaseIdPhase(baseDto.getCaseId(),
                    baseDto.getBlacklistCallPhase());
            List<String> newList = blacklistVerifyDtos.stream().filter(blacklistVerifyDto -> blacklistVerifyDto.isHit() &&
                    blacklistVerifyDto.getLimitActionIds().contains(limitActionEnum.getId()))
                    .map(blacklistVerifyDto -> RiskStrategyEnum.BLACKLIST.getCode() + "_" + RiskStrategySecondEnum.DEFAULT.getCode() +
                            "_" + blacklistVerifyDto.getVerifyRole() + "_" + blacklistVerifyDto.getVerifyType() +
                            "_" + (BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())
                            ? oldShuidiCipher.aesEncrypt(blacklistVerifyDto.getVerifyData())
                            : blacklistVerifyDto.getVerifyData()))
                    .collect(Collectors.toList());
            List<String> oldList = hitRecords.stream()
                    .filter(hitRecord -> hitRecord.getLifting() == BooleanEnum.TRUE.value())
                    .flatMap(hitRecord -> JSON.parseArray(hitRecord.getHitInfo(), BlacklistVerifyDto.class).stream()
                                .map(blacklistVerifyDto -> hitRecord.getRiskStrategy() + "_" + hitRecord.getSecondStrategy() +
                                "_" + blacklistVerifyDto.getVerifyRole() + "_" + blacklistVerifyDto.getVerifyType() +
                                "_" + blacklistVerifyDto.getVerifyData()))
                    .collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("newList:{}, oldList:{}", newList, oldList);
            }
            boolean unlock = CollectionUtils.subtract(newList,oldList).size() == 0;
            if (CollectionUtils.isEmpty(hitRecords) || !unlock) {
                //发送通知
                producer.send(Message.of(CfRiskMQTopicCons.CF_RISK_TOPIC, CfRiskMQTagCons.BLACKLIST_HIT, UUID.randomUUID().toString(),
                        new BlacklistExtendDto(baseDto.getCaseId(), baseDto.getCaseInitiateTime(),
                                baseDto.getBlacklistCallPhase(), blacklistVerifyDtos, true)));
                return true;
            }
            log.info("case id {}, sensitive info has been unlocked altogether.", baseDto.getCaseId());
        }

        return false;
    }

    private List<BlacklistVerifyDto> reorganizeVerifyDate(Integer casePatientCardType, List<BlacklistVerifyDto> blacklistVerifyDtos,
                                                          LimitActionEnum limitActionEnum){
        List<BlacklistVerifyDto> verifyDtos = Lists.newArrayListWithCapacity(blacklistVerifyDtos.size());
        BlacklistVerifyDto patientCardDto = null;
        for (BlacklistVerifyDto blacklistVerifyDto : blacklistVerifyDtos) {
            //重新设置命中情况
            if (blacklistVerifyDto.isHit() && blacklistVerifyDto.getLimitActionIds().contains(limitActionEnum.getId())) {
                blacklistVerifyDto.setLimitActionIds(List.of(limitActionEnum.getId()));
            } else {
                blacklistVerifyDto.resetResult();
            }
            //处理患者出生证号两次匹配的重复数据
            if (Objects.equals(casePatientCardType, BlacklistVerifyTypeEnum.BORN_CARD.getCode()) &&
                    blacklistVerifyDto.getVerifyRole() == BlacklistVerifyRoleEnum.PATIENT.getCode() &&
                    (blacklistVerifyDto.getVerifyType() == BlacklistVerifyTypeEnum.ID_CARD.getCode() ||
                            blacklistVerifyDto.getVerifyType() == BlacklistVerifyTypeEnum.BORN_CARD.getCode())) {
                if (patientCardDto == null || blacklistVerifyDto.isHit()) {
                    patientCardDto = blacklistVerifyDto;
                    patientCardDto.setVerifyType(casePatientCardType);
                }
                continue;
            }
            verifyDtos.add(blacklistVerifyDto);
        }

        if (patientCardDto != null) {
            verifyDtos.add(patientCardDto);
        }

        return verifyDtos;
    }

    @Override
    public boolean isBlackValid(long userId, long actionId) {
        List<RiskBlacklistData> blacklistDataList = blacklistDataDao.getByUserIdOrUserIdBind(userId);
        if (CollectionUtils.isEmpty(blacklistDataList)) {
            return false;
        }
        RiskBlacklistDataActionRef ref = dataActionRefDao.listByDataIdAndActionId(
                blacklistDataList.stream().map(RiskBlacklistData::getId).collect(Collectors.toList()), actionId);
        return ref != null;
    }

    @Override
    public BlacklistHitHighRiskDto isBlackByHighRisk(int caseId) {
        BlacklistHitHighRiskDto blacklistHitHighRiskDto = new BlacklistHitHighRiskDto();
        RpcResult<CfFirsApproveMaterial> cfFirsApproveMaterialRpcResult = cfFirstApproveClient.selectFirstApproveByCaseId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(cfFirsApproveMaterialRpcResult)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            return blacklistHitHighRiskDto;
        }
        BlacklistDataQuery blacklistDataQuery = new BlacklistDataQuery();
        if (cfFirsApproveMaterial.getPatientIdType() == UserIdentityType.identity.getCode()) {
            blacklistDataQuery.setIdCard(cfFirsApproveMaterial.getPatientCryptoIdcard());
        } else {
            blacklistDataQuery.setIdCard("");
            blacklistDataQuery.setBornCard(oldShuidiCipher.aesEncrypt(cfFirsApproveMaterial.getPatientBornCard()));
        }
        List<RiskBlacklistData> riskBlacklistData = blacklistDataDao.listByQuery(blacklistDataQuery);
        if (CollectionUtils.isEmpty(riskBlacklistData)) {
            return blacklistHitHighRiskDto;
        }
        RiskBlacklistDataActionRef ref = dataActionRefDao.listByDataIdAndActionId(
                riskBlacklistData.stream()
                        .map(RiskBlacklistData::getId)
                        .collect(Collectors.toList()), LimitActionEnum.HIGH_WORK_ORDER.getId());
        if (Objects.nonNull(ref)) {
            blacklistHitHighRiskDto.setHitType(StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientBornCard()) ?
                    1 : 2);
            blacklistHitHighRiskDto.setHitIdCard(StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientBornCard()) ?
                    cfFirsApproveMaterial.getPatientBornCard() : cfFirsApproveMaterial.getPatientCryptoIdcard());
            blacklistHitHighRiskDto.setBlackListId(ref.getDataId());
            return blacklistHitHighRiskDto;
        }
        return blacklistHitHighRiskDto;
    }

}
