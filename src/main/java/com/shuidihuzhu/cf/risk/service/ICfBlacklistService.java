package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cf.risk.model.risk.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @time 2019/9/23 下午8:48
 * @desc
 */
@Validated
public interface ICfBlacklistService {

    void checkHit(@Valid List<BlacklistVerifyDto> blacklistVerifyDtos);

    void checkHitV2(List<BlacklistVerifyDto> blacklistVerifyDtos);

    /**
     * 检测友商举报
     * @param blacklistReportDto
     */
    List<BlacklistVerifyDto> checkFriendReport(@Valid BlacklistReportDto blacklistReportDto, Predicate<Long> limitActionFilter);

    boolean checkPreTrialAdoption(BlacklistPreTrialAdoptionDto preTrialAdoptionDto);

    boolean checkPayeeLimit(@Valid BlacklistPayeeLimitDto payeeLimitDto);

    boolean checkLaunchCase(@Valid BlacklistLunchCaseDto lunchCaseDto);

    boolean isBlackValid(long userId, long actionId);

    BlacklistHitHighRiskDto isBlackByHighRisk(int caseId);
}
