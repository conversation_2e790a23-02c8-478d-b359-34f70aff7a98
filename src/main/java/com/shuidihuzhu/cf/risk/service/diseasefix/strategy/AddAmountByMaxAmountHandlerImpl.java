package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.service.diseasefix.CompareSymbols;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
@Service
public class AddAmountByMaxAmountHandlerImpl implements IStrategyHandler{

    private final Long AMOUNT_CHANGE = 10000L;


    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {
        InfoReasonableAmountResultVo resourceResult = diseaseFixContext.getResourceContext().getResult();

        if(logic.getMaxAmountInYuan() <= 0 || StringUtils.isEmpty(logic.getCondition())){
            return diseaseFixContext;
        }

        if(CompareSymbols.MORE_THAN.equals(logic.getCondition())){
            if(resourceResult.getAdviseMaxAmount() * AMOUNT_CHANGE > logic.getMaxAmountInYuan()){
                diseaseFixContext.setAmountForPlusInWanYuan(logic.getAddAmountInYuan() * 1.0 / AMOUNT_CHANGE);
            }
        }

        if(CompareSymbols.LESS_THAN_OR_EQUAL.equals(logic.getCondition())){
            if(resourceResult.getAdviseMaxAmount() * AMOUNT_CHANGE <= logic.getMaxAmountInYuan()){
                diseaseFixContext.setAmountForPlusInWanYuan(logic.getAddAmountInYuan() * 1.0 / AMOUNT_CHANGE);
            }
        }
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "addAmountByMaxAmount";
    }
}
