package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.enhancer.subject.query.EhCursorQuery;
import com.shuidihuzhu.cf.risk.biz.ReportHitStrategyRecordBiz;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.jdbc.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-04-01
 **/
@Slf4j
@Service
public class CfReportService {

    @Autowired
    private ReportHitStrategyRecordBiz reportHitStrategyRecordBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public List<ReportHitStrategyRecord> getStrategyRecord(List<Integer> reportIds) {
        return reportHitStrategyRecordBiz.getByReportIds(reportIds);
    }

    public int addStrategyRecord(List<ReportHitStrategyRecord> reportHitStrategyRecords) {
        return reportHitStrategyRecordBiz.batchInsert(reportHitStrategyRecords);
    }

    public void washData(int beginId){
        EhCursorQuery.queryByCursorWithConsumer(beginId, (id1, id2) ->
                        reportHitStrategyRecordBiz.listByCursor(id1.longValue(), id2), ReportHitStrategyRecord::getId,
                item -> {
                    if (CollectionUtils.isEmpty(item)) {
                        return;
                    }
                    for (ReportHitStrategyRecord record : item) {
                        if(record == null){
                            continue;
                        }
                        String mobile = StringUtils.isBlank(record.getMobile()) ? "" : oldShuidiCipher.aesEncrypt(record.getMobile());
                        String idCard = StringUtils.isBlank(record.getIdCard()) ? "" : oldShuidiCipher.aesEncrypt(record.getIdCard());
                        String reportMobile = StringUtils.isBlank(record.getReportMobile()) ? "" : oldShuidiCipher.aesEncrypt(record.getReportMobile());
                        reportHitStrategyRecordBiz.updateEncryptById(record.getId(), mobile, reportMobile, idCard);
                    }
                });
    }

    public void washDataV2(int beginId){
        EhCursorQuery.queryByCursorWithConsumer(beginId, (id1, id2) ->
                        reportHitStrategyRecordBiz.listByCursor(id1.longValue(), id2), ReportHitStrategyRecord::getId,
                item -> {
                    if (CollectionUtils.isEmpty(item)) {
                        return;
                    }
                    for (ReportHitStrategyRecord record : item) {
                        if(record == null){
                            continue;
                        }
                        reportHitStrategyRecordBiz.updateMobileById(record.getId(), "", "", "");
                    }
                });
    }
}
