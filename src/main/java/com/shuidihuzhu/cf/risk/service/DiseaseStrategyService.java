package com.shuidihuzhu.cf.risk.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyLogBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyRelBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategy;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyRel;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Service
@Slf4j
public class DiseaseStrategyService {


    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Autowired
    private RiskExecuteStrategyLogBiz executeStrategyLogBiz;
    @Autowired
    private RiskExecuteStrategyBiz riskExecuteStrategyBiz;
    @Autowired
    private RiskExecuteStrategyRelBiz riskExecuteStrategyRelBiz;
    @Autowired
    private List<ExecuteStrategyModelTemplate> executeStrategyModelTemplates;
    @Autowired
    private SeaAccountService seaAccountService;

    private static Map<String, ExecuteStrategyModelTemplate> strategyModelMap;

    //疾病测录判断初始条件
    private static Map<Integer, Set<Integer>> raiseMapList =
            ImmutableMap.of(
                    DiseaseStrategyEnum.USER_WRITE.getCode(), Sets.newHashSet(
                            DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode(),
                            DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode()),
                    DiseaseStrategyEnum.MANUAL_WRITE.getCode(), Sets.newHashSet(
                            DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode(),
                            DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode()),
                    DiseaseStrategyEnum.OCR.getCode(), Sets.newHashSet(
                            DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode(),
                            DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode()));


    @PostConstruct
    private void init(){
        //初始化所有的策略
        strategyModelMap = executeStrategyModelTemplates.stream()
                .collect(Collectors.toMap(ExecuteStrategyModelTemplate::getCode, Function.identity()));
    }

    public Response<DiseaseStrategyResponse> diseaseStrategy(DiseaseStrategyRequest diseaseStrategyRequest) {
        //判断来源 如果是手工录入需要set疾病list
        setDiseaseName(diseaseStrategyRequest);
        //进行trim处理
        if (CollectionUtils.isNotEmpty(diseaseStrategyRequest.getDiseaseNameList())){
            diseaseStrategyRequest.setDiseaseNameList(diseaseStrategyRequest.getDiseaseNameList()
                    .stream().map(StringUtils::trim).collect(Collectors.toList()));
        }
        //执行当前的策略
        DiseaseStrategyResponse diseaseStrategyResponse = new DiseaseStrategyResponse();
        doStrategy(diseaseStrategyRequest, diseaseStrategyResponse, new RiskExecuteStrategyTransit());
        //dealSort
        sortDiseaseNameList(diseaseStrategyRequest, diseaseStrategyResponse);
        return NewResponseUtil.makeSuccess(diseaseStrategyResponse);
    }

    /**
     * 返回的目录重排序
     * @param diseaseStrategyRequest
     * @param diseaseStrategyResponse
     */
    private void sortDiseaseNameList(DiseaseStrategyRequest diseaseStrategyRequest, DiseaseStrategyResponse diseaseStrategyResponse) {
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList())) {
            return;
        }
        Map<String, DiseaseInfoVo> diseaseInfoVoMap = diseaseStrategyResponse.getDiseaseInfoList()
                .stream().collect(Collectors.toMap(DiseaseInfoVo::getDiseaseName, Function.identity()));
        List<DiseaseInfoVo> diseaseInfoVos = Lists.newArrayList();
        for (String s : diseaseStrategyRequest.getDiseaseNameList()) {
            DiseaseInfoVo diseaseInfoVo = diseaseInfoVoMap.get(s);
            if (diseaseInfoVo == null) {
                diseaseInfoVo = new DiseaseInfoVo();
                diseaseInfoVo.setDiseaseName(s);
            }
            diseaseInfoVos.add(diseaseInfoVo);
        }
        diseaseStrategyResponse.setDiseaseInfoList(diseaseInfoVos);
    }

    private void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                            DiseaseStrategyResponse diseaseStrategyResponse,
                            RiskExecuteStrategyTransit transit) {
        //开始执行目标金额合理性策略
        RiskExecuteStrategy riskExecuteStrategy = riskExecuteStrategyBiz.getByType(diseaseStrategyRequest.getExecuteStrategyEnum());
        if (riskExecuteStrategy == null) {
            log.error("riskExecuteStrategy is null, type:{}", diseaseStrategyRequest.getExecuteStrategyEnum());
            return ;
        }
        List<RiskExecuteStrategyRel> riskExecuteStrategyRels = getRiskExecuteStrategyRelList(riskExecuteStrategy.getId());
        if (CollectionUtils.isEmpty(riskExecuteStrategyRels)) {
            return;
        }
        int indexSort = getIndexSort(riskExecuteStrategyRels, diseaseStrategyRequest) ;
        //按顺序执行策略
        for (RiskExecuteStrategyRel riskExecuteStrategyRel : riskExecuteStrategyRels) {
            if (indexSort > 0 && riskExecuteStrategyRel.getSort() < indexSort) {
                continue;
            }
            //特殊判断  如果有选择项  直接跳过之前的
            ExecuteStrategyModelTemplate executeModel  = strategyModelMap.get(riskExecuteStrategyRel.getModelCode());
            if (executeModel == null){
                log.error("model is null, code:{}", riskExecuteStrategyRel.getModelCode());
               return;
            }
            executeModel.doStrategy(diseaseStrategyRequest, diseaseStrategyResponse, transit);
            if (!transit.isHaNext()) {
                //终止时保存结果
                saveResultLog(diseaseStrategyRequest, diseaseStrategyResponse, transit, riskExecuteStrategy);
                break;
            }
        }

        if (isRaiseResult(diseaseStrategyResponse.getDiseaseRaiseStrategyResult(), diseaseStrategyRequest)) {
            diseaseStrategyRequest.setExecuteStrategyEnum(DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode());
            transit.setHaNext(true);
            doStrategy(diseaseStrategyRequest, diseaseStrategyResponse, transit);
        }
    }

    @Nullable
    private List<RiskExecuteStrategyRel> getRiskExecuteStrategyRelList(long strategyId) {
        //获取策略
        List<RiskExecuteStrategyRel> riskExecuteStrategyRels = riskExecuteStrategyRelBiz.getByStrategyId(strategyId);
        if (CollectionUtils.isEmpty(riskExecuteStrategyRels)) {
            return Lists.newArrayList();
        }
        //排序
        riskExecuteStrategyRels.sort(Comparator.comparing(RiskExecuteStrategyRel::getSort));
        return riskExecuteStrategyRels;
    }

    private int getIndexSort(List<RiskExecuteStrategyRel> riskExecuteStrategyRels,
                             DiseaseStrategyRequest diseaseStrategyRequest) {
        Map<String, Integer> sortMap = riskExecuteStrategyRels.stream()
                .collect(Collectors.toMap(RiskExecuteStrategyRel::getModelCode, RiskExecuteStrategyRel::getSort,
                        (a, b) -> b));
        String wantKey = getWantKey(diseaseStrategyRequest.getExecuteStrategyEnum(),
                diseaseStrategyRequest.getSpecialRaiseChoiceInfo(),
                diseaseStrategyRequest.getSpecialDiseaseInfo());
        if (StringUtils.isBlank(wantKey)){
            return 0;
        }
        return sortMap.get(wantKey);
    }

    private String getWantKey(int executeStrategyEnum,
                              String specialRaiseChoiceInfo,
                              String specialDiseaseInfo) {
        if (StringUtils.isBlank(specialRaiseChoiceInfo) && StringUtils.isBlank(specialDiseaseInfo)) {
            return "";
        }
        DiseaseStrategyEnum strategyEnum = DiseaseStrategyEnum.findByCode(executeStrategyEnum);
        switch (strategyEnum) {
            case OCR:
            case MANUAL_WRITE:
                return "disease_judge_special_raise";
            case USER_WRITE:
            case AMOUNT_REASONABLE:
                return "disease_judge_amount_high_risk_model";
            default:
                return null;
        }

    }

    private boolean isRaiseResult(DiseaseStrategyResultInfo result, DiseaseStrategyRequest diseaseStrategyRequest) {
        if (result == null){
            return false;
        }
        Set<Integer> raiseResult = raiseMapList.get(diseaseStrategyRequest.getExecuteStrategyEnum());
        if (CollectionUtils.isEmpty(raiseResult)){
            return false;
        }
        return raiseResult.contains(result.getResult());
    }


    private void saveResultLog(DiseaseStrategyRequest diseaseStrategyRequest,
                               DiseaseStrategyResponse diseaseStrategyResponse,
                               RiskExecuteStrategyTransit transit, RiskExecuteStrategy riskExecuteStrategy) {
        List<RiskExecuteStrategyLog> riskExecuteStrategyLogs = Lists.newArrayList();
        String operator = seaAccountService.getCurrAdminUserNameWithOrg(diseaseStrategyRequest.getUserId()).getUserNameWithOrg();
        if (diseaseStrategyRequest.getExecuteStrategyEnum() ==DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()){
            riskExecuteStrategyLogs.add(buildDiseaseAmountLog(riskExecuteStrategy.getStrategyName(),
                    diseaseStrategyResponse.getDiseaseAmountStrategyResult(),
                    transit,
                    diseaseStrategyRequest));
        } else {
            riskExecuteStrategyLogs.add(buildDiseaseLog(riskExecuteStrategy.getStrategyName(),
                    diseaseStrategyResponse.getDiseaseRaiseStrategyResult(),
                    transit,
                    diseaseStrategyRequest));
        }
        //减少调用次数
        riskExecuteStrategyLogs = riskExecuteStrategyLogs.stream().filter(Objects::nonNull).collect(Collectors.toList());
        riskExecuteStrategyLogs.forEach(v -> v.setOperator(operator));
        executeStrategyLogBiz.save(riskExecuteStrategyLogs);
    }

    private RiskExecuteStrategyLog buildDiseaseAmountLog(String strategyName,
                                                         DiseaseStrategyResultInfo resultInfo,
                                                         RiskExecuteStrategyTransit transit,
                                                         DiseaseStrategyRequest request) {
        if (resultInfo == null) {
            return null;
        }
        RiskExecuteStrategyLog riskExecuteStrategyLog = new RiskExecuteStrategyLog();
        riskExecuteStrategyLog.setCaseId(request.getCaseId());
        riskExecuteStrategyLog.setWorkOrderId(request.getWorkOrderId());
        riskExecuteStrategyLog.setExecuteTime(DateUtil.nowTime());
        riskExecuteStrategyLog.setStrategyName(strategyName);
        riskExecuteStrategyLog.setStrategyResult(String.valueOf(resultInfo.getResult()));
        riskExecuteStrategyLog.setStrategyResultDesc(resultInfo.getResultDesc());
        Map<String, Object> otherMap = Maps.newHashMap();
        otherMap.put("diseaseName", request.getDiseaseNameList());
        otherMap.put("diseaseMergeOneResult",
                transit.getDiseaseClassifyVOV2List().stream()
                        .map(v -> "【" + v.getDisease()+ "】：" + String.join(",", v.getNorm()))
                        .collect(Collectors.joining("\n")));
        if (transit.getResultVo() != null){
            otherMap.put("diseaseTreatmentInfo", transit.getResultVo().getTreatmentInfo());
            //参与未来花费计算的疾病病种
            otherMap.put("calculateDiseaseNames", transit.getResultVo().getCalculateDiseaseNames());
            //未来花费建议
            otherMap.put("adviseAmount", transit.getResultVo().getAdviseMinAmount() + "-" + transit.getResultVo().getAdviseMaxAmount() + "万元");
            //目标金额
            otherMap.put("targetAmount", transit.getTargetAmount() / 100);
        }
        otherMap.put("executeStrategyEnum", request.getExecuteStrategyEnum());
        riskExecuteStrategyLog.setOtherInfo(JSON.toJSONString(otherMap));
        return riskExecuteStrategyLog;
    }

    private RiskExecuteStrategyLog buildDiseaseLog(String strategyName,
                                                   DiseaseStrategyResultInfo resultInfo,
                                                   RiskExecuteStrategyTransit transit,
                                                   DiseaseStrategyRequest request) {
        if (resultInfo == null){
            return null;
        }
        RiskExecuteStrategyLog riskExecuteStrategyLog = new RiskExecuteStrategyLog();
        riskExecuteStrategyLog.setCaseId(request.getCaseId());
        riskExecuteStrategyLog.setWorkOrderId(request.getWorkOrderId());
        riskExecuteStrategyLog.setExecuteTime(DateUtil.nowTime());
        riskExecuteStrategyLog.setStrategyName(strategyName);
        riskExecuteStrategyLog.setStrategyResult(String.valueOf(resultInfo.getResult()));
        riskExecuteStrategyLog.setStrategyResultDesc(resultInfo.getResultDesc());
        Map<String, Object> otherMap = Maps.newHashMap();
        otherMap.put("diseaseName", request.getDiseaseNameList());
        otherMap.put("diseaseMergeOneResult",
                transit.getDiseaseClassifyVOV2List().stream().map(v -> "【" + v.getDisease()+ "】：" + String.join(",", v.getNorm()))
                        .collect(Collectors.joining("\n")));
        otherMap.put("diseaseTreatmentInfo", transit.getTreatmentInfo());
        otherMap.put("diseaseRaiseInfo", transit.getDiseaseInfoList().stream()
                .map(v -> "【" + v.getDiseaseName()+ "】：" + RiskDiseaseData.RaiseTypeEnum.findByCode(v.getDiseaseType()).getDesc())
                .collect(Collectors.joining("\n")));
        if (transit.getCaseRaiseType() != null) {
            otherMap.put("caseRaiseStatus", transit.getCaseRaiseType().getDesc());
        } else {
            //默认值为未覆盖
            otherMap.put("caseRaiseStatus", "未覆盖");
        }
        otherMap.put("executeStrategyEnum", request.getExecuteStrategyEnum());
        riskExecuteStrategyLog.setOtherInfo(JSON.toJSONString(otherMap));
        return riskExecuteStrategyLog;
    }

    private void setDiseaseName(DiseaseStrategyRequest diseaseStrategyRequest) {
        //第一版暂时只用用户输入的策略去查
        if (diseaseStrategyRequest.getExecuteStrategyEnum() != DiseaseStrategyEnum.USER_WRITE.getCode()){
            return;
        }
        RpcResult<RaiseBasicInfoModel> rpcResult = cfRaiseMaterialClient.selectRaiseBasicInfo(diseaseStrategyRequest.getCaseId());
        log.info("rpcResult.data:{} caseId:{}", JSON.toJSONString(rpcResult.getData()), diseaseStrategyRequest.getCaseId());
        if (rpcResult.isFail() ||rpcResult.getData() == null|| StringUtils.isBlank(rpcResult.getData().getDiseaseName())) {
            return;
        }
        //写入list
        diseaseStrategyRequest.setDiseaseNameList(List.of(
                StringUtils.split(rpcResult.getData().getDiseaseName(), ",，")));
    }

    public Response<List<DiseaseStrategyLog>> strategyResult(int caseId) {
        List<RiskExecuteStrategyLog> logs = executeStrategyLogBiz.getByCaseId(caseId);
        return NewResponseUtil.makeSuccess(buildResponseLogList(logs));
    }

    private List<DiseaseStrategyLog> buildResponseLogList(List<RiskExecuteStrategyLog> logs) {
        List<DiseaseStrategyLog> logList = Lists.newArrayListWithCapacity(logs.size());
        for (RiskExecuteStrategyLog log : logs){
            DiseaseStrategyLog diseaseStrategyLog = new DiseaseStrategyLog();
            BeanUtils.copyProperties(log, diseaseStrategyLog);
            diseaseStrategyLog.setWorkOrderId((int)log.getWorkOrderId());
            diseaseStrategyLog.setExecuteTime(DateUtil.formatDateTime(log.getExecuteTime()));
            diseaseStrategyLog.setOtherInfo(JSON.parseObject(log.getOtherInfo(), HashMap.class));
            diseaseStrategyLog.setOperator(diseaseStrategyLog.getOperator().replaceAll("null", ""));
            if (StringUtils.isBlank(diseaseStrategyLog.getOperator())){
                diseaseStrategyLog.setOperator("系统");
            }
            logList.add(diseaseStrategyLog);
        }
        return logList;
    }

}
