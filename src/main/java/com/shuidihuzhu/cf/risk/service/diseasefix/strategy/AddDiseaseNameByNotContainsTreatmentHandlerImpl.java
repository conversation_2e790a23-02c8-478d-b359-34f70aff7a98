package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AddDiseaseNameByNotContainsTreatmentHandlerImpl implements IStrategyHandler{

    @Resource
    private DiseaseDecideAmountService diseaseDecideAmountService;
    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;
    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {

        DecideReasonableInfo needFixRequest = diseaseFixContext.getNeedFixRequest();
        log.info("addDiseaseNameByNotContainsTreatment");

        // 获取治疗方案idList
        String treatmentName = logic.getTreatmentName();
        List<RiskDiseaseTreatmentProject> treatmentInfo = riskDiseaseTreatmentProjectRpcDao.findByTreatmentName(treatmentName);
        if(treatmentInfo == null){
            log.info("treatment:{} is not exist",treatmentName);
            return diseaseFixContext;
        }
        List<Long> treatmentIdList = treatmentInfo.stream().map(RiskDiseaseTreatmentProject::getId).collect(Collectors.toList());

        // 对比用户填写治疗方案
        final Map<String, List<Long>> getAllChoiceMap = diseaseDecideAmountService.getAllChoiceMap(needFixRequest);
        for(String disease : getAllChoiceMap.keySet()){
            List<Long> treatmentIdsByUser = getAllChoiceMap.get(disease);
            if(CollectionUtils.isEmpty(treatmentIdsByUser)){
                continue;
            }
            List<Long> list = Lists.newArrayList();
            list.addAll(treatmentIdList);
            list.retainAll(treatmentIdsByUser);
            if(CollectionUtils.isNotEmpty(list)){
                return diseaseFixContext;
            }
        }

        // 获取新增疾病id
        String addDiseaseName = logic.getAddDiseaseName();
        RiskDiseaseData diseaseInfo = riskDiseaseDataRpcDao.getByClassName(addDiseaseName);
        if(diseaseInfo == null){
            log.info("disease:{} is not exist",addDiseaseName);
            return diseaseFixContext;
        }
        needFixRequest.getDiseaseNameList().add(addDiseaseName);
        diseaseFixContext.setNeedFixRequest(needFixRequest);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "addDiseaseNameByNotContainsTreatment";
    }
}
