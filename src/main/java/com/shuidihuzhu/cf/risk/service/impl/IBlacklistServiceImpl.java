package com.shuidihuzhu.cf.risk.service.impl;

import com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataActionRefDao;
import com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataDao;
import com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataTypeRefDao;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataActionRef;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataTypeRef;
import com.shuidihuzhu.cf.risk.model.vo.blacklist.BlackListResult;
import com.shuidihuzhu.cf.risk.model.vo.blacklist.RiskBlacklistDataVo;
import com.shuidihuzhu.cf.risk.service.IBlacklistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/13 21:16
 */
@Slf4j
@Service
public class IBlacklistServiceImpl implements IBlacklistService {

    @Resource
    private RiskBlacklistDataActionRefDao dataActionRefDao;
    @Resource
    private RiskBlacklistDataDao blacklistDataDao;
    @Resource
    private RiskBlacklistDataTypeRefDao dataTypeRefDao;

    @Override
    public BlackListResult listByLimit(long previousId, int size) {
        List<RiskBlacklistDataActionRef> dataActionRefs = dataActionRefDao.listByLimitOptionalActionIds(null, previousId, size);
        if (CollectionUtils.isEmpty(dataActionRefs)) {
            return new BlackListResult(Collections.emptyList(), previousId);
        }
        Map<Long, List<RiskBlacklistDataActionRef>> dataIdMap = dataActionRefs.parallelStream()
                .collect(Collectors.groupingBy(RiskBlacklistDataActionRef::getDataId));
        List<RiskBlacklistData> blacklistDataList = blacklistDataDao.listByIds(dataIdMap.keySet());
        List<RiskBlacklistDataTypeRef> dataTypeRefs = dataTypeRefDao.listByDataIds(dataIdMap.keySet());
        Map<Long, List<Long>> dataTypesMap = dataTypeRefs.parallelStream()
                .collect(Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId,
                        Collector.of(ArrayList::new, (types, ref) -> types.add(ref.getTypeId()),
                                (left, right) -> { left.addAll(right); return left; })));
        return new BlackListResult(
                blacklistDataList.parallelStream().map(riskBlacklistData ->
                        RiskBlacklistDataVo.convertValidDataList(dataIdMap.get(riskBlacklistData.getId()),
                                riskBlacklistData, dataTypesMap.get(riskBlacklistData.getId())))
                        .flatMap(Collection::parallelStream)
                        .collect(Collectors.toList()),dataActionRefs.get(dataActionRefs.size()-1).getId()
        );
    }

}
