package com.shuidihuzhu.cf.risk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.biz.disease.*;
import com.shuidihuzhu.cf.risk.biz.impl.disease.*;
import com.shuidihuzhu.cf.risk.delegate.DiseaseNormDelegate;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.disease.MaxFeeDiseaseCallLog;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseRaiseTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseBurnEnum;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2020/3/16
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseRpcService {

    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;
    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz treatmentProjectRpcBiz;
    @Value("${disease.JudgeAmountListLimit:3}")
    private int diseaseJudgeAmountListLimit;
    @Autowired
    private DiseaseSpecialBurnHandler burnHandler;
    @Autowired
    private DiseaseSpecialFractureHandler fractureHandler;
    @Autowired
    private DiseaseSpecialHeartHandler heartHandler;
    @Autowired
    private DiseaseSpecialOtherHandler otherHandler;
    @Autowired
    private DiseaseSpecialMergeHandler mergeHandler;
    @Autowired
    private DiseaseSpecialSortHandler diseaseSpecialSortHandler;
    @Autowired
    private DiseaseSpecialTumourHandler specialTumourHandler;
    @Autowired
    private DiseaseSpecialHeartChoiceHandler heartChoiceHandler;
    @Autowired
    private DiseaseSpecialTumourChoiceHandler diseaseSpecialTumourChoiceHandler;
    @Autowired
    private DiseaseAmountCallLogBiz diseaseAmountCallLogBiz;
    @Autowired
    private MaxFeeDiseaseCallLogBiz maxFeeDiseaseCallLogBiz;
    @Resource(name = RiskDS.CF_RISK_REDIS)
    private RedissonHandler cfRiskRedissonHandler;

    @Resource
    private DiseaseNormDelegate diseaseNormDelegate;

    @Value("${disease.special.treatment-name.no-know:不知道治疗方案}")
    private String noKnowTreatmentName;
    @Value("${disease.special.raise.choice.limit:5}")
    private int specialRaiseChoiceLimit;

    @Autowired
    private DiseaseDecideAmountFacade diseaseDecideAmountFacade;

    @Autowired
    private DiseaseFixService diseaseFixService;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    private final static String ALARM_DISEASE_GROUP_ID = "wx-alarm-prod-20200313-0001";

    private ImmutableList<DiseaseSpecialHandler> specialHandlers;

    private ImmutableList<DiseaseSpecialHandler> choiceHandlers;

    private final static Integer CAN_RAISE = 1;

    /**
     * 初始化检查项目 有顺序
     */
    @PostConstruct
    public void init(){
        specialHandlers = ImmutableList.of(
                fractureHandler,
                otherHandler,
                //设计到心脏病的归一需要放在otherHandler 进行计算
                heartHandler,
                //1.6 涉及到烧伤的otherMerge 规则 所以burnHandler需要往后挪
                burnHandler,
                //diseaseSpecialSortHandler 必须放在otherHandler 之后
                diseaseSpecialSortHandler,
                mergeHandler,
                specialTumourHandler
        );
        choiceHandlers = ImmutableList.of(
                fractureHandler,
                otherHandler,
                //设计到心脏病的归一需要放在otherHandler 进行计算
                heartHandler,
                //1.6 涉及到烧伤的otherMerge 规则 所以burnHandler需要往后挪
                burnHandler,
                heartChoiceHandler,
                mergeHandler,
                //diseaseSpecialSortHandler 必须放在otherHandler 之后
                diseaseSpecialSortHandler,
                diseaseSpecialTumourChoiceHandler
        );
    }



    public Response<InfoReasonableAmountResultVo> decideInfoAmountReasonable(int targetAmount,
                                                                             DecideReasonableInfo decideReasonableInfo) {

        DiseaseDecideContext context = diseaseDecideAmountFacade.decideInfoByContext(targetAmount, decideReasonableInfo);
        return NewResponseUtil.makeSuccess(context.getResult());
//        return v1(targetAmount, decideReasonableInfo);
    }

    @NotNull
    private Response<InfoReasonableAmountResultVo> v1(int targetAmount, DecideReasonableInfo decideReasonableInfo) {
        List<String> mergeResult = mergeOneDiseaseList(decideReasonableInfo.getDiseaseNameList());
        //处理特殊病例
        InfoReasonableAmountResultVo reasonableAmountResultVo = new InfoReasonableAmountResultVo();
        //写入金额
        reasonableAmountResultVo.setTargetAmountInFen(targetAmount);
        reasonableAmountResultVo.setTreatmentInfo("");
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos
                = getByJson(decideReasonableInfo.getSpecialDiseaseInfo());
        dealNormalDisease(mergeResult, decideReasonableInfo, reasonableAmountResultVo,
                dealSpecialDisease(mergeResult, reasonableAmountResultVo, specialDiseaseChoiceInfos, specialHandlers), specialDiseaseChoiceInfos);
        fullAdviseMinAmount(targetAmount, reasonableAmountResultVo);
        //记录请求以及结果
        diseaseAmountCallLogBiz.saveLog(reasonableAmountResultVo, decideReasonableInfo, targetAmount);
        return NewResponseUtil.makeSuccess(reasonableAmountResultVo);
    }

    private List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> getByJson(String specialDiseaseInfo) {
        return JSONObject.parseArray(specialDiseaseInfo, SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
    }

    private List<String> mergeOneDiseaseList(List<String> diseaseList){
        //对归一后的疾病名称进行合并
        List<String> diseaseNameList = Lists.newArrayList();
        for (String diseaseName : diseaseList) {
            if (StringUtils.isBlank(diseaseName)) {
                diseaseNameList.add("");
                continue;
            }
            if (diseaseNameList.contains(diseaseName)){
                continue;
            }
            diseaseNameList.add(diseaseName);
        }
        return diseaseNameList;
    }

    /**
     * @param diseaseNameList
     * @param decideReasonableInfo
     * @param resultVo
     * @param needCalculate             需要额外增加的病例
     * @param specialDiseaseChoiceInfos
     */
    private void dealNormalDisease(List<String> diseaseNameList,
                                   DecideReasonableInfo decideReasonableInfo,
                                   InfoReasonableAmountResultVo resultVo,
                                   List<String> needCalculate,
                                   List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos) {
        //去重设置
        diseaseNameList = mergeOneDiseaseList(diseaseNameList);
        List<RiskDiseaseData> allRiskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(decideReasonableInfo.getDiseaseNameList());
        //检查疾病库
        checkExistDiseaseInfo(decideReasonableInfo, resultVo, allRiskDiseaseDataList);
        if (CollectionUtils.isEmpty(allRiskDiseaseDataList)){
            return ;
        }
        //获取用户选择的 治疗方案
        Map<String, List<Long>> choiceMap = getAllChoiceMap(specialDiseaseChoiceInfos, decideReasonableInfo);
        //过滤空白
        List<RiskDiseaseData> riskDiseaseDataList =
                getNeedDecideData(diseaseNameList, allRiskDiseaseDataList, needCalculate, getTreatmentProjectMap(allRiskDiseaseDataList, choiceMap), resultVo);
        riskDiseaseDataList.addAll(getNeedCalculateList(needCalculate));
        //写入案例是否可以发起的状态
        List<RiskDiseaseData> canList = getCanList(riskDiseaseDataList);
        resultVo.setCanRaiseStatus(CollectionUtils.isNotEmpty(canList) || CollectionUtils.isNotEmpty(resultVo.getDiseaseInfoList()));
        //写入可发起不可发起的状态
        fullDiseaseType(resultVo, riskDiseaseDataList);
        //写入参与计算的疾病名称
        final List<String> collect = resultVo.getDiseaseInfoList()
                .stream()
                .filter(DiseaseInfoVo::isCalculateSpecialStatus)
                .map(DiseaseInfoVo::getDiseaseName)
                .distinct()
                .collect(Collectors.toList());
        final String allCalculateDisease = getAllCalculateDisease(collect, riskDiseaseDataList);
        resultVo.setCalculateDiseaseNames(allCalculateDisease);
        //写入治疗金额
        setAmount(riskDiseaseDataList, resultVo, choiceMap);
    }

    private Map<Long, List<RiskDiseaseTreatmentProject>> getTreatmentProjectMap(List<RiskDiseaseData> allRiskDiseaseDataList,
                                                                                Map<String, List<Long>> choiceMap) {
        List<Long> diseaseIds = allRiskDiseaseDataList.stream().map(RiskDiseaseData::getId).collect(Collectors.toList());
        List<RiskDiseaseTreatmentProject> treatmentProjectList = filterChoiceTreatment(getCanTreatmentProjectList(diseaseIds), choiceMap, allRiskDiseaseDataList);
        return treatmentProjectList.stream().collect(Collectors.groupingBy(RiskDiseaseTreatmentProject::getDiseaseId));
    }

    private Map<String, List<Long>> getAllChoiceMap(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos,
                                                   DecideReasonableInfo decideReasonableInfo) {
        Map<String, List<Long>> choiceMap = getChoiceMap(specialDiseaseChoiceInfos);
        Map<String, List<Long>> specialRaiseChoiceMap = getChoiceMap(JSONObject.parseArray(decideReasonableInfo.getSpecialRaiseChoiceInfo(),
                SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class));
        if (MapUtils.isNotEmpty(specialRaiseChoiceMap)) {
            choiceMap.putAll(specialRaiseChoiceMap);
        }
        return choiceMap;
    }


    private List<RiskDiseaseData> getNeedCalculateList(List<String> needCalculate) {
        if (CollectionUtils.isEmpty(needCalculate)) {
            return Lists.newArrayList();
        }
        return riskDiseaseDataRpcBiz.getByClassNameList(needCalculate.stream()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
    }


    private List<RiskDiseaseData> getNeedDecideData(List<String> diseaseNameList,
                                                    final List<RiskDiseaseData> allRiskDiseaseDataList,
                                                    List<String> needCalculate,
                                                    Map<Long, List<RiskDiseaseTreatmentProject>> projectMap,
                                                    InfoReasonableAmountResultVo resultVo) {
        Map<String, RiskDiseaseData> riskDiseaseDataMap = allRiskDiseaseDataList
                .stream().collect(Collectors.toMap(RiskDiseaseData::getDiseaseClassName, Function.identity()));
        //建立一个标志位  当到达对应的数量返回
        List<RiskDiseaseData> topThreeList = Lists.newArrayList();
        List<String> existNameList = Lists.newArrayList();
        int initFlag = 0;
        for (String diseaseName : diseaseNameList) {
            //如果达到限定的数量  直接返回
            if (initFlag >= diseaseJudgeAmountListLimit) {
                return topThreeList;
            }
            if (StringUtils.isBlank(diseaseName)) {
                initFlag ++ ;
                continue;
            }
            //1. 如果和之前归一的结果相同也直接跳过
            //2. 特殊疾病包含的直接跳过
            if (existNameList.contains(diseaseName) || needCalculate.contains(diseaseName)) {
                continue;
            }
            RiskDiseaseData currentData = riskDiseaseDataMap.get(diseaseName);
            //未匹配到则需要进行加位
            if (currentData == null) {
                initFlag ++ ;
                continue;
            }
            //如果是不可发起的直接跳过
            if (currentData.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode()) {
                continue;
            }
            //判断特殊疾病是否为可发起的
            if (decideSpecialData(resultVo, currentData,  projectMap.get(currentData.getId()))) {
                continue;
            }
            topThreeList.add(currentData);
            existNameList.add(diseaseName);
            initFlag ++;
        }
        return topThreeList;
    }

    private boolean decideSpecialData(InfoReasonableAmountResultVo resultVo, RiskDiseaseData currentData, List<RiskDiseaseTreatmentProject> treatmentProjects) {
        if (currentData.getRaiseType() != RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()){
            return false;
        }
        for (RiskDiseaseTreatmentProject project : treatmentProjects) {
            //可发起直接返回
            if (project.getRaiseType() == 1) {
               return false;
            }
            //如果当前治疗方案为不可发起则需要增加治疗信息
            resultVo.setTreatmentInfo(StringUtils.trimToEmpty(resultVo.getTreatmentInfo()) +
                    buildInfoStringByTreatment(currentData.getDiseaseClassName(), Lists.newArrayList(project)).toString());
            break;
        }
        return true;
    }

    private String getAllCalculateDisease(List<String> calculateDiseaseNames, List<RiskDiseaseData> riskDiseaseDataList) {
        calculateDiseaseNames.addAll(riskDiseaseDataList.stream()
                .map(RiskDiseaseData::getDiseaseClassName)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(calculateDiseaseNames)) {
            return "";
        }
        return Joiner.on(",").join(calculateDiseaseNames.stream().distinct().collect(Collectors.toList()));
    }

    private void setAmount(List<RiskDiseaseData> riskDiseaseDataList,
                           InfoReasonableAmountResultVo resultVo,
                           Map<String, List<Long>> choiceMap) {
        List<Long> diseaseIds = riskDiseaseDataList.stream().map(RiskDiseaseData::getId).collect(Collectors.toList());
        List<RiskDiseaseTreatmentProject> treatmentProjectList = filterChoiceTreatment(getCanTreatmentProjectList(diseaseIds), choiceMap, riskDiseaseDataList);
        setTreatmentInfo(riskDiseaseDataList, treatmentProjectList, resultVo);
        if (CollectionUtils.isEmpty(treatmentProjectList)) {
            log.info("treatmentProjectList is empty");
            return;
        }
        int minAmount = treatmentProjectList.stream()
                .mapToInt(RiskDiseaseTreatmentProject::getMinTreatmentFee).sum();
        int maxAmount = treatmentProjectList.stream()
                .mapToInt(RiskDiseaseTreatmentProject::getMaxTreatmentFee).sum();
        //匹配金额
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + minAmount / 100.0);
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + maxAmount / 100.0);
    }

    private void setTreatmentInfo(List<RiskDiseaseData> riskDiseaseDataList,
                                  List<RiskDiseaseTreatmentProject> treatmentProjectList,
                                  InfoReasonableAmountResultVo resultVo) {
        StringBuilder builder = new StringBuilder();
        Map<Long, List<RiskDiseaseTreatmentProject>> treatmentProjectMap = transTreatmentProjectMap(treatmentProjectList);
        for (RiskDiseaseData riskDiseaseData : riskDiseaseDataList) {
            List<RiskDiseaseTreatmentProject> treatmentProjectPart = treatmentProjectMap.get(riskDiseaseData.getId());
            if (CollectionUtils.isEmpty(treatmentProjectPart)){
                continue;
            }
            builder.append(buildInfoStringByTreatment(riskDiseaseData.getDiseaseClassName(), treatmentProjectPart));
            //处理治疗方案
            for (DiseaseInfoVo diseaseInfoVo : resultVo.getDiseaseInfoList() ) {
                if (diseaseInfoVo.getDiseaseName().equals(riskDiseaseData.getDiseaseClassName())){
                    diseaseInfoVo.setTreatmentInfo(Joiner.on("+").join(treatmentProjectPart.stream()
                            .map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.toList())));
                }
            }
        }
        resultVo.setTreatmentInfo(StringUtils.trimToEmpty(resultVo.getTreatmentInfo()) + builder.toString());
    }

    private StringBuilder buildInfoStringByTreatment(String diseaseClassName, List<RiskDiseaseTreatmentProject> treatmentProjectPart) {
        StringBuilder builder = new StringBuilder();
        builder.append("【").append(diseaseClassName).append("】:");
        builder.append(Joiner.on("+").join(treatmentProjectPart.stream()
                .map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.toList())));
        builder.append("<br>");
        return builder;
    }

    /**
     *
     * @param treatmentProjects         疾病下所有的治疗方案
     * @param choiceMap                 用户选择的方式
     * @param riskDiseaseDataList       疾病的基础信息
     * @return 最后确定的治疗方案
     */
    private List<RiskDiseaseTreatmentProject> filterChoiceTreatment(List<RiskDiseaseTreatmentProject> treatmentProjects,
                                                                    Map<String, List<Long>> choiceMap,
                                                                    List<RiskDiseaseData> riskDiseaseDataList) {
        List<RiskDiseaseTreatmentProject> result = Lists.newArrayList();
        Map<Long, List<RiskDiseaseTreatmentProject>> allTreatmentProjects = transTreatmentProjectMap(treatmentProjects);
        for (RiskDiseaseData diseaseData : riskDiseaseDataList) {
            List<RiskDiseaseTreatmentProject> treatmentList = allTreatmentProjects.get(diseaseData.getId());
            List<Long> choiceTreatmentIdList = choiceMap.get(diseaseData.getDiseaseClassName());
            if (CollectionUtils.isEmpty(treatmentList)) {
                log.error("treatmentList is empty:{}", diseaseData.getId());
                continue;
            }
            if (CollectionUtils.isNotEmpty(choiceTreatmentIdList)) {
                result.addAll(treatmentList.stream().filter(v -> choiceTreatmentIdList.contains(v.getId()))
                        .collect(Collectors.toList()));
                continue;
            }
           /* //没选择返回不知道的治疗方案
            List<RiskDiseaseTreatmentProject> noKnow = treatmentList.stream().
                    filter(v -> noKnowTreatmentName.equals(v.getProjectName()) || "无分期".equals(v.getProjectName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noKnow)) {
                result.addAll(noKnow);
            } else {
                result.add(treatmentList.get(0));
            }*/
            //目标金额合理性判断策略选择最便宜的方案
            RiskDiseaseTreatmentProject minTreatmentProject = treatmentList.stream()
                    .sorted(Comparator.comparing(RiskDiseaseTreatmentProject::getMinTreatmentFee))
                    .collect(Collectors.toList()).get(0);
            result.add(minTreatmentProject);
        }
        return result;
    }

    private Map<Long, List<RiskDiseaseTreatmentProject>> transTreatmentProjectMap(List<RiskDiseaseTreatmentProject> treatmentProjects) {
        Map<Long, List<RiskDiseaseTreatmentProject>> result = Maps.newHashMap();
        for (RiskDiseaseTreatmentProject treatmentProject: treatmentProjects) {
            List<RiskDiseaseTreatmentProject> treatmentProjectsPart = result.get(treatmentProject.getDiseaseId());
            if (CollectionUtils.isEmpty(treatmentProjectsPart)) {
                treatmentProjectsPart = Lists.newArrayList();
            }
            treatmentProjectsPart.add(treatmentProject);
            result.put(treatmentProject.getDiseaseId(), treatmentProjectsPart);
        }
        return result;
    }

    /**
     *
     * @param specialDiseaseChoiceInfos          选择的特殊信息
     * @return key为疾病名称 value为治疗方案id的Map
     */
    public Map<String, List<Long>> getChoiceMap(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos) {
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return Maps.newHashMap();
        }
        Map<String, List<Long>> choiceMap  = Maps.newHashMap();
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return choiceMap;
        }
        //特殊可发起治疗方案的展示
        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : specialDiseaseChoiceInfos) {
            List<Long> treatmentProjectIdList = getTreatmentProjectId(specialDiseaseChoiceInfo.getChoiceInfoList());
            choiceMap.put(specialDiseaseChoiceInfo.getSpecialDiseaseName(), treatmentProjectIdList);
        }
        return choiceMap;
    }

    /**
     * 获取需要计算的id
     * @return 疾病对应的治疗方案idList
     */
    private List<Long> getTreatmentProjectId(List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList) {
        if (CollectionUtils.isEmpty(choiceInfoList)){
            return Lists.newArrayList();
        }
        List<Long> treatmentProjectIds = Lists.newArrayList();
        for (SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo : choiceInfoList) {
            if (choiceInfo.isRealCalculateChoice()) {
                treatmentProjectIds.add(choiceInfo.getChoiceId());
            }
            if (CollectionUtils.isEmpty(choiceInfo.getChoiceInfoList())) {
                continue;
            }
            treatmentProjectIds.addAll(getTreatmentProjectId(choiceInfo.getChoiceInfoList()));
        }
        return treatmentProjectIds;
    }


    /**
     * 处理特殊的病例
     * @param diseaseNameList   疾病民称
     * @param resultVo          结果集合
     */
    private List<String> dealSpecialDisease(List<String> diseaseNameList,
                                            InfoReasonableAmountResultVo resultVo,
                                            List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos,
                                            ImmutableList<DiseaseSpecialHandler> specialHandlers) {
        List<String> needCalculate = Lists.newArrayList();
        for (DiseaseSpecialHandler specialHandler : specialHandlers) {
            needCalculate.addAll(specialHandler
                    .dealSpecialDisease(resultVo, diseaseNameList, diseaseJudgeAmountListLimit, specialDiseaseChoiceInfos));
        }
        if (CollectionUtils.isNotEmpty(resultVo.getDiseaseInfoList())) {
            resultVo.setCanRaiseStatus(true);
        }
        resultVo.setCalculateDiseaseNames(Joiner.on(",").join(resultVo.getDiseaseInfoList()
                .stream().map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList())));
        return needCalculate;
    }

    /**
     * 写入疾病信息
     * @param resultVo              结果信息
     * @param riskDiseaseDataList   疾病库list
     */
    private void fullDiseaseType(InfoReasonableAmountResultVo resultVo, List<RiskDiseaseData> riskDiseaseDataList) {
        for (RiskDiseaseData diseaseData : riskDiseaseDataList){
            DiseaseInfoVo diseaseInfoVo = new DiseaseInfoVo();
            diseaseInfoVo.setDiseaseName(diseaseData.getDiseaseClassName());
            diseaseInfoVo.setDiseaseType(diseaseData.getRaiseType());
            resultVo.getDiseaseInfoList().add(diseaseInfoVo);
        }
    }

    private List<RiskDiseaseData> getCanList(List<RiskDiseaseData> riskDiseaseDataList) {
        //过滤可发起的疾病库信息
        List<RiskDiseaseData> canList = riskDiseaseDataList.stream()
                .filter(v -> v.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()
                        || v.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canList)) {
            return Lists.newArrayList();
        }
        if (canList.size() <= diseaseJudgeAmountListLimit) {
            return canList;
        }
        return canList.subList(0, diseaseJudgeAmountListLimit);
    }

    /**
     * 写入金额顺序
     * @param targetAmount          案例的目标金额
     * @param resultVo              疾病结果Vo
     */
    private void fullAdviseMinAmount(int targetAmount,
                                     InfoReasonableAmountResultVo resultVo) {
        if (resultVo.getMatchMaxAmount() <= 0) {
            return;
        }
        //建议金额
        resultVo.setAdviseMinAmount(getMinAmount(resultVo.getMatchMinAmount()));
        resultVo.setAdviseMaxAmount(getAdviseMaxAmount(resultVo.getMatchMaxAmount()));
        //目标金额以分为单位  所以金额需要* 1000000
        resultVo.setReasonableStatus(targetAmount <= (resultVo.getAdviseMaxAmount() * 1000000));
    }

    private double getMinAmount(double originMinAmount) {
        return Math.min(originMinAmount, 50.0);
    }

    /**
     * 匹配疾病的金额
     * a.标准基数5万元以下的(含5万)，可接受上浮2万元。
     * b.标准基数6-20万元的(含20万)，可接受上浮3万元。
     * c.标准基数21-35万元的(含35万)，可接受上浮5万元。
     * d.标准基数36万元以上的，可接受上浮10万元，上限50万元。
     * @param sumAmount 疾病总的上限金额
     * @return 加上上浮范围的金额
     */
    private double getAdviseMaxAmount(double sumAmount) {
        return Math.min(sumAmount, 50.0);
    }

    private List<RiskDiseaseTreatmentProject> getCanTreatmentProjectList(List<Long> diseaseIds) {
        // 1.3 去除每个疾病只保留一个治疗方案的格式
        return treatmentProjectRpcBiz.findByDiseaseId(diseaseIds);
    }


    /**
     * 检查不存在的疾病 如果不存在 则发送报警到企业微信群
     * @param riskDiseaseDataList   查询出的疾病库信息
     * @param decideReasonableInfo       业务传入的疾病库信息
     * @param resultVo                  结果信息
     * @param
     */
    private void checkExistDiseaseInfo(DecideReasonableInfo decideReasonableInfo,
                                       final InfoReasonableAmountResultVo resultVo,
                                       List<RiskDiseaseData> riskDiseaseDataList) {
        List<String> specialNameList = resultVo.getDiseaseInfoList().stream().map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(riskDiseaseDataList) && CollectionUtils.isEmpty(specialNameList)) {
            //没查询到信息 直接报警
            alarmClient.sendByGroup(ALARM_DISEASE_GROUP_ID, getAlarmContent(decideReasonableInfo, Lists.newArrayList()));
            return;
        }
        List<String> existNameList = riskDiseaseDataList.stream()
                .map(RiskDiseaseData::getDiseaseClassName).collect(Collectors.toList());
        existNameList.addAll(specialNameList);
        existNameList = existNameList.stream().distinct().collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("existNameList:{}, DiseaseName:{}", JSON.toJSONString(existNameList),
                    JSON.toJSONString(decideReasonableInfo.getDiseaseNameList()));
        }
        if (existNameList.containsAll(decideReasonableInfo.getDiseaseNameList()
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()))) {
            return;
        }
        alarmClient.sendByGroup(ALARM_DISEASE_GROUP_ID,  getAlarmContent(decideReasonableInfo, existNameList));
    }

    private String getAlarmContent(DecideReasonableInfo decideReasonableInfo, List<String> existNameList) {
        if (CollectionUtils.isEmpty(existNameList)) {
            //兼容未匹配到的情况
            existNameList.add("null");
        }

        return WorkWeiXinContentBuilder.create()
                .subject("【疾病库匹配结果报错】")
                .payload("案例ID", decideReasonableInfo.getCaseId())
                .payload("工单ID",  StringUtils.substringBetween(decideReasonableInfo.getExtContent(),"工单ID：", "\n"))
                .payload("疾病名称", StringUtils.substringBetween(decideReasonableInfo.getExtContent(),"疾病名称：", "\n"))
                .payload("疾病归一结果", StringUtils.join(decideReasonableInfo.getDiseaseNameList(), ","))
                .payload("疾病库匹配结果", StringUtils.join(existNameList, ","))
                .payload("报错类型", "未匹配到疾病类型")
                .payload("报错时间", DateUtil.getCurrentDateTimeStr())
                .payload("操作人及其组织架构", StringUtils.substringAfter(decideReasonableInfo.getExtContent(),"操作人"))
                .build();
    }

    public Response<SpecialDiseaseChoiceInfoVo> specialChoiceInfo(List<String> diseaseNameList, String specialRaiseChoiceInfo) {

        SpecialDiseaseChoiceInfoVo specialDiseaseChoiceInfoVo = new SpecialDiseaseChoiceInfoVo();
        if (diseaseNameList.size() > specialRaiseChoiceLimit){
            diseaseNameList = diseaseNameList.subList(0, specialRaiseChoiceLimit);
        }
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(diseaseNameList);
        if (CollectionUtils.isEmpty(riskDiseaseDataList)) {
            return NewResponseUtil.makeSuccess(specialDiseaseChoiceInfoVo);
        }
        List<RiskDiseaseData> specialRaiseList = riskDiseaseDataList.stream()
                .filter(r -> r.getChoiceType() != DEFAULT.getCode())
//                .filter(r -> r.getRaiseType() == DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode()
//                        || StringUtils.equals(r.getDiseaseClassName(), DiseaseCommonConfig.BURN_NAME))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specialRaiseList)){
            return NewResponseUtil.makeSuccess(specialDiseaseChoiceInfoVo);
        }

        final SpecialDiseaseChoiceInfoVo generateResult = generateChoiceInfo(specialDiseaseChoiceInfoVo, specialRaiseList);

        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialRemoveList =
                JSON.parseArray(specialRaiseChoiceInfo, SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
        if (CollectionUtils.isNotEmpty(specialRemoveList)) {
            generateResult.getSpecialDiseaseChoiceInfoList().removeAll(specialRemoveList);
            generateResult.setContainSpecialDisease(CollectionUtils.isNotEmpty(generateResult.getSpecialDiseaseChoiceInfoList()));
        }
        return NewResponseUtil.makeSuccess(generateResult);
    }

    public Response<SpecialDiseaseChoiceInfoVo> specialRaiseChoiceInfo(List<String> diseaseNameList){
        SpecialDiseaseChoiceInfoVo specialDiseaseChoiceInfoVo = new SpecialDiseaseChoiceInfoVo();
        if (diseaseNameList.size() > specialRaiseChoiceLimit){
            diseaseNameList = diseaseNameList.subList(0, specialRaiseChoiceLimit);
        }
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(diseaseNameList);
        if (CollectionUtils.isEmpty(riskDiseaseDataList)) {
            return NewResponseUtil.makeSuccess(specialDiseaseChoiceInfoVo);
        }
        List<RiskDiseaseData> specialRaiseList = riskDiseaseDataList.stream()
                .filter(r -> r.getRaiseType() == DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specialRaiseList)){
            return NewResponseUtil.makeSuccess(specialDiseaseChoiceInfoVo);
        }

        return NewResponseUtil.makeSuccess(generateChoiceInfo(specialDiseaseChoiceInfoVo, specialRaiseList));

    }

    public SpecialDiseaseChoiceInfoVo generateChoiceInfo(SpecialDiseaseChoiceInfoVo specialDiseaseChoiceInfoVo,
                                                         List<RiskDiseaseData> diseaseList){
        if (CollectionUtils.isEmpty(diseaseList)){
            return new SpecialDiseaseChoiceInfoVo();
        }

        diseaseList = diseaseList.stream().filter(
                v -> v.getChoiceType() != DiseaseTreamentChoiceTypeEnum.DEFAULT.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(diseaseList)){
            return new SpecialDiseaseChoiceInfoVo();
        }
        //写入是否多选择的疾病
        specialDiseaseChoiceInfoVo.setContainSpecialDisease(CollectionUtils.isNotEmpty(diseaseList));

        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos = Lists.newArrayList();
        Map<Long, List<RiskDiseaseTreatmentProject>> treatmentProjectMap = getSpecialTreatmentProjectMap(diseaseList);
        //生成选择项
        for (RiskDiseaseData diseaseData: diseaseList) {
            SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo choiceInfo = buildChoiceVo(diseaseData, treatmentProjectMap.get(diseaseData.getId()));
            specialDiseaseChoiceInfos.add(choiceInfo);
        }
        specialDiseaseChoiceInfoVo.setSpecialDiseaseChoiceInfoList(specialDiseaseChoiceInfos);
        return specialDiseaseChoiceInfoVo;
    }




    /**
     * 获取所有治疗方式的治疗方案
     * @param riskDiseaseDataList   特殊疾病list
     * @return 治疗方案list
     */
    private Map<Long, List<RiskDiseaseTreatmentProject>> getSpecialTreatmentProjectMap(List<RiskDiseaseData> riskDiseaseDataList) {
        if (CollectionUtils.isEmpty(riskDiseaseDataList)){
            return Maps.newHashMap();
        }
        List<RiskDiseaseTreatmentProject> treatmentProjects = treatmentProjectRpcBiz
                .findByDiseaseId(riskDiseaseDataList.stream().map(RiskDiseaseData::getId).collect(Collectors.toList()));
        Map<Long, List<RiskDiseaseTreatmentProject>> map = Maps.newHashMap();
        for (RiskDiseaseTreatmentProject treatmentProject : treatmentProjects) {
            List<RiskDiseaseTreatmentProject> treatmentProjectPart = map.get(treatmentProject.getDiseaseId());
            if (CollectionUtils.isEmpty(treatmentProjectPart)) {
                treatmentProjectPart = Lists.newArrayList();
            }
            treatmentProjectPart.add(treatmentProject);
            map.put(treatmentProject.getDiseaseId(), treatmentProjectPart);
        }
        return map;
    }

    /**
     * 生成特殊的选项
     * @param diseaseData                           需要生成选择项的特殊疾病
     * @param riskDiseaseTreatmentProjects          需要生成特殊疾病的治疗方案
     * @return  治疗方案分组对象
     */
    private SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo buildChoiceVo(RiskDiseaseData diseaseData,
                                                                              List<RiskDiseaseTreatmentProject> riskDiseaseTreatmentProjects) {
        DiseaseTreamentChoiceTypeEnum choiceType = DiseaseTreamentChoiceTypeEnum.findByCode(diseaseData.getChoiceType());
        if (choiceType == null || choiceType == DiseaseTreamentChoiceTypeEnum.DEFAULT) {
            return null;
        }
        //判断是否只有烧伤
        switch (choiceType) {
            case INPUT_FIELD:
                return buildBurnChoiceInfoVo();
            case SINGLE_CHOICE:
                return buildSingleChoiceInfoVo(diseaseData, riskDiseaseTreatmentProjects);
            case MULTIPLE_CHOICE:
                return buildMultipleChoiceInfoVo(diseaseData, riskDiseaseTreatmentProjects);
            default:
                return null;
        }
    }


    /**
     * 生成特殊的选项
     * @param diseaseData                   需要生成选择项的特殊疾病
     * @param treatmentProjects             需要生成特殊疾病的治疗方案
     * @return  治疗方案分组对象
     */
    private SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo buildMultipleChoiceInfoVo(RiskDiseaseData diseaseData,
                                                                                          List<RiskDiseaseTreatmentProject> treatmentProjects) {
        log.info("build Mulitup");
        List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfos = Lists.newArrayList();
        //获取不知道治疗方案
        List<RiskDiseaseTreatmentProject> noKnowList = treatmentProjects.stream()
                .filter(v -> v.getProjectName().equals(noKnowTreatmentName)).collect(Collectors.toList());
        int diseaseRaiseType = diseaseData.getRaiseType();
        //多选分为存在不知道治疗和不存在两种
        if (CollectionUtils.isEmpty(noKnowList)) {
            for (RiskDiseaseTreatmentProject treatmentProject : treatmentProjects) {
                choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(treatmentProject.getId(),
                        DiseaseTreamentChoiceTypeEnum.MULTIPLE_CHOICE.getCode(), treatmentProject.getProjectName(),
                        treatmentProject.getNoSameTimeId(),true, "",
                        diseaseRaiseType == DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode() ? treatmentProject.getRaiseType() : diseaseRaiseType,
                        Lists.newArrayList()));
            }
        } else {
            existNoKnowTreatmentProject(treatmentProjects, choiceInfos, noKnowList, diseaseRaiseType);
        }
        return new SpecialDiseaseChoiceInfoVo.
                SpecialDiseaseChoiceInfo(DiseaseTreamentChoiceTypeEnum.MULTIPLE_CHOICE.getCode(),
                DiseaseTreamentChoiceTypeEnum.MULTIPLE_CHOICE.getWord(),
                diseaseData.getDiseaseClassName(), choiceInfos);
    }

    /**
     * 当存在不知道治疗方案时需要单独处理
     * @param treatmentProjects 治疗方案
     * @param choiceInfos   选项信息
     * @param noKnowList    不知道的治疗的方案
     */
    private void existNoKnowTreatmentProject(List<RiskDiseaseTreatmentProject> treatmentProjects,
                                             List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfos,
                                             List<RiskDiseaseTreatmentProject> noKnowList, int diseaseRaiseType) {
        RiskDiseaseTreatmentProject noKnowTreatmentProject = noKnowList.get(0);
        if (noKnowTreatmentProject == null) {
            log.error("noKnowTreatmentProject is null");
           return;
        }
        choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(noKnowTreatmentProject.getId(), SINGLE_CHOICE.getCode(),
                DiseaseCommonConfig.NO_KNOW_CHOICE_NAME, true, noKnowTreatmentProject.getRaiseType()));
        treatmentProjects.removeAll(noKnowList);
        //对治疗方案进行分组处理
        SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo = new SpecialDiseaseChoiceInfoVo
                .ChoiceInfo(SINGLE_CHOICE.getCode(), DiseaseCommonConfig.KNOW_CHOICE_NAME, false, 0);
        choiceInfo.setChoiceInfoList(Lists.newArrayList());
        choiceInfos.add(choiceInfo);
        for (RiskDiseaseTreatmentProject treatmentProject : treatmentProjects) {
            choiceInfo.getChoiceInfoList().add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(treatmentProject.getId(),
                    DiseaseTreamentChoiceTypeEnum.MULTIPLE_CHOICE.getCode(), treatmentProject.getProjectName(),
                    treatmentProject.getNoSameTimeId(),true, "",
                    diseaseRaiseType == DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode() ? treatmentProject.getRaiseType() : diseaseRaiseType,
                    Lists.newArrayList()));
        }
    }

    /**
     * 生成单选框的选择信息
     * @param diseaseData                           需要生成选择项的特殊疾病
     * @param riskDiseaseTreatmentProjects          需要生成特殊疾病的治疗方案
     * @return 单选框的选择信息Vo
     */
    private SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo buildSingleChoiceInfoVo(RiskDiseaseData diseaseData,
                                                                                        List<RiskDiseaseTreatmentProject> riskDiseaseTreatmentProjects) {
        List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(riskDiseaseTreatmentProjects)) {
            return new SpecialDiseaseChoiceInfoVo.
                    SpecialDiseaseChoiceInfo(DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getCode(),
                    DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getWord(),
                    diseaseData.getDiseaseClassName(), choiceInfos);
        }
        int diseaseRaiseType = diseaseData.getRaiseType();
        for (RiskDiseaseTreatmentProject treatmentProject : riskDiseaseTreatmentProjects) {
            SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo = new SpecialDiseaseChoiceInfoVo
                    .ChoiceInfo(treatmentProject.getId(), DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getCode(),
                    treatmentProject.getProjectName(), true,
                    diseaseRaiseType == DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode() ? treatmentProject.getRaiseType() : diseaseRaiseType);
            choiceInfos.add(choiceInfo);
        }
        return new SpecialDiseaseChoiceInfoVo.
                SpecialDiseaseChoiceInfo(DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getCode(),
                DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getWord(),
                diseaseData.getDiseaseClassName(), choiceInfos);
    }

    private SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo buildBurnChoiceInfoVo() {
        List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfos = Lists.newArrayList();
        choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(INPUT_FIELD.getCode(),
                SpecialDiseaseBurnEnum.SIMPLE_TWO.getDesc(),  true, CAN_RAISE));
        choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(INPUT_FIELD.getCode(),
                SpecialDiseaseBurnEnum.SIMPLE_TWO_TO_THREE.getDesc(),  true, CAN_RAISE));
        choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(INPUT_FIELD.getCode(),
                SpecialDiseaseBurnEnum.OTHER.getDesc(),  true, CAN_RAISE));
        choiceInfos.add(new SpecialDiseaseChoiceInfoVo.ChoiceInfo(SPECIAL_BURN.getCode(),
                SpecialDiseaseBurnEnum.SPECIAL_BURN.getDesc(),  true, CAN_RAISE));
        return new  SpecialDiseaseChoiceInfoVo.
                SpecialDiseaseChoiceInfo(INPUT_FIELD.getCode(), INPUT_FIELD.getWord(),DiseaseCommonConfig.BURN_NAME, choiceInfos);
    }

    public Response<Set<String>> getAllDiseaseName() {
        return NewResponseUtil.makeSuccess(Sets.newHashSet(riskDiseaseDataRpcBiz.findAllDiseaseNameCache()));
    }

    public List<DiseaseClassifyVOV2> diseaseNorm(List<String> diseaseList) {
        return diseaseNormNew(diseaseList);
    }

    public List<DiseaseClassifyVOV2> diseaseNormNew(List<String> diseaseList) {
        Map<String, Set<String>> map = diseaseNormDelegate.agent(diseaseList);
        if (MapUtils.isEmpty(map)) {
            return Lists.newArrayList();
        }
        //转化对象
        return map.keySet().stream().map(key ->
        {
            DiseaseClassifyVOV2 diseaseClassifyVOV2 = new DiseaseClassifyVOV2();
            diseaseClassifyVOV2.setDisease(key);
            diseaseClassifyVOV2.setNorm(Lists.newArrayList(map.get(key)));
            return diseaseClassifyVOV2;
        }).collect(Collectors.toList());
    }


    public List<DiseaseInfoVo> buildVoList(List<RiskDiseaseData> riskDiseaseDataList) {
        return riskDiseaseDataList.stream().map(v ->{
            DiseaseInfoVo diseaseInfoVo = new DiseaseInfoVo();
            diseaseInfoVo.setDiseaseName(v.getDiseaseClassName());
            diseaseInfoVo.setDiseaseType(v.getRaiseType());
            return diseaseInfoVo;
        }).collect(Collectors.toList());
    }

    public List<DiseaseProjectVO> maxFeeOfDiseaseProjectList(String diseases, int caseId){
        MaxFeeDiseaseCallLog callLog = maxFeeOfDiseaseProjects(diseases, caseId);
        if (callLog == null){
            return new ArrayList<>();
        }
        return JSONObject.parseArray(callLog.getMaxFeeStr(), DiseaseProjectVO.class);
    }

    public String maxFeeOfDiseaseProjectStr(String diseases, int caseId){
        MaxFeeDiseaseCallLog callLog = maxFeeOfDiseaseProjects(diseases, caseId);
        if (callLog == null){
            return "";
        }
        return callLog.getResponseStr();
    }

    public MaxFeeDiseaseCallLog maxFeeOfDiseaseProjects(String diseases, int caseId) {
        String cacheName = "cache_" + caseId + "_v2_" + diseases.hashCode();
        String lockName = "lock_" + caseId + "_v2_" + diseases.hashCode();
        RLock rLock = null;
        int retryTime = 0;
        while (true) {
            try {
                //1 查询缓存'cache!=null' 空字符串表示没结果防止穿透
                String cache = cfRiskRedissonHandler.get(cacheName, String.class);
                if (cache != null){
                    return JSONObject.toJavaObject(JSON.parseObject(cache), MaxFeeDiseaseCallLog.class);
                }
                //2 加锁防止缓存击穿
                rLock = cfRiskRedissonHandler.getLock(lockName);
                if (!rLock.tryLock(500L, 1000L * 60, TimeUnit.MILLISECONDS)) {
                    retryTime ++;
                    if (retryTime > 6){
                        cache = cfRiskRedissonHandler.get(cacheName, String.class);
                        if (cache != null){
                            return JSONObject.toJavaObject(JSON.parseObject(cache), MaxFeeDiseaseCallLog.class);
                        }
                    }
                    continue;
                }
                //3 防止自旋500ms期间并发
                cache = cfRiskRedissonHandler.get(cacheName, String.class);
                if (cache != null){
                    return JSONObject.toJavaObject(JSON.parseObject(cache), MaxFeeDiseaseCallLog.class);
                }
                //4 业务处理
                MaxFeeDiseaseCallLog callLog = handleDiseasesAndSaveLogNew(diseases, caseId);
                if (callLog != null) {
                    //5 设置缓存并返回
                    cache = JSONObject.toJSONString(callLog);
                    cfRiskRedissonHandler.setEX(cacheName, cache, 1000L * 60 * 60 * 24);
                    return callLog;
                }
                return null;
            } catch (Exception e) {
                log.error("", e);
            } finally {
                if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            }
        }
    }

    private MaxFeeDiseaseCallLog handleDiseasesAndSaveLogNew(String diseases, int caseId) {
        List<String> diseaseList = List.of(StringUtils.split(diseases, ",，"));
        if (CollectionUtils.isEmpty(diseaseList)) {
            return null;
        }

        Map<String, Set<String>> normMap = diseaseNormDelegate.agent(diseaseList);
        if (MapUtils.isEmpty(normMap)) {
            return new MaxFeeDiseaseCallLog();
        }
        List<DiseaseProjectVO> fillList = new ArrayList<>();
        //业务处理
        String responseStr = handleDiseases(normMap, fillList);
        //写日志
        MaxFeeDiseaseCallLog callLog = new MaxFeeDiseaseCallLog();
        callLog.setCaseId(caseId);
        callLog.setDiseases(diseases);
        callLog.setNormDisease(JSONObject.toJSONString(normMap));
        callLog.setMaxFeeStr(JSONObject.toJSONString(fillList));
        callLog.setResponseStr(responseStr);
        maxFeeDiseaseCallLogBiz.add(callLog);
        return callLog;
    }


    private String handleDiseases(Map<String, Set<String>> normMap, List<DiseaseProjectVO> fillList){
        if (MapUtils.isEmpty(normMap)){
            return "";
        }
        //查询所有疾病
        List<String> allDiseaseList = new ArrayList<>();
        //未归一到 返回
        boolean noNorm = true;
        for (Map.Entry<String, Set<String>> entry : normMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                noNorm = false;
            }
            allDiseaseList.addAll(entry.getValue());
        }
        if (noNorm){
            return "";
        }
        List<String> mergeDiseaseList = mergeOneDiseaseList(allDiseaseList);
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(mergeDiseaseList);
        //全部为不可发起疾病 返回
        RiskDiseaseData diseaseData = riskDiseaseDataList.stream().filter(r -> r.getRaiseType() != DiseaseRaiseTypeEnum.CAN_NOT_RAISE.getCode()).findFirst().orElse(null);
        if (diseaseData == null){
            return "";
        }
        Map<String, RiskDiseaseData> diseaseMap =
                riskDiseaseDataList
                        .stream()
                        .collect(Collectors.toMap(RiskDiseaseData::getDiseaseClassName, Function.identity(), (o, n) -> n));
        //查询所有治疗方案
        Set<Long> idSet = riskDiseaseDataList.stream().map(RiskDiseaseData::getId).collect(Collectors.toSet());
        List<RiskDiseaseTreatmentProject> treatmentProjectList = treatmentProjectRpcBiz.findByDiseaseId(new ArrayList<>(idSet));
        Map<Long, List<RiskDiseaseTreatmentProject>> treatmentProjectMap =
                treatmentProjectList
                        .stream()
                        .collect(Collectors.groupingBy(RiskDiseaseTreatmentProject::getDiseaseId));

        List<DiseaseProjectVO> canNotRaiseList = new ArrayList<>();
        List<DiseaseProjectVO> raiseList = new ArrayList<>();
        List<DiseaseProjectVO> specialRaiseList = new ArrayList<>();
        normMap.forEach((k,v) ->{
            if (CollectionUtils.isEmpty(v)){
                DiseaseProjectVO projectVO = new DiseaseProjectVO();
                projectVO.setDiseaseName(k);
                canNotRaiseList.add(projectVO);
            }
            List<DiseaseProjectVO> projectVOList = v.stream().map(diseaseName -> {
                RiskDiseaseData disease = diseaseMap.get(diseaseName);
                DiseaseProjectVO projectVO = new DiseaseProjectVO();
                projectVO.setDiseaseName(k);
                if (disease == null){
                    //没归一到
                    return projectVO;
                }
                List<RiskDiseaseTreatmentProject> projects = treatmentProjectMap.get(disease.getId());
                if (CollectionUtils.isEmpty(projects)){
                    //没有治疗方案
                    return projectVO;
                }
                projectVO.setDiseaseId(disease.getId());
                RiskDiseaseTreatmentProject max = projects.stream().max(Comparator.comparing(RiskDiseaseTreatmentProject::getMaxTreatmentFee)).orElse(null);
                projectVO.setMaxFee(max == null ? 0L : max.getMaxTreatmentFee());
                projectVO.setNormName(diseaseName);
                projectVO.setRaiseType(disease.getRaiseType());
                return projectVO;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(projectVOList)) {
                DiseaseProjectVO maxVO = projectVOList.stream().filter(Objects::nonNull).max(Comparator.comparing(DiseaseProjectVO::getMaxFee)).orElse(null);
                if (maxVO != null) {
                    RiskDiseaseData.RaiseTypeEnum raiseType =
                            Optional.ofNullable(RiskDiseaseData.RaiseTypeEnum.findByCode(maxVO.getRaiseType()))
                            .orElse(RiskDiseaseData.RaiseTypeEnum.DEFAULT);
                    switch (raiseType) {
                        case CAN_RAISE:
                            raiseList.add(maxVO);
                            break;
                        case SPECIAL_RAISE:
                            specialRaiseList.add(maxVO);
                            break;
                        default:
                            canNotRaiseList.add(maxVO);
                            break;
                    }
                }
            }
        });
        if (CollectionUtils.isEmpty(raiseList) && CollectionUtils.isEmpty(canNotRaiseList)) {
            return "";
        }
        String raiseNameStr = "";
        String specialNameStr = "";
        String canNotRaiseNameStr = "";
        if (CollectionUtils.isNotEmpty(raiseList)) {
            fillList.addAll(raiseList);
            List<String> raiseNameList = raiseList.stream().sorted(Comparator.comparing(DiseaseProjectVO::getMaxFee).reversed()).map(DiseaseProjectVO::getDiseaseName).collect(Collectors.toList());
            raiseNameStr = StringUtils.join(raiseNameList, ",");
        }
        if (CollectionUtils.isNotEmpty(specialRaiseList)) {
            fillList.addAll(specialRaiseList);
            if (StringUtils.isBlank(raiseNameStr)) {
                List<String> specialRaiseNameList = specialRaiseList.stream().sorted(Comparator.comparing(DiseaseProjectVO::getMaxFee).reversed()).map(DiseaseProjectVO::getDiseaseName).collect(Collectors.toList());
                specialNameStr = "," + StringUtils.join(specialRaiseNameList, ",");
            } else {
                specialNameStr = "," + StringUtils.join(specialRaiseList.stream().map(DiseaseProjectVO::getDiseaseName).collect(Collectors.toList()), ",");
            }
        }
        if (CollectionUtils.isNotEmpty(canNotRaiseList)){
            fillList.addAll(canNotRaiseList);
            canNotRaiseNameStr = "," + StringUtils.join(canNotRaiseList.stream().map(DiseaseProjectVO::getDiseaseName).collect(Collectors.toList()), ",");
        }
        return raiseNameStr + specialNameStr + canNotRaiseNameStr;
    }

}
