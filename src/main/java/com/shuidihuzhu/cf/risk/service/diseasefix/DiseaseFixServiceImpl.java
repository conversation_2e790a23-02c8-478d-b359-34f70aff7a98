package com.shuidihuzhu.cf.risk.service.diseasefix;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountFacade;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.strategy.*;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 疾病修正逻辑
 * 需求文档 https://wdh.feishu.cn/docx/doxcnnWLqh01Gr1yN5VK33OpVYb
 * 设计文档 https://wdh.feishu.cn/wiki/wikcn8BprmsDcDTqOlssqepUz1e
 * around
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class DiseaseFixServiceImpl implements DiseaseFixService, InitializingBean, ApplicationContextAware {


    @Autowired
    private DiseaseDecideAmountFacade diseaseDecideAmountFacade;

    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;

    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Setter
    private ApplicationContext applicationContext;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    private DiseaseFixConfig diseaseFixConfig;
    private static final String KEYWORD_SPLIT_FLAG = "+";

    @Data
    public static class DiseaseFixConfig {
        private List<GroupConfig> groupList;
        private Set<String> limitWord;
    }

    @Data
    public static class GroupConfig{
        private String groupName;
        private String keywords;
        private int level;
        private ParentLogic logic;
    }

    @Value("${apollo.risk.disease-fix-json:{}}")
    public void setDiseaseFixConfig(String diseaseFixConfigJson) {
        log.info("setDiseaseFixConfig:{}", diseaseFixConfigJson);
        diseaseFixConfig = JSON.parseObject(diseaseFixConfigJson, DiseaseFixConfig.class);
    }

    private final Map<String, IStrategyHandler> typeHandlerMap = Maps.newHashMap();


    @Override
    public DiseaseDecideContext decideByFix(DiseaseDecideContext preFixContext, CrowdfundingInfo crowdfundingInfo) {
        // 修正逻辑计算
        final DecideReasonableInfo requestCopy = JSON.parseObject(JSON.toJSONString(preFixContext.getDecideReasonableInfo()), DecideReasonableInfo.class);
        DiseaseFixContext diseaseFixContext = new DiseaseFixContext();
        diseaseFixContext.setNeedFixRequest(requestCopy);
        diseaseFixContext.setResourceContext(preFixContext);

        // 使用暂存内容做检查
        final CaseInfoApproveStageDO stageInfo = caseInfoApproveStageFeignClient.getStageInfo(crowdfundingInfo.getId()).getData();
        if (stageInfo != null && StringUtils.isNotEmpty(stageInfo.getContent())) {
            crowdfundingInfo.setContent(stageInfo.getContent());
        }

        // 修正计算参数 修正方案 or 疾病 得出需要累加金额
        fixRequest(diseaseFixContext, crowdfundingInfo);

        // 使用修正后的参数重新计算
        final DiseaseDecideContext fixedContext = diseaseDecideAmountFacade.decideInfoByContext(
                crowdfundingInfo.getTargetAmount(), requestCopy);

        // 累加修正的建议花费
        final InfoReasonableAmountResultVo fixedResult = fixedContext.getResult();
        fixedResult.setAdviseMinAmount(fixedResult.getAdviseMinAmount() + diseaseFixContext.getAmountForPlusInWanYuan());
        fixedResult.setAdviseMaxAmount(fixedResult.getAdviseMaxAmount() + diseaseFixContext.getAmountForPlusInWanYuan());

        return fixedContext;
    }

    /**
     * 基于文章与原有治疗方案选择替换或新增新的方案选择
     * 基于文章与原有疾病替换新的疾病选择
     * 得出目标金额累加字段
     *
     * @param diseaseFixContext
     * @param crowdfundingInfo
     */
    private void fixRequest(DiseaseFixContext diseaseFixContext, CrowdfundingInfo crowdfundingInfo) {

        if(crowdfundingInfo == null || diseaseFixContext == null){
            return;
        }
        Set<String> limitWords = diseaseFixConfig.getLimitWord();
        // 得到过滤包含负向意图句子后的文章
        String[] contentList = StringUtils.split(crowdfundingInfo.getContent(),"。，、；：.");
        List<String> fixedContentList = Lists.newArrayList();
        for(String content : contentList){
            if(StringUtils.isEmpty(content)){
                return;
            }
            if(isHitLimitWords(limitWords, content)){
                continue;
            }
            fixedContentList.add(content);
        }
        if(CollectionUtils.isEmpty(fixedContentList)){
            return;
        }
        String fixedContent = String.join("", fixedContentList);

        List<GroupConfig> groupList = diseaseFixConfig.getGroupList();
        if(CollectionUtils.isEmpty(groupList)){
            return;
        }

        for(GroupConfig group : groupList) {

            // 判断包含关键词
            boolean hit = isHitKeywords(group, fixedContent);

            // 未命中continue
            if (!hit) {
                continue;
            }

            // 命中执行规则
            log.info("命中修正逻辑 策略标签为 {}", group.getGroupName());
            boolean needBreak = handleParentLogicWithBreakReturn(diseaseFixContext, group);
            if (needBreak) {
                return;
            }
        }
    }

    private boolean isHitLimitWords(Set<String> limitWords, String content) {
        for(String limitWord : limitWords){
            if(content.contains(limitWord)){
                return true;
            }
        }
        return false;
    }

    private boolean handleParentLogicWithBreakReturn(DiseaseFixContext diseaseFixContext,
                                                     GroupConfig group) {

        final ParentLogic parentLogic = group.getLogic();
        // 判断全等疾病
        if (StringUtils.isNotEmpty(parentLogic.getContainsDisease())) {
            handleParentContainsDisease(diseaseFixContext, group);
        }
        // 全等匹配归一后疾病的治疗方案
        if (StringUtils.isNotEmpty(parentLogic.getContainsTreatment())) {
            handleParentContainsTreatment(diseaseFixContext, group);
        }
        // 模糊匹配归一后疾病的治疗方案
        if (StringUtils.isNotEmpty(parentLogic.getContainsLikeTreatment())) {
            handleParentContainsLikeTreatment(diseaseFixContext, group);
        }
        // 全等匹配关键词
        if (StringUtils.isNotEmpty(parentLogic.getContainsKeywords())) {
            handleParentContainsKeywords(diseaseFixContext, group);
        }
        return diseaseFixContext.isNeedBreak();
    }

    private void handleParentContainsKeywords(DiseaseFixContext diseaseFixContext,
                                              GroupConfig group) {
        ParentLogic parentLogic = group.getLogic();
        doChildList(diseaseFixContext, parentLogic, true);
    }
    private void handleParentContainsLikeTreatment(DiseaseFixContext diseaseFixContext,
                                                   GroupConfig group) {

        // 获取归一后疾病
        List<String> diseaseNameList = diseaseFixContext.getResourceContext().getDecideReasonableInfo().getDiseaseNameList();
        if(CollectionUtils.isEmpty(diseaseNameList)){
            return;
        }
        // 获取所有疾病的id
        List<RiskDiseaseData> diseaseInfoList = riskDiseaseDataRpcDao.getByClassNameList(diseaseNameList);
        if(CollectionUtils.isEmpty(diseaseInfoList)){
            return;
        }
        List<Long> diseaseIds = diseaseInfoList.stream().map(RiskDiseaseData::getId).collect(Collectors.toList());
        // 获取所有疾病的治疗方案
        List<RiskDiseaseTreatmentProject> treatmentList = riskDiseaseTreatmentProjectRpcDao.findByDiseaseIdList(diseaseIds);
        String treatmentNames = treatmentList.stream().map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.joining("#"));

        ParentLogic parentLogic = group.getLogic();
        // 模糊判断是否包含配置中的治疗方案
        boolean yes = StringUtils.contains(treatmentNames, parentLogic.getContainsLikeTreatment());

        doChildList(diseaseFixContext, parentLogic, yes);
    }

    private void handleParentContainsTreatment(DiseaseFixContext diseaseFixContext,
                                               GroupConfig group) {

        // 获取归一后疾病
        List<String> diseaseNameList = diseaseFixContext.getResourceContext().getDecideReasonableInfo().getDiseaseNameList();
        if(CollectionUtils.isEmpty(diseaseNameList)){
            return;
        }
        // 获取所有疾病的治疗方案
        List<RiskDiseaseData> diseaseInfoList = riskDiseaseDataRpcDao.getByClassNameList(diseaseNameList);
        if(CollectionUtils.isEmpty(diseaseInfoList)){
            return;
        }
        List<Long> diseaseIds = diseaseInfoList.stream().map(RiskDiseaseData::getId).collect(Collectors.toList());
        // 获取所有疾病的治疗方案
        List<RiskDiseaseTreatmentProject> treatmentList = riskDiseaseTreatmentProjectRpcDao.findByDiseaseIdList(diseaseIds);
        Set<String> treatmentNames = treatmentList.stream().map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.toSet());
        ParentLogic parentLogic = group.getLogic();
        List<String> containsTreatments = Splitter.on(",").splitToList(parentLogic.getContainsTreatment());
        boolean yes = false;
        // 判断是否命中配置中的治疗方案
        for(String treatment : containsTreatments){
            yes = treatmentNames.contains(treatment);
            if(yes){
                break;
            }
        }
        doChildList(diseaseFixContext, parentLogic, yes);
    }

    private void doChildList(DiseaseFixContext diseaseFixContext, ParentLogic parentLogic, boolean yes) {
        final List<ChildLogic> childList = yes ? parentLogic.getYesDo() : parentLogic.getNoDo();
        for (ChildLogic logic : childList) {
            handleLogic(logic, diseaseFixContext);
        }
    }

    private void handleParentContainsDisease(DiseaseFixContext diseaseFixContext,
                                             GroupConfig group) {
        InfoReasonableAmountResultVo resourceResult = diseaseFixContext.getResourceContext().getResult();
        final List<DiseaseInfoVo> diseaseInfoList = resourceResult.getDiseaseInfoList();
        if (CollectionUtils.isEmpty(diseaseInfoList)) {
            return;
        }
        final ParentLogic parentLogic = group.getLogic();
        final Set<String> diseaseSet = diseaseInfoList
                .stream().map(DiseaseInfoVo::getDiseaseName)
                .collect(Collectors.toSet());
        final String containsDisease = parentLogic.getContainsDisease();
        final boolean yes = Arrays.stream(StringUtils.split(containsDisease, ","))
                .anyMatch(diseaseSet::contains);

        doChildList(diseaseFixContext, parentLogic, yes);
    }

    private boolean isHitKeywords(GroupConfig group, String fixedContent) {
        Set<String> keywords = Sets.newHashSet(Splitter.on(",").splitToList(group.getKeywords()));
        for (String keyword : keywords) {
            boolean hit = checkHitKeyword(keyword, fixedContent);
            if (hit) {
                return true;
            }
        }
        return false;
    }

    private boolean checkHitKeyword(String keyword, String fixedContent) {
        if (StringUtils.contains(keyword, KEYWORD_SPLIT_FLAG)){
            // 包含+的情况
            final String[] split = StringUtils.split(keyword, "+");
            for (String s : split) {
                final boolean hit = StringUtils.contains(fixedContent, s);
                if (!hit) {
                    return false;
                }
            }
            return true;
        } else {
            // 不包含+的情况
            return StringUtils.contains(fixedContent, keyword);
        }
    }

    private void handleLogic(ChildLogic logic, DiseaseFixContext diseaseFixContext) {
        final IStrategyHandler handler = typeHandlerMap.get(logic.getDoType());
        if (handler == null) {
            throw new RuntimeException("doType 未支持 " + logic.getDoType());
        }
        handler.process(logic, diseaseFixContext);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, IStrategyHandler> beansOfTypeMap = applicationContext.getBeansOfType(IStrategyHandler.class);
        if (MapUtils.isEmpty(beansOfTypeMap)) {
            return;
        }
        beansOfTypeMap.values()
                .forEach(v -> {
                    final String doType = v.getDoType();
                    typeHandlerMap.put(doType, v);
                });
    }

}
