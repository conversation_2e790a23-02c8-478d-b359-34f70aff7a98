package com.shuidihuzhu.cf.risk.service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.impl.disease.DiseaseCommonConfig;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseAmountSpecialInfo;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseRaiseTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseBurnEnum;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DiseaseBurnService {

    @Autowired
    private DiseaseDecideAmountService diseaseDecideAmountService;

    public void handle(DiseaseDecideContext diseaseDecideContext) {
        List<RiskDiseaseData> allRiskDiseaseDataList = diseaseDecideContext.getAllDiseaseDataList();
        DecideReasonableInfo decideReasonableInfo = diseaseDecideContext.getDecideReasonableInfo();
        InfoReasonableAmountResultVo result = diseaseDecideContext.getResult();

        final List<String> diseaseNameList = allRiskDiseaseDataList.stream().map(RiskDiseaseData::getDiseaseClassName)
                .collect(Collectors.toList());
        if (!diseaseNameList.contains(DiseaseCommonConfig.BURN_NAME)) {
            return;
        }
        final List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos =
                diseaseDecideAmountService.getChoiceList(decideReasonableInfo.getSpecialDiseaseInfo());
        //兼容为空的情况
        List<BurnInfo> burnInfos = fullInfoVos(specialChoiceInfoVos, decideReasonableInfo);
        final Pair<Double, Double> burnAmount = getBurnAmount(burnInfos);
        final String treatmentInfo = getTreatmentInfo(burnInfos);
        setBurnOtherInfo(result, treatmentInfo);

        final DiseaseAmountSpecialInfo diseaseAmountSpecialInfo = DiseaseAmountSpecialInfo.create(
                burnAmount.getLeft() * 100,
                burnAmount.getRight() * 100,
                true,
                DiseaseCommonConfig.BURN_NAME,
                RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode(),
                treatmentInfo
        );
        addTreatMethodToJudgeDiseaseList(diseaseDecideContext.getJudgeDiseaseInfoList(), treatmentInfo, diseaseAmountSpecialInfo);
        diseaseDecideContext.getDiseaseAmountInfoList().add(diseaseAmountSpecialInfo);
        diseaseDecideContext.getDiseaseAmountInfoAllList().add(diseaseAmountSpecialInfo);

    }

    private void addTreatMethodToJudgeDiseaseList(List<JudgeDiseaseInfoVO> judgeDiseaseInfoVOList, String treatmentInfo, DiseaseAmountSpecialInfo diseaseAmountSpecialInfo) {
        List<String> treatMethodList = Splitter.on("<br>").splitToList(treatmentInfo);
        for(JudgeDiseaseInfoVO judgeDiseaseInfo : judgeDiseaseInfoVOList){
            if(judgeDiseaseInfo.getDiseaseClassName().equals(DiseaseCommonConfig.BURN_NAME)){
                judgeDiseaseInfo.setTreatMethod(String.valueOf(treatMethodList));
                judgeDiseaseInfo.setRaise(DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode());
                judgeDiseaseInfo.setMatchMinAmount(diseaseAmountSpecialInfo.getMinAmount() / 100.0);
                judgeDiseaseInfo.setMatchMaxAmount(diseaseAmountSpecialInfo.getMaxAmount() / 100.0);
            }
        }
    }


    public List<BurnInfo> fullInfoVos(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfo,
                                      DecideReasonableInfo decideReasonableInfo) {

        List<BurnInfo> diseaseSpecialInfoVos = Lists.newArrayList();

        if (CollectionUtils.isEmpty(specialChoiceInfo)) {
            return getBurnInfosByFix(diseaseSpecialInfoVos);
        }
        //获取烧伤的特殊信息
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos =
                diseaseDecideAmountService.filterSpecialChoice(specialChoiceInfo, List.of(DiseaseCommonConfig.BURN_NAME));
        //移除已经处理的特殊值
        specialChoiceInfo.removeAll(specialDiseaseChoiceInfos);
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return Lists.newArrayList();
        }
        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : specialDiseaseChoiceInfos) {
            List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList = specialDiseaseChoiceInfo.getChoiceInfoList();
            if (CollectionUtils.isEmpty(choiceInfoList)) {
                return Lists.newArrayList();
            }
            for (SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo : choiceInfoList) {
                DiseaseTreamentChoiceTypeEnum choiceType = DiseaseTreamentChoiceTypeEnum.findByCode(choiceInfo.getChoiceType());
                if (choiceType == null || choiceType == DiseaseTreamentChoiceTypeEnum.DEFAULT) {
                    return Lists.newArrayList();
                }
                BurnInfo diseaseSpecialInfoVo = new BurnInfo();
                diseaseSpecialInfoVo.setSpecialDiseaseName(specialDiseaseChoiceInfo.getSpecialDiseaseName());
                diseaseSpecialInfoVo.setBurnArea(Integer.parseInt(StringUtils.isBlank(choiceInfo.getInputValue()) ? "0" : choiceInfo.getInputValue()));
                diseaseSpecialInfoVo.setBurnLevel(SpecialDiseaseBurnEnum.findByDesc(choiceInfo.getChoiceName()).getCode());
                // 判断是否被修正为特重度烧伤
                 boolean choiceSpecialBurn = false;
                if(choiceInfo.getSpecialBurn() != null){
                    choiceSpecialBurn = choiceInfo.getSpecialBurn();
                }
                diseaseSpecialInfoVo.setVerySevere(decideReasonableInfo.isSpecialBurn() ? true : choiceSpecialBurn);
//                diseaseSpecialInfoVo.setVerySevere(choiceInfo.getSpecialBurn());
                diseaseSpecialInfoVos.add(diseaseSpecialInfoVo);
            }
        }

        // 如果list空 且 判断被修正为特重度烧伤，手动添加
        if(decideReasonableInfo.isSpecialBurn() && diseaseSpecialInfoVos.size() == 0){
            return getBurnInfosByFix(diseaseSpecialInfoVos);
        }

        return diseaseSpecialInfoVos;
    }

    @NotNull
    private List<BurnInfo> getBurnInfosByFix(List<BurnInfo> diseaseSpecialInfoVos) {
        BurnInfo diseaseSpecialInfoVo = new BurnInfo();
        diseaseSpecialInfoVo.setSpecialDiseaseName("烧伤");
        // 判断是否被修正为特重度烧伤
        diseaseSpecialInfoVo.setVerySevere(true);
        diseaseSpecialInfoVo.setBurnLevel(SpecialDiseaseBurnEnum.SPECIAL_BURN.getCode());

        diseaseSpecialInfoVos.add(diseaseSpecialInfoVo);
        return diseaseSpecialInfoVos;
    }

    private void setBurnOtherInfo(InfoReasonableAmountResultVo resultVo, String treatmentInfo) {
        if (StringUtils.isEmpty(treatmentInfo)){
            return;
        }
        String builder = "其他信息：" + "<br>" +
                "疾病名称：" + DiseaseCommonConfig.BURN_NAME + "<br>" +
                treatmentInfo;
        resultVo.setOtherInfo(builder);
    }

    private String getTreatmentInfo(List<BurnInfo> burnInfos) {
        StringBuilder builder = new StringBuilder();
        for (BurnInfo infoVo : burnInfos) {
            builder.append("烧伤等级：").append(SpecialDiseaseBurnEnum.findByCode(infoVo.getBurnLevel()).getDesc());
            if (infoVo.getBurnLevel() == SpecialDiseaseBurnEnum.SPECIAL_BURN.getCode()) {
                builder.append("   是否是特重度烧伤：").append(BooleanUtils.toBoolean(infoVo.getVerySevere()) ? "是" : "否").append("<br>");
            } else {
                builder.append("   烧伤面积：").append(infoVo.getBurnArea()).append("<br>");
            }
        }
        return builder.toString();
    }

    private Pair<Double, Double> getBurnAmount(List<BurnInfo> burnInfos) {
        double min = 0;
        double max = 0;
        for (BurnInfo burnInfo : burnInfos) {
            if (burnInfo.getVerySevere() != null && burnInfo.getVerySevere()) {
                return Pair.of(25D, 40D);
            }
            final int burnLevel = burnInfo.getBurnLevel();
            if (burnLevel == 1) {
                final double amount = burnInfo.getBurnArea() * 0.5;
                min += amount;
                max += amount;
                continue;
            }
            if (burnLevel == 2) {
                final double amount = burnInfo.getBurnArea();
                min += amount;
                max += amount;
            }
        }
        //如果全部没写面积则烧伤金额为5
        if (min == 0) {
            return Pair.of(5D,5D);
        }
        return Pair.of(min, max);
    }

    private Pair<Double, Double> getBurnAmount(BurnInfo burnInfo) {
        final int burnArea = burnInfo.getBurnArea();
        if(burnArea <=0 || burnArea > 100) {
            return Pair.of(0D,0D);
        }
        final int burnLevel = burnInfo.getBurnLevel();
        if (burnLevel == 1) {
            final double amount = burnArea * 0.5;
            return Pair.of(amount, amount);
        }
        if (burnLevel == 2) {
            final double amount = burnArea;
            return Pair.of(amount, amount);
        }
        return Pair.of(0D, 0D);
    }


    @Data
    @ApiModel("特殊疾病名称Vo")
    public class BurnInfo {


        /**
         * 疾病名称
         * 1 ：烧伤
         */
        @ApiModelProperty("特殊疾病类型名称")
        private String specialDiseaseName;

        /**
         * 疾病名称
         */
        @ApiModelProperty("烧伤等级")
        private int burnLevel;

        /**
         * 疾病名称
         */
        @ApiModelProperty("烧伤面积")
        private int burnArea;

        @Nullable
        @ApiModelProperty("特重度烧伤")
        private Boolean verySevere;

    }
}