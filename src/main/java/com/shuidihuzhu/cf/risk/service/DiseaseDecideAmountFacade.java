package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DiseaseDecideAmountFacade {

    Response<InfoReasonableAmountResultVo> decideInfoAmountReasonable(int targetAmount,
                                                                      DecideReasonableInfo decideReasonableInfo);

    DiseaseDecideContext decideInfoByContext(int targetAmount,
                                             DecideReasonableInfo decideReasonableInfo);
}
