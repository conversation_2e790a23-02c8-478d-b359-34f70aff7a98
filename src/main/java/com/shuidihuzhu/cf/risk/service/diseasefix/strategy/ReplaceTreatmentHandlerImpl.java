package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.biz.impl.disease.DiseaseCommonConfig;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.PrePoseSpecialDiseaseChoiceInfo;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.risk.service.DiseaseBurnService;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplaceTreatmentHandlerImpl implements IStrategyHandler {

    @Resource
    private DiseaseDecideAmountService diseaseDecideAmountService;

    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {


        String treatmentNameForReplace = logic.getTreatmentNameForReplace();
        DecideReasonableInfo needFixRequest = diseaseFixContext.getNeedFixRequest();

        if("特重度烧伤".equals(treatmentNameForReplace)){
            needFixRequest.setSpecialBurn(true);
        }

        // 获取归一后疾病
        List<String> diseaseNameList = diseaseFixContext.getResourceContext().getDecideReasonableInfo().getDiseaseNameList();

        // 所有疾病治疗方案选项信息
        final Map<String, List<Long>> getAllChoiceMap = diseaseDecideAmountService.getAllChoiceMap(needFixRequest);
        Map<String, List<Long>> fixChoiceMap = Maps.newHashMap();
        fixChoiceMap.putAll(getAllChoiceMap);

        // 替换原有治疗方案
        for(String disease : diseaseNameList){
            RiskDiseaseData diseaseInfo = riskDiseaseDataRpcDao.getByClassName(disease);
            if(diseaseInfo == null){
                continue;
            }
            List<RiskDiseaseTreatmentProject> treatmentByDisease =
                    riskDiseaseTreatmentProjectRpcDao.findByDiseaseIdList(Lists.newArrayList(diseaseInfo.getId()));
            if(CollectionUtils.isEmpty(treatmentByDisease)){
                continue;
            }
            List<RiskDiseaseTreatmentProject> list = treatmentByDisease.stream().filter(v -> v.getProjectName().equals(treatmentNameForReplace)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            fixChoiceMap.put(disease, list.stream().map(RiskDiseaseTreatmentProject::getId).collect(Collectors.toList()));

        }
        needFixRequest.setGetAllChoiceMap(fixChoiceMap);
        diseaseFixContext.setNeedFixRequest(needFixRequest);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "replaceTreatment";
    }
}
