package com.shuidihuzhu.cf.risk.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.risk.dao.DiscussionCheckRecordDao;
import com.shuidihuzhu.cf.risk.dao.DiscussionDao;
import com.shuidihuzhu.cf.risk.model.*;
import com.shuidihuzhu.cf.risk.model.enums.DiscussionStatusEnum;
import com.shuidihuzhu.cf.risk.model.enums.RiskRpcErrorCode;
import com.shuidihuzhu.cf.risk.service.DiscussionService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class DiscussionServiceImpl implements DiscussionService {

    @Autowired
    private DiscussionDao discussionDao;
    @Autowired
    private DiscussionCheckRecordDao checkRecordDao;

    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;

    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    private static final String DISCUSSION_KEY = "DISCUSSION_";

    public Discussion getByCaseId(int caseId) {
        if (caseId <= 0 ) {
            return null;
        }
        return discussionDao.getByCaseId(caseId);
    }

    public Response<DiscussionVO> getDiscussionInnerInfo(int caseId) {
        Discussion discussion = getByCaseId(caseId);
        if (discussion == null) {
            return NewResponseUtil.makeError(RiskRpcErrorCode.DISCUSSION_NOT_EXIST);
        }
        DiscussionVO discussionVO = new DiscussionVO();
        log.info("closeTime:{} currentTime:{}" , discussion.getCloseTime().getTime(), System.currentTimeMillis());
        discussionVO.setDiscussionEndTime(discussion.getCloseTime().getTime()
                - System.currentTimeMillis());
        discussionVO.setDiscussionStatus(DiscussionStatusEnum.getDisscussionStatus(discussion));
        discussionVO.setCaseId(caseId);
        return NewResponseUtil.makeSuccess(discussionVO);
    }

    @Override
    public Response<List<DiscussionVO>> findDiscussionInfoByCaseId(List<Integer> caseIdList) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseIdList);
        List<DiscussionVO> discussionVOS = Lists.newArrayList();
        for (Discussion discussion : discussions) {
            DiscussionVO discussionVO = new DiscussionVO();
            discussionVO.setCaseId(discussion.getCaseId());
            discussionVO.setDiscussionStatus(DiscussionStatusEnum.getDisscussionStatus(discussion));
            discussionVO.setDiscussionEndTime(discussion.getCloseTime().getTime());
            discussionVOS.add(discussionVO);
        }
        if (log.isDebugEnabled()){
            log.debug("discussionVOS:{}", JSON.toJSONString(discussionVOS));
        }
        return NewResponseUtil.makeSuccess(discussionVOS);
    }

    @Override
    public DiscussionDTO findByCaseId(int caseId) {
        DiscussionDTO discussionDTO = cfRiskRedissonHandler.get(DISCUSSION_KEY + caseId, DiscussionDTO.class);
        if (discussionDTO == null){
            log.info("findByCaseId not hit caseId:{}", caseId);
            return findInfoByCaseId(caseId);
        }
        log.info("findByCaseId hit caseId:{}", caseId);
        return discussionDTO;
    }

    private DiscussionDTO findInfoByCaseId(int caseId){
        Discussion discussion = discussionDao.getByCaseId(caseId);
        if (discussion == null){
            return null;
        }
        long discussionId = discussion.getId();
        long closeTime = discussion.getCloseTime().getTime();
        long currentTime = System.currentTimeMillis();
        //如果已经过期就关闭评议
        if (discussion.getStatus() == 1
                && discussion.getCheckStatus() == 2
                && closeTime < System.currentTimeMillis()
                && closeTime > 946656000000L) {
            String infoUuid = discussion.getInfoUuid();
            try {
                log.info("案例恢复打款 infoUuId:{}, caseId:{}", infoUuid, caseId);
                //恢复打款
                com.shuidihuzhu.cf.finance.client.response.FeignResponse response =
                        cfFinancePauseFeignClient.recoverBySourceType(infoUuid, caseId, 11,
                                CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(), CfOperatingRecordEnum.Role.OPERATOR.getCode(),
                                102, "系统", "评议通道关闭");
                log.info("案例恢复打款 responseCode:{} responseMsg:{},infoUuId:{}, caseId:{}",
                        response == null ? -1 : response.getCode(),
                        response == null ? "" : response.getMsg(), infoUuid, caseId);
                if (response != null && response.notOk()) {
                    return null;
                }
            } catch (Exception e) {
                log.error("", e);
            }
            log.info("discussion is invalid discussionId:{}, closeTime:{}, currentTime:{}", discussionId, closeTime, currentTime);
            int result = discussionDao.updateStatus(2, 1, discussionId);
            if (result > 0) {
                discussion.setStatus(2);
            }
        }
        DiscussionCheckRecord lastRecord = checkRecordDao.findLastByDiscussId(discussionId);
        if (lastRecord != null) {
            discussion.setOperator(lastRecord.getOperator());
            if (lastRecord.getStatus() == 3) {
                discussion.setRefuseReason(lastRecord.getRefuseReason());
            }
        }
        int applyTime = discussionDao.countByCaseId(caseId);
        discussion.setApplyTime(applyTime);
        DiscussionDTO discussionDTO = discussion.convertDiscussionDTO();
        if (closeTime > 946656000000L) {
            long leftTime = closeTime - currentTime;
            if (leftTime > 0 && leftTime <= RedissonHandler.ONE_HOUR) {
                cfRiskRedissonHandler.setEX(DISCUSSION_KEY + caseId, discussionDTO, closeTime - currentTime);
            }else {
                cfRiskRedissonHandler.setEX(DISCUSSION_KEY + caseId, discussionDTO, RedissonHandler.ONE_HOUR);
            }
        }
        return discussionDTO;

    }

    @Override
    public Boolean hasDiscussion(int caseId) {
       Discussion discussion = discussionDao.getByCaseId(caseId);
        if (discussion == null) {
            return false;
        }
        return discussion.getStatus() == 1;
    }
}
