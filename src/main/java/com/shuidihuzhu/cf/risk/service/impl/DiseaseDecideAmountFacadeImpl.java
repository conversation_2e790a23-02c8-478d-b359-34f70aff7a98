package com.shuidihuzhu.cf.risk.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseAmountCallLogBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.impl.disease.DiseaseCommonConfig;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.*;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.JudgeDiseaseInfoVO;
import com.shuidihuzhu.cf.risk.service.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class DiseaseDecideAmountFacadeImpl implements DiseaseDecideAmountFacade {

    @Autowired
    private DiseaseAmountCallLogBiz diseaseAmountCallLogBiz;

    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;

    @Autowired
    private DiseaseDecideAmountService diseaseDecideAmountService;

    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz treatmentProjectRpcBiz;

    @Autowired
    private DiseaseBurnService diseaseBurnService;

    @Autowired
    private DiseaseRpcService diseaseRpcService;

    @Autowired
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    private int diseaseJudgeAmountListLimit;

    private DiseaseConfig diseaseConfig;

    /*
    所有特殊疾病名称
     */
    private Set<String> specialDiseaseSet;

    private Set<String> possibleKeys = Sets.newHashSet("?", "？", "可能", "疑似", "待查");
    private static final String TAG = "疾病目标金额计算";
    private static final Integer IN = 1;
    private static final Integer NOT_IN = 2;
    private static final Integer DEFAULT = -1;

    @Value("${disease.special-config:{}}")
    public void setDiseaseConfig(String configJson) {
        log.info("setDiseaseConfig:{}", configJson);
        diseaseConfig = JSON.parseObject(configJson, DiseaseConfig.class);
        diseaseJudgeAmountListLimit = diseaseConfig.getJudgeAmountListLimit();
        this.specialDiseaseSet = diseaseConfig.getSpecialDiseaseClassList()
                .stream()
                .flatMap(v -> v.getList().stream())
                .collect(Collectors.toSet());
    }

    @Data
    public static class DiseaseConfig{

        private List<DiseaseClass> specialDiseaseClassList;
        private int judgeAmountListLimit;

    }

    @Data
    public static class DiseaseClass {
        private String desc;
        private Set<String> list;
        @ApiModelProperty("{true: 占用前n疾病名额, false 不占位，独立计算}")
        private Boolean occupyPlace;
    }


    @Override
    public Response<InfoReasonableAmountResultVo> decideInfoAmountReasonable(int targetAmount,
                                                                             DecideReasonableInfo decideReasonableInfo) {
        final DiseaseDecideContext context = decideInfoByContext(targetAmount, decideReasonableInfo);
        return NewResponseUtil.makeSuccess(context.getResult());
    }

    @Override
    public DiseaseDecideContext decideInfoByContext(int targetAmount,
                                                    DecideReasonableInfo decideReasonableInfo) {

        log.info("{} 计算开始 targetAmount {}, param {}", TAG, targetAmount, decideReasonableInfo);
        List<JudgeDiseaseInfoVO> judgeDiseaseInfoList = Lists.newArrayList();
        List<String> submitDiseaseNames = decideReasonableInfo.getDiseaseResourceList();
        // 疾病名写入展示列表
        addSubmitAndClassDiseaseToJudgeDiseaseList(submitDiseaseNames, judgeDiseaseInfoList);
        List<String> mergeResult = mergeOneDiseaseList(decideReasonableInfo.getDiseaseNameList());
        InfoReasonableAmountResultVo result = new InfoReasonableAmountResultVo();
        //写入金额
        result.setTargetAmountInFen(targetAmount);
        result.setTreatmentInfo("");

        List<RiskDiseaseData> allRiskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(mergeResult);
        log.info("{} 匹配疾病库中疾病 入参疾病 {}, 匹配结果 {}", TAG, mergeResult, allRiskDiseaseDataList);

        final DiseaseDecideContext diseaseDecideContext = new DiseaseDecideContext();
        diseaseDecideContext.setDecideReasonableInfo(decideReasonableInfo);
        diseaseDecideContext.setAllDiseaseDataList(allRiskDiseaseDataList);
        diseaseDecideContext.setResult(result);
        diseaseDecideContext.setDiseaseAmountInfoAllList(Sets.newHashSet());
        diseaseDecideContext.setDiseaseAmountInfoList(Sets.newHashSet());
        diseaseDecideContext.setDiseaseAmountInfoNoPlaceList(Sets.newHashSet());
        diseaseDecideContext.setJudgeDiseaseInfoList(judgeDiseaseInfoList);
        // 列表返回合并症信息
        diseaseDecideContext.setComplications(Lists.newArrayList());

        // 处理特殊疾病
        handleSpecialDisease(diseaseDecideContext);

        // 处理普通疾病
        handleNormalDisease(diseaseDecideContext);

        // 处理结果
        return handleContext(targetAmount, decideReasonableInfo, diseaseDecideContext);
    }

    @NotNull
    private DiseaseDecideContext handleContext(int targetAmount,
                                               DecideReasonableInfo decideReasonableInfo,
                                               DiseaseDecideContext diseaseDecideContext) {
        // 计算所有疾病对应方案最大金额并排序 取前n个疾病
        final List<IDiseaseAmountInfo> amountList = diseaseDecideContext.getDiseaseAmountInfoList()
                .stream()
                .sorted(
                        Comparator.comparing(IDiseaseAmountInfo::getMinAmount)
                                .thenComparing(IDiseaseAmountInfo::getMinAmount)
                                .reversed()
                )
                .limit(diseaseJudgeAmountListLimit)
                .collect(Collectors.toList());

        int minAmount = 0;
        int maxAmount = 0;

        // 参与计算的疾病集合
        final HashSet<String> calculateDiseaseNamesSet = Sets.newHashSet();
        for (IDiseaseAmountInfo iDiseaseAmountInfo : amountList) {
            minAmount += iDiseaseAmountInfo.getMinAmount();
            maxAmount += iDiseaseAmountInfo.getMaxAmount();
            calculateDiseaseNamesSet.add(iDiseaseAmountInfo.getDiseaseClassName());
        }
        // 计算所有疾病对应方案最大金额并排序 取前n个疾病
        for (IDiseaseAmountInfo iDiseaseAmountInfo : diseaseDecideContext.getDiseaseAmountInfoNoPlaceList()) {
            minAmount += iDiseaseAmountInfo.getMinAmount();
            maxAmount += iDiseaseAmountInfo.getMaxAmount();
            calculateDiseaseNamesSet.add(iDiseaseAmountInfo.getDiseaseClassName());
        }
        // 更新列表疾病是否算入总花费
        for (JudgeDiseaseInfoVO diseaseInfo : diseaseDecideContext.getJudgeDiseaseInfoList()){
            diseaseInfo.setInTotalAmount(calculateDiseaseNamesSet.contains(diseaseInfo.getDiseaseClassName()) ? IN : NOT_IN);
            if(diseaseInfo.getRaise() != RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()){
                diseaseInfo.setInTotalAmount(NOT_IN);
            }
        }
        final InfoReasonableAmountResultVo result = diseaseDecideContext.getResult();
        // 设置参与计算的疾病
        result.setCalculateDiseaseNames(StringUtils.join(calculateDiseaseNamesSet, ","));

        //匹配金额
        result.setMatchMinAmount(result.getMatchMinAmount() + minAmount / 100.0);
        result.setMatchMaxAmount(result.getMatchMaxAmount() + maxAmount / 100.0);

        // 设置全部治疗信息
        Map<String, DiseaseInfoVo> diseaseNameInfoMap =
                getDiseaseNameInfoVoMap(diseaseDecideContext.getDiseaseAmountInfoAllList());
        final Collection<DiseaseInfoVo> values = diseaseNameInfoMap.values();
        result.setTreatmentInfo(getTreatmentInfo(values));

        // 全部疾病
        result.getDiseaseInfoList().addAll(diseaseNameInfoMap.values());

        // 最终建议金额
        fullAdviseMinAmount(targetAmount, result);

        result.setCanRaiseStatus(getCanRaise(result.getDiseaseInfoList()));
        //记录请求以及结果
        diseaseAmountCallLogBiz.saveLog(result, decideReasonableInfo, targetAmount);

        log.info("{} 金额计算完成 取前几个疾病 {}, 参与计算占位列表 {}\n, 参与计算不占位列表 {}\n, context {}", TAG,
                diseaseJudgeAmountListLimit, JSON.toJSONString(amountList),
                JSON.toJSONString(diseaseDecideContext.getDiseaseAmountInfoNoPlaceList()),
                JSON.toJSONString(diseaseDecideContext));


        result.setComplications(diseaseDecideContext.getComplications().stream().distinct().collect(Collectors.toList()));
        // 展示疾病列表
        result.setJudgeDiseaseInfoList(diseaseDecideContext.getJudgeDiseaseInfoList().
                stream().sorted(Comparator.comparing(JudgeDiseaseInfoVO::getDiseaseName)).collect(Collectors.toList()));
        return diseaseDecideContext;
    }

    private void addSubmitAndClassDiseaseToJudgeDiseaseList(List<String> submitDiseaseNames, List<JudgeDiseaseInfoVO> judgeDiseaseInfoList) {
        if(CollectionUtils.isEmpty(submitDiseaseNames)){
            return;
        }
        // 查询用户输入疾病的归一疾病
        List<DiseaseClassifyVOV2> diseaseClassNameList = diseaseRpcService.diseaseNormNew(submitDiseaseNames);
        Map<String, DiseaseClassifyVOV2> diseaseClassNameMap = diseaseClassNameList.stream()
                .collect(Collectors.toMap(DiseaseClassifyVOV2::getDisease, Function.identity(), (o1, o2) -> o2));
        for(String submitDisease : submitDiseaseNames){
            JudgeDiseaseInfoVO judgeDiseaseInfo = new JudgeDiseaseInfoVO();
            judgeDiseaseInfo.setDiseaseName(submitDisease);
            DiseaseClassifyVOV2 diseaseClassName = diseaseClassNameMap.get(submitDisease);
            if(diseaseClassName == null){
                judgeDiseaseInfoList.add(judgeDiseaseInfo);
                continue;
            }
            String className = StringUtils.strip(diseaseClassName.getNorm().toString(), "[]");
            judgeDiseaseInfo.setDiseaseClassName(StringUtils.trim(className));
            RiskDiseaseData diseaseSystemData = riskDiseaseDataRpcDao.getByClassName(className);
            if (diseaseSystemData == null){
                judgeDiseaseInfoList.add(judgeDiseaseInfo);
                continue;
            }
            judgeDiseaseInfo.setInDiseaseSystem(IN);
            judgeDiseaseInfo.setRaise(diseaseSystemData.getRaiseType() ==
                    RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() ? RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() : DEFAULT);
            if(judgeDiseaseInfo.getRaise() == DEFAULT){
                judgeDiseaseInfo.setRaise(diseaseSystemData.getRaiseType() ==
                        RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode() ? RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode() : DEFAULT);
            }
            judgeDiseaseInfoList.add(judgeDiseaseInfo);
        }
    }

    private String getTreatmentInfo(Collection<DiseaseInfoVo> values) {
        StringBuilder treatmentStringBuilder = new StringBuilder();
        for (DiseaseInfoVo value : values) {
            final String t = String.format("【%s】:%s<br>", value.getDiseaseName(), value.getTreatmentInfo());
            treatmentStringBuilder.append(t);
        }
        return treatmentStringBuilder.toString();
    }

    @NotNull
    private Map<String, DiseaseInfoVo> getDiseaseNameInfoVoMap(Collection<IDiseaseAmountInfo>  diseaseAmountInfoAllList) {
        Map<String, DiseaseInfoVo> diseaseNameInfoMap = Maps.newHashMap();
        for (IDiseaseAmountInfo iDiseaseAmountInfo : diseaseAmountInfoAllList) {
            final DiseaseInfoVo oldValue = diseaseNameInfoMap.get(iDiseaseAmountInfo.getDiseaseClassName());
            if (oldValue == null) {
                final DiseaseInfoVo newValue = createInfoByDiseaseAmountInfo(iDiseaseAmountInfo);
                diseaseNameInfoMap.put(iDiseaseAmountInfo.getDiseaseClassName(), newValue);
                continue;
            }
            if (StringUtils.isBlank(iDiseaseAmountInfo.getTreatmentInfo())){
                continue;
            }
            oldValue.setTreatmentInfo(oldValue.getTreatmentInfo() + "+" + iDiseaseAmountInfo.getTreatmentInfo());
        }
        return diseaseNameInfoMap;
    }

    private DiseaseInfoVo createInfoByDiseaseAmountInfo(IDiseaseAmountInfo iDiseaseAmountInfo) {
        final DiseaseInfoVo v = new DiseaseInfoVo();
        v.setDiseaseName(iDiseaseAmountInfo.getDiseaseClassName());
        v.setDiseaseType(iDiseaseAmountInfo.getRaiseType());
        v.setTreatmentInfo(iDiseaseAmountInfo.getTreatmentInfo());
        v.setCalculateSpecialStatus(false);
        return v;
    }

    private void handleSpecialDisease(DiseaseDecideContext diseaseDecideContext) {
        final List<DiseaseClass> specialDiseaseClassList = diseaseConfig.getSpecialDiseaseClassList();

        // 骨折 癌症 等其他分类(合并症逻辑)
        for (DiseaseClass diseaseClass : specialDiseaseClassList) {
            handleSpecialClassDisease(diseaseDecideContext,
                    diseaseClass.getList(),
                    diseaseClass.getOccupyPlace());
        }

        // 烧伤
        diseaseBurnService.handle(diseaseDecideContext);

        // 不确诊疾病 对于录入疾病中包含“？/可能/疑似/待查”的疾病，其花费直接给2~3w
//        for (String diseaseName : diseaseDecideContext.getDecideReasonableInfo().getDiseaseNameList()) {
//            if (isPossible(diseaseName)){
//                final DiseaseAmountSpecialInfo d = DiseaseAmountSpecialInfo.create(2D, 3D,
//                        true, diseaseName, RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode());
//                diseaseDecideContext.getDiseaseAmountInfoAllList().add(d);
//                diseaseDecideContext.getDiseaseAmountInfoNoPlaceList().add(d);
//            }
//        }
    }

    private boolean isPossible(String diseaseName) {
        for (String possibleKey : possibleKeys) {
            if (diseaseName.contains(possibleKey)) {
                return true;
            }
        }
        return false;
    }

    private void handleSpecialClassDisease(DiseaseDecideContext diseaseDecideContext,
                                           Set<String> specialDiseaseSet,
                                           boolean occupyPlace) {
        List<RiskDiseaseData> allRiskDiseaseDataList = diseaseDecideContext.getAllDiseaseDataList();
        final List<String> complications = diseaseDecideContext.getComplications();
        final List<JudgeDiseaseInfoVO> judgeDiseaseInfoList = diseaseDecideContext.getJudgeDiseaseInfoList();
        final List<RiskDiseaseData> specialDiseaseList = allRiskDiseaseDataList.stream()
                .filter(v -> specialDiseaseSet.contains(v.getDiseaseClassName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specialDiseaseList)) {
            return;
        }
        // 展示列表增加合并症
        addComplicationsToJudgeDiseaseList(complications, specialDiseaseList);
        final Map<Long, RiskDiseaseData> diseaseIdMap = specialDiseaseList.stream()
                .collect(Collectors.toMap(RiskDiseaseData::getId, Function.identity(), (o1, o2) -> o2));
        // 所有疾病治疗方案选项信息
//        final Map<String, List<Long>> allChoiceMap = getAllChoiceByContext(diseaseDecideContext);
        Map<String, List<Long>> map = diseaseDecideContext.getDecideReasonableInfo().getGetAllChoiceMap();
        final Map<String, List<Long>> allChoiceMap = MapUtils.isNotEmpty(map) ? map : getAllChoiceByContext(diseaseDecideContext);

        // 查出多选方案的疾病
        List<RiskDiseaseTreatmentProject> specialDiseaseTreatmentList =
                getTreatmentByDisease(specialDiseaseList, allChoiceMap);
        if (CollectionUtils.isEmpty(specialDiseaseTreatmentList)) {
            return;
        }

        List<IDiseaseAmountInfo> amountList =
                promoteAmountListByTreatmentList(specialDiseaseTreatmentList, diseaseIdMap, occupyPlace, judgeDiseaseInfoList);
        diseaseDecideContext.getDiseaseAmountInfoAllList().addAll(amountList);

        // 计算特殊疾病的花费 同一类特殊疾病只取花费最高的一个
        // 获取花费最高疾病治疗方案
        final List<IDiseaseAmountInfo> sortList = amountList.stream()
                .sorted(
                        Comparator.comparing(IDiseaseAmountInfo::getMaxAmount)
                                .thenComparing(IDiseaseAmountInfo::getMinAmount)
                                .reversed()
                ).collect(Collectors.toList());
        final IDiseaseAmountInfo maxAmountInfo = sortList.get(0);
        if (maxAmountInfo == null) {
            return;
        }

        final Set<IDiseaseAmountInfo> selectDiseaseAmountInfoList =
                getAmountInfoListByOccupyPlace(diseaseDecideContext, occupyPlace);
        // 添加到选中列表中
        selectDiseaseAmountInfoList.add(maxAmountInfo);

        // 被淘汰的该类其他疾病需要检查是否已经在选中列表，在的话需要移除
        for (IDiseaseAmountInfo amountTreatmentInfo : amountList) {
            final String diseaseClassName = amountTreatmentInfo.getDiseaseClassName();
            if (StringUtils.equals(diseaseClassName, maxAmountInfo.getDiseaseClassName())){
                continue;
            }
            selectDiseaseAmountInfoList.remove(amountTreatmentInfo);
        }
    }

    private void addComplicationsToJudgeDiseaseList(List<String> complications, List<RiskDiseaseData> specialDiseaseList) {
        List<String> oneComplication = Lists.newArrayList();
        for(RiskDiseaseData riskDiseaseData : specialDiseaseList){
            int size = CollectionUtils.size(specialDiseaseList);
            // list数据小于两个时说明无合并症，直接返回
            if(size < 2){
                return;
            }
            oneComplication.add(riskDiseaseData.getDiseaseClassName());
        }
        String complication = String.join(" ", oneComplication);
        complications.add(complication);
    }

    private List<IDiseaseAmountInfo> promoteAmountListByTreatmentList(
            List<RiskDiseaseTreatmentProject> projects,
            Map<Long, RiskDiseaseData> diseaseIdMap,
            boolean occupyPlace,
            List<JudgeDiseaseInfoVO> judgeDiseaseInfoVOList) {
        final List<IDiseaseAmountInfo> result = Lists.newArrayList();
        final Map<String, List<RiskDiseaseTreatmentProject>> projectByDiseaseNameGroup =
                getProjectByDiseaseNameGroup(projects, diseaseIdMap);

        // 更新和添加展示列表疾病对应的治疗方案
        addTreatmentToJudgeDiseaseList(judgeDiseaseInfoVOList, projectByDiseaseNameGroup);

        for (List<RiskDiseaseTreatmentProject> singleDiseaseTreatments : projectByDiseaseNameGroup.values()) {
            final int size = CollectionUtils.size(singleDiseaseTreatments);
            if (size == 0) {
                continue;
            }
            final RiskDiseaseTreatmentProject first = singleDiseaseTreatments.get(0);
            final RiskDiseaseData disease = diseaseIdMap.get(first.getDiseaseId());
            final String diseaseClassName = disease.getDiseaseClassName();
            if (size == 1) {
                DiseaseAmountTreatmentInfo amountInfo =
                        DiseaseAmountTreatmentInfo.create(disease, first, occupyPlace);
                result.add(amountInfo);
                continue;
            }
            if (size > 1) {
                int minTreatmentFee = 0;
                int maxTreatmentFee = 0;
                List<String> treatmentInfoList = Lists.newArrayList();
                for (RiskDiseaseTreatmentProject treatment : singleDiseaseTreatments) {
                    minTreatmentFee += treatment.getMinTreatmentFee();
                    maxTreatmentFee += treatment.getMaxTreatmentFee();
                    treatmentInfoList.add(treatment.getProjectName());
                }
                final String multiTreatmentInfo = StringUtils.join(treatmentInfoList, ",");
                final DiseaseAmountSpecialInfo diseaseAmountSpecialInfo = DiseaseAmountSpecialInfo.create(
                        minTreatmentFee,
                        maxTreatmentFee,
                        occupyPlace,
                        diseaseClassName,
                        disease.getRaiseType(),
                        multiTreatmentInfo
                );
                result.add(diseaseAmountSpecialInfo);
            }
        }
        return result;
    }

    private void addTreatmentToJudgeDiseaseList(List<JudgeDiseaseInfoVO> judgeDiseaseInfoVOList, Map<String, List<RiskDiseaseTreatmentProject>> projectByDiseaseNameGroup) {
        List<JudgeDiseaseInfoVO> newTreatMethodJudgeDiseaseList = Lists.newArrayList();
        Set<JudgeDiseaseInfoVO> judgeDiseaseTreatmentList = Sets.newHashSet(judgeDiseaseInfoVOList);
        for(JudgeDiseaseInfoVO diseaseInfo : judgeDiseaseInfoVOList){
            List<RiskDiseaseTreatmentProject> projectByDiseaseName =
                    projectByDiseaseNameGroup.get(diseaseInfo.getDiseaseClassName());
            if(CollectionUtils.isEmpty(projectByDiseaseName)){
                continue;
            }
            for(RiskDiseaseTreatmentProject project : projectByDiseaseName) {
                if (StringUtils.isEmpty(diseaseInfo.getTreatMethod())) {
                    setInfos(diseaseInfo, project, diseaseInfo.getRaise());
                }

                if(judgeDiseaseTreatmentList.stream().map(JudgeDiseaseInfoVO::getTreatMethod).collect(Collectors.toList())
                        .contains(project.getProjectName())){
                    continue;
                }
                JudgeDiseaseInfoVO addDiseaseInfo = new JudgeDiseaseInfoVO();
                addDiseaseInfo.setDiseaseName(diseaseInfo.getDiseaseName());
                addDiseaseInfo.setDiseaseClassName(diseaseInfo.getDiseaseClassName());
                addDiseaseInfo.setInDiseaseSystem(diseaseInfo.getInDiseaseSystem());
                setInfos(addDiseaseInfo, project, diseaseInfo.getRaise());
                newTreatMethodJudgeDiseaseList.add(addDiseaseInfo);

            }
        }
        judgeDiseaseInfoVOList.addAll(newTreatMethodJudgeDiseaseList);
    }

    private void setInfos(JudgeDiseaseInfoVO diseaseInfo, RiskDiseaseTreatmentProject project, int raise) {
        diseaseInfo.setTreatMethod(project.getProjectName());
        diseaseInfo.setRaise(raise == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() ?
                RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() : project.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() ?
                RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode() : RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode());
        diseaseInfo.setMatchMinAmount(project.getMinTreatmentFee() / 100.0);
        diseaseInfo.setMatchMaxAmount(project.getMaxTreatmentFee() / 100.0);
    }

    private Map<String, List<RiskDiseaseTreatmentProject>> getProjectByDiseaseNameGroup(List<RiskDiseaseTreatmentProject> projects,
                                                                                        Map<Long, RiskDiseaseData> diseaseIdMap) {
        final Map<String, List<RiskDiseaseTreatmentProject>> resultMap = Maps.newHashMap();
        for (RiskDiseaseTreatmentProject project : projects) {
            final RiskDiseaseData disease = diseaseIdMap.get(project.getDiseaseId());
            final String diseaseClassName = disease.getDiseaseClassName();
            List<RiskDiseaseTreatmentProject> list = resultMap.get(diseaseClassName);
            if (list == null) {
                list = Lists.newArrayList();
            }
            list.add(project);
            resultMap.put(diseaseClassName, list);
        }
        return resultMap;
    }

    private Set<IDiseaseAmountInfo> getAmountInfoListByOccupyPlace(DiseaseDecideContext diseaseDecideContext,
                                                                    boolean occupyPlace) {
        return occupyPlace ?
                diseaseDecideContext.getDiseaseAmountInfoList() :
                diseaseDecideContext.getDiseaseAmountInfoNoPlaceList();
    }

    private List<RiskDiseaseTreatmentProject> getTreatmentByDisease(List<RiskDiseaseData> allRiskDiseaseDataList,
                                                                    Map<String, List<Long>> allChoiceMap) {
        List<Long> normalDiseaseIds = allRiskDiseaseDataList.stream().map(RiskDiseaseData::getId)
                .collect(Collectors.toList());
        final List<RiskDiseaseTreatmentProject> treatmentList = treatmentProjectRpcBiz.findByDiseaseId(normalDiseaseIds);

        List<RiskDiseaseTreatmentProject> treatmentProjectList = filterChoiceTreatment(treatmentList,
                allChoiceMap, allRiskDiseaseDataList);
        if (CollectionUtils.isEmpty(treatmentProjectList)) {
            log.info("treatmentProjectList is empty");
        }
        return treatmentProjectList;
    }

    private List<RiskDiseaseData> getNormalDiseaseListByAll(List<RiskDiseaseData> allRiskDiseaseDataList) {
        // 普通疾病列表
        List<RiskDiseaseData> normalDiseaseList = Lists.newArrayList();
        // 特殊疾病列表
        for (RiskDiseaseData riskDiseaseData : allRiskDiseaseDataList) {
            final String diseaseClassName = riskDiseaseData.getDiseaseClassName();
            if (specialDiseaseSet.contains(diseaseClassName)) {
                continue;
            }
            if (StringUtils.equals(DiseaseCommonConfig.BURN_NAME, diseaseClassName)) {
                continue;
            }
            normalDiseaseList.add(riskDiseaseData);
        }
        return normalDiseaseList;
    }

    private void handleNormalDisease(DiseaseDecideContext diseaseDecideContext){
        final List<JudgeDiseaseInfoVO> judgeDiseaseInfoList = diseaseDecideContext.getJudgeDiseaseInfoList();
        List<RiskDiseaseData> allRiskDiseaseDataList = diseaseDecideContext.getAllDiseaseDataList();

        // 筛选普通疾病列表
        final List<RiskDiseaseData> normalDiseaseList = getNormalDiseaseListByAll(allRiskDiseaseDataList);
        // 所有疾病治疗方案选项信息
//        final Map<String, List<Long>> allChoiceMap = getAllChoiceByContext(diseaseDecideContext);
        Map<String, List<Long>> map = diseaseDecideContext.getDecideReasonableInfo().getGetAllChoiceMap();
        final Map<String, List<Long>> allChoiceMap = MapUtils.isNotEmpty(map) ? map : getAllChoiceByContext(diseaseDecideContext);
        final Map<Long, RiskDiseaseData> diseaseIdMap = normalDiseaseList.stream()
                .collect(Collectors.toMap(RiskDiseaseData::getId, Function.identity(), (o1, o2) -> o2));

        List<RiskDiseaseTreatmentProject> treatmentProjectList = getTreatmentByDisease(normalDiseaseList, allChoiceMap);

        List<IDiseaseAmountInfo> amountList =
                promoteAmountListByTreatmentList(treatmentProjectList, diseaseIdMap, true, judgeDiseaseInfoList);
        diseaseDecideContext.getDiseaseAmountInfoList().addAll(amountList);
        diseaseDecideContext.getDiseaseAmountInfoAllList().addAll(amountList);
    }

    private Map<String, List<Long>> getAllChoiceByContext(DiseaseDecideContext diseaseDecideContext) {
        return diseaseDecideAmountService.getAllChoiceMapByContext(diseaseDecideContext);
    }

    private boolean getCanRaise(List<DiseaseInfoVo> riskDiseaseDataList) {
        //过滤可发起的疾病库信息
        final Optional<DiseaseInfoVo> any = riskDiseaseDataList.stream()
                .filter(v -> v.getDiseaseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()
                        || v.getDiseaseType() == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode())
                .findAny();
        return any.isPresent();
    }

    /**
     * @param treatmentProjects   疾病下所有的治疗方案
     * @param choiceMap           用户选择的方式
     * @param riskDiseaseDataList 疾病的基础信息
     * @return 最后确定的治疗方案
     */
    private List<RiskDiseaseTreatmentProject> filterChoiceTreatment(List<RiskDiseaseTreatmentProject> treatmentProjects,
                                                                    Map<String, List<Long>> choiceMap,
                                                                    List<RiskDiseaseData> riskDiseaseDataList) {
        List<RiskDiseaseTreatmentProject> result = Lists.newArrayList();
        Map<Long, List<RiskDiseaseTreatmentProject>> allTreatmentProjects = transTreatmentProjectMap(treatmentProjects);
        for (RiskDiseaseData diseaseData : riskDiseaseDataList) {
            List<RiskDiseaseTreatmentProject> treatmentList = allTreatmentProjects.get(diseaseData.getId());
            List<Long> choiceTreatmentIdList = choiceMap.get(diseaseData.getDiseaseClassName());
            if (CollectionUtils.isEmpty(treatmentList)) {
                log.info("treatmentList is empty:{}", diseaseData.getId());
                continue;
            }
            if (CollectionUtils.isNotEmpty(choiceTreatmentIdList)) {
                result.addAll(treatmentList.stream().filter(v -> choiceTreatmentIdList.contains(v.getId()))
                        .collect(Collectors.toList()));
                continue;
            }
           /* //没选择返回不知道的治疗方案
            List<RiskDiseaseTreatmentProject> noKnow = treatmentList.stream().
                    filter(v -> noKnowTreatmentName.equals(v.getProjectName()) || "无分期".equals(v.getProjectName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noKnow)) {
                result.addAll(noKnow);
            } else {
                result.add(treatmentList.get(0));
            }*/
            if(diseaseData.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()){
                RiskDiseaseTreatmentProject minTreatmentProject = treatmentList.stream()
                        .filter(v -> v.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode())
                        .sorted(Comparator.comparing(RiskDiseaseTreatmentProject::getMinTreatmentFee))
                        .collect(Collectors.toList()).get(0);
                result.add(minTreatmentProject);
                continue;
            }
            //目标金额合理性判断策略选择最便宜的方案
            RiskDiseaseTreatmentProject minTreatmentProject = treatmentList.stream()
                    .sorted(Comparator.comparing(RiskDiseaseTreatmentProject::getMinTreatmentFee))
                    .collect(Collectors.toList()).get(0);
            result.add(minTreatmentProject);
        }
        return result;
    }

    private Map<Long, List<RiskDiseaseTreatmentProject>> transTreatmentProjectMap(List<RiskDiseaseTreatmentProject> treatmentProjects) {
        Map<Long, List<RiskDiseaseTreatmentProject>> result = Maps.newHashMap();
        for (RiskDiseaseTreatmentProject treatmentProject : treatmentProjects) {
            List<RiskDiseaseTreatmentProject> treatmentProjectsPart = result.get(treatmentProject.getDiseaseId());
            if (CollectionUtils.isEmpty(treatmentProjectsPart)) {
                treatmentProjectsPart = Lists.newArrayList();
            }
            treatmentProjectsPart.add(treatmentProject);
            result.put(treatmentProject.getDiseaseId(), treatmentProjectsPart);
        }
        return result;
    }

    private List<String> mergeOneDiseaseList(List<String> diseaseList) {
        //对归一后的疾病名称进行合并
        List<String> diseaseNameList = Lists.newArrayList();
        for (String diseaseName : diseaseList) {
            if (StringUtils.isBlank(diseaseName)) {
                diseaseNameList.add("");
                continue;
            }
            if (diseaseNameList.contains(diseaseName)) {
                continue;
            }
            diseaseNameList.add(diseaseName);
        }
        return diseaseNameList;
    }

    /**
     * 写入金额顺序
     *
     * @param targetAmount 案例的目标金额
     * @param resultVo     疾病结果Vo
     */
    private void fullAdviseMinAmount(int targetAmount,
                                     InfoReasonableAmountResultVo resultVo) {
        if (resultVo.getMatchMaxAmount() <= 0) {
            return;
        }
        //建议金额
        resultVo.setAdviseMinAmount(getMinAmount(resultVo.getMatchMinAmount()));
        resultVo.setAdviseMaxAmount(getAdviseMaxAmount(resultVo.getMatchMaxAmount()));
        //目标金额以分为单位  所以金额需要* 1000000
        resultVo.setReasonableStatus(targetAmount <= (resultVo.getAdviseMaxAmount() * 1000000));
    }


    private double getMinAmount(double originMinAmount) {
        return Math.min(originMinAmount, 50.0);
    }

    /**
     * 匹配疾病的金额
     * a.标准基数5万元以下的(含5万)，可接受上浮2万元。
     * b.标准基数6-20万元的(含20万)，可接受上浮3万元。
     * c.标准基数21-35万元的(含35万)，可接受上浮5万元。
     * d.标准基数36万元以上的，可接受上浮10万元，上限50万元。
     *
     * @param sumAmount 疾病总的上限金额
     * @return 加上上浮范围的金额
     */
    private double getAdviseMaxAmount(double sumAmount) {
        return Math.min(sumAmount, 50.0);
    }

}
