package com.shuidihuzhu.cf.risk.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.dao.CfRiskCommentDao;
import com.shuidihuzhu.cf.risk.model.CfRiskComment;
import com.shuidihuzhu.cf.risk.model.CommentVO;
import com.shuidihuzhu.cf.risk.service.CfRiskCommentService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@Service
@Slf4j
public class CfRiskCommentServiceImpl implements CfRiskCommentService {

    @Autowired
    private CfRiskCommentDao cfRiskCommentDao;


    @Override
    public List<CfRiskComment> findByCommentIds(List<Long> commentIdList) {
        if(CollectionUtils.isEmpty(commentIdList)) {
            return Lists.newArrayList();
        }
        return cfRiskCommentDao.getByCommentIds(commentIdList);
    }

    @Override
    public Response<List<CommentVO>> findByCommentIdsInner(List<Long> commentIdList) {
        List<CfRiskComment> cfRiskComments = findByCommentIds(commentIdList);
        if (CollectionUtils.isEmpty(cfRiskComments)) {
            return NewResponseUtil.makeSuccess(null);
        }
        List<CommentVO> commentVOS = Lists.newArrayListWithCapacity(commentIdList.size());
        for (CfRiskComment comment : cfRiskComments){
            CommentVO commentVO = new CommentVO();
            commentVO.setId(comment.getId());
            commentVO.setSensitiveWord(comment.getSensitiveWord());
            commentVO.setUserId(comment.getUserId());
            commentVO.setContent(comment.getContent());
            commentVOS.add(commentVO);
        }
        return NewResponseUtil.makeSuccess(commentVOS);
    }

}
