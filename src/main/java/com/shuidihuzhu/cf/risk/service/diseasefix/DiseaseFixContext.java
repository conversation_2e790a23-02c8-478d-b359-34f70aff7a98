package com.shuidihuzhu.cf.risk.service.diseasefix;

import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DiseaseFixContext {

    @ApiModelProperty("需修正的请求参数")
    private DecideReasonableInfo needFixRequest;

    /**
     * 只读 严禁修改内部数据
     */
    @ApiModelProperty("修正前计算结果")
    private DiseaseDecideContext resourceContext;

    @ApiModelProperty("需要累加的总计金额 单位万元")
    private double amountForPlusInWanYuan;

    @ApiModelProperty("是否需要中断")
    private boolean needBreak;

    @ApiModelProperty("是否特重度烧伤")
    private boolean specialBurn;

}
