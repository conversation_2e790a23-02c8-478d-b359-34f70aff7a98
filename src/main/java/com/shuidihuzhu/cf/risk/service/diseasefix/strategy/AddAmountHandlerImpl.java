package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.service.diseasefix.CompareSymbols;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class AddAmountHandlerImpl implements IStrategyHandler{

    private final Long AMOUNT_CHANGE = 10000L;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {

        if(logic.getAddAmountInYuan() > 0){
            diseaseFixContext.setAmountForPlusInWanYuan(logic.getAddAmountInYuan() * 1.0 / AMOUNT_CHANGE);
        }

        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "addAmount";
    }
}
