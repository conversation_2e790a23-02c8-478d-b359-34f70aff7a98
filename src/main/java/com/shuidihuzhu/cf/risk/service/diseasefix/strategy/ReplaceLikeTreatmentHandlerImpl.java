package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplaceLikeTreatmentHandlerImpl implements IStrategyHandler{

    @Resource
    private DiseaseDecideAmountService diseaseDecideAmountService;

    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {

        // 获取归一后疾病
        List<String> diseaseNameList = diseaseFixContext.getResourceContext().getDecideReasonableInfo().getDiseaseNameList();
        String likeTreatmentNameForReplace = logic.getLikeTreatmentNameForReplace();
        DecideReasonableInfo needFixRequest = diseaseFixContext.getNeedFixRequest();

        // 所有疾病治疗方案选项信息
        final Map<String, List<Long>> getAllChoiceMap = diseaseDecideAmountService.getAllChoiceMap(needFixRequest);
        Map<String, List<Long>> fixChoiceMap = Maps.newHashMap();
        fixChoiceMap.putAll(getAllChoiceMap);

        // 替换原有治疗方案
        for(String disease : diseaseNameList){
            RiskDiseaseData diseaseInfo = riskDiseaseDataRpcDao.getByClassName(disease);
            List<RiskDiseaseTreatmentProject> treatmentByDisease =
                    riskDiseaseTreatmentProjectRpcDao.findByDiseaseIdList(Lists.newArrayList(diseaseInfo.getId()));
            if(CollectionUtils.isEmpty(treatmentByDisease)){
                continue;
            }
            List<RiskDiseaseTreatmentProject> list = treatmentByDisease.stream().filter(v -> v.getProjectName().contains(likeTreatmentNameForReplace)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            fixChoiceMap.put(disease, list.stream().map(RiskDiseaseTreatmentProject::getId).collect(Collectors.toList()));

        }
        needFixRequest.setGetAllChoiceMap(fixChoiceMap);
        diseaseFixContext.setNeedFixRequest(needFixRequest);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "replaceLikeTreatment";
    }
}
