package com.shuidihuzhu.cf.risk.service;

import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.common.web.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

@Slf4j
@Service
public class SeaAccountService {

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;


    public String getName(long userId){
        if (userId <= 0) {
            return "";
        }
        Response<AuthUserDto> accountResponse = userFeignClient.getValidAuthUserById(userId);
        log.info("call user account client resp:{}", accountResponse);
        String name = "";
        if (accountResponse.ok() && accountResponse.getData() != null) {
            name = accountResponse.getData().getUserName();
        } else {
            log.error("query sea account filed:{}", accountResponse);
        }

        return name;
    }

    public String getOrganization(long userId){
        Response<String> rpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        log.info("call user org client resp:{}", rpcResponse);
        String organization = "";
        if (rpcResponse.ok()) {
            organization = rpcResponse.getData();
        } else {
            log.error("query sea org filed:{}", rpcResponse);
        }
        return StringUtils.isBlank(organization) ? organization : organization + "-";
    }



    public AdminUserNameWithOrg getCurrAdminUserNameWithOrg(long adminUserId){

        if (adminUserId > 0) {
            String userName = getName(adminUserId);
            String organization = getOrganization(adminUserId);
            return new AdminUserNameWithOrg(adminUserId, userName, StringUtils.isBlank(organization)
                    ? organization
                    : organization.substring(0, organization.length()-1));
        }

        return new AdminUserNameWithOrg();
    }


    public String getInfoByMis(String mis){
        if (StringUtils.isBlank(mis)){
            return null;
        }
        Response<AuthUserDto> rpcResponse = userFeignClient.getByLoginName(mis);
        if (rpcResponse != null && rpcResponse.ok() && rpcResponse.getData() != null){
            AuthUserDto authUserDto = rpcResponse.getData();
            String name = StringUtils.trimToEmpty(authUserDto.getUserName());
            String organization = getOrganization(authUserDto.getUserId());
            return organization + name;
        }
        return null;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @NotNull
    public static final class AdminUserNameWithOrg {
        private long adminUserId;
        private String adminUserName;
        private String org;
        public String getUserNameWithOrg(){
            if (StringUtils.isAnyBlank(org, adminUserName)) {
                return org + adminUserName;
            }
            return org + "-" + adminUserName;
        }
    }

}
