package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountService;
import com.shuidihuzhu.cf.risk.service.diseasefix.CompareSymbols;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AddDiseaseNameAndTreatmentByMaxAmountHandlerImpl implements IStrategyHandler{

    @Resource
    private DiseaseDecideAmountService diseaseDecideAmountService;

    @Resource
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    @Resource
    private RiskDiseaseTreatmentProjectRpcDao riskDiseaseTreatmentProjectRpcDao;

    private final Long AMOUNT_CHANGE = 10000L;

    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {

        InfoReasonableAmountResultVo resourceResult = diseaseFixContext.getResourceContext().getResult();
        DecideReasonableInfo needFixRequest = diseaseFixContext.getNeedFixRequest();

        // 所有疾病治疗方案选项信息
        final Map<String, List<Long>> getAllChoiceMap = diseaseDecideAmountService.getAllChoiceMap(needFixRequest);
        Map<String, List<Long>> fixChoiceMap = Maps.newHashMap();
        fixChoiceMap.putAll(getAllChoiceMap);

        if(logic.getMaxAmountInYuan() <= 0 || StringUtils.isEmpty(logic.getCondition())){
            return diseaseFixContext;
        }

        if(CompareSymbols.MORE_THAN.equals(logic.getCondition())){
            if(resourceResult.getAdviseMaxAmount() * AMOUNT_CHANGE > logic.getMaxAmountInYuan()){
                return addDiseaseNameAndTreatmentByMaxAmount(logic, diseaseFixContext, needFixRequest, fixChoiceMap);
            }
        }

        if(CompareSymbols.LESS_THAN_OR_EQUAL.equals(logic.getCondition())){
            if(resourceResult.getAdviseMaxAmount() * AMOUNT_CHANGE <= logic.getMaxAmountInYuan()){
                return addDiseaseNameAndTreatmentByMaxAmount(logic, diseaseFixContext, needFixRequest, getAllChoiceMap);
            }
        }


        return diseaseFixContext;
    }

    private DiseaseFixContext addDiseaseNameAndTreatmentByMaxAmount(ChildLogic logic,
                                                                    DiseaseFixContext diseaseFixContext,
                                                                    DecideReasonableInfo needFixRequest,
                                                                    Map<String, List<Long>> fixChoiceMap) {
        String addDiseaseName = logic.getAddDiseaseName();
        String addTreatment = logic.getAddTreatment();
        // 获取疾病id
        RiskDiseaseData diseaseInfo = riskDiseaseDataRpcDao.getByClassName(addDiseaseName);
        if(diseaseInfo == null){
            log.info("disease:{} is not exist",addDiseaseName);
            return diseaseFixContext;
        }
        // 获取治疗方案id
        RiskDiseaseTreatmentProject treatmentInfo =
                riskDiseaseTreatmentProjectRpcDao.findByTreatmentNameAndDiseaseId(addTreatment, diseaseInfo.getId());
        if(treatmentInfo == null){
            log.info("treatment:{} is not exist",addTreatment);
            return diseaseFixContext;
        }
        needFixRequest.getDiseaseNameList().add(addDiseaseName);
        fixChoiceMap.put(addDiseaseName, Lists.newArrayList(treatmentInfo.getId()));
        needFixRequest.setGetAllChoiceMap(fixChoiceMap);
        diseaseFixContext.setNeedFixRequest(needFixRequest);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "addDiseaseNameAndTreatmentByMaxAmount";
    }
}
