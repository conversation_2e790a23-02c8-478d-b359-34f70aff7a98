package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChildLogic{

    @ApiModelProperty("操作规则")
    private String doType;

    @ApiModelProperty("替换后的治疗方案名称")
    private String treatmentNameForReplace;

    @ApiModelProperty("替换后的治疗方案名称(模糊)")
    private String likeTreatmentNameForReplace;

    @ApiModelProperty("替换后的疾病名")
    private String diseaseNameForReplace;

    @ApiModelProperty("用来匹配的疾病名")
    private String diseaseName;

    @ApiModelProperty("用来匹配的疾病名")
    private String treatmentName;

    @ApiModelProperty("增加的疾病名")
    private String addDiseaseName;

    @ApiModelProperty("增加的治疗方案名")
    private String addTreatment;

    @ApiModelProperty("判断条件")
    private String condition;

    @ApiModelProperty("原总金额上限 单位：元")
    private int maxAmountInYuan;

    @ApiModelProperty("增加的金额 单位：元")
    private int addAmountInYuan;
}
