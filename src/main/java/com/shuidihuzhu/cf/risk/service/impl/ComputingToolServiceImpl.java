package com.shuidihuzhu.cf.risk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.impl.DiseaseComputingToolStrategy;
import com.shuidihuzhu.cf.risk.biz.impl.ToolJudgeContext;
import com.shuidihuzhu.cf.risk.dao.ComputingToolRecordDao;
import com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.risk.ComputingRecord;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.cf.risk.service.ComputingToolService;
import com.shuidihuzhu.cf.risk.service.SeaAccountService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ComputingToolServiceImpl implements ComputingToolService {

    @Autowired
    private SeaAccountService seaAccountService;

    @Resource
    private ComputingToolRecordDao computingToolRecordDao;

    @Autowired
    private DiseaseComputingToolStrategy diseaseComputingToolStrategy;

    @Override
    public Response<ComputingToolResult> getResultAndSubmit(String extName, long extId, long operatorId, String submitJson) {
        ComputingToolRecordDO record = new ComputingToolRecordDO();
        record.setExtName(extName);
        record.setExtId(extId);
        if(operatorId > 0){
            record.setOperator(seaAccountService.getName(operatorId));
            record.setOperatorOrg(seaAccountService.getOrganization(operatorId));
        }
        record.setSourceJson(submitJson);
        // 根据策略计算结果
        FamilyFinancialSituation submit = JSONObject.parseObject(submitJson, FamilyFinancialSituation.class);
        if(submit == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        ToolJudgeContext toolJudgeContext = new ToolJudgeContext();
        toolJudgeContext.setRequestParam(submit);
        toolJudgeContext.setTargetAmount(submit.getMaxSystemDiseaseCost() != null ? Math.toIntExact(submit.getMaxSystemDiseaseCost()) : 0);
        ComputingToolResult result = diseaseComputingToolStrategy.exec(toolJudgeContext);
        record.setResultJson(JSONObject.toJSONString(result));

        int insert = computingToolRecordDao.insert(record);

        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public ComputingToolRecordDO getRecordById(String extName, long extId, int recordId) {
        // -1 默认回填extName&extId条件下最新的一条
        if(recordId == -1 && StringUtils.isNotBlank(extName) && extId >= 0){
            List<ComputingToolRecordDO> recordList = computingToolRecordDao.selectByExtNameAndExtId(extName, extId);
            if(CollectionUtils.isEmpty(recordList)){
                return new ComputingToolRecordDO();
            }
            // 取最新的一条
            return recordList.get(0);
        }
        if(recordId <= 0){
            return new ComputingToolRecordDO();
        }
        ComputingToolRecordDO record = computingToolRecordDao.selectById(recordId);
        if(record == null){
            return new ComputingToolRecordDO();
        }
        if(!record.getExtName().equals(extName) || record.getExtId() != extId){
            log.info("extName or extId not match, recordId:{}, extName:{}, extId:{}", recordId, extName, extId);
            return new ComputingToolRecordDO();
        }
        return record;
    }

    @Override
    public Response<List<ComputingRecord>> getRecordList(String extName, long extId) {
        if(extId <= 0 || StringUtils.isBlank(extName)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<ComputingToolRecordDO> recordList = computingToolRecordDao.selectByExtNameAndExtId(extName, extId);
        if(CollectionUtils.isEmpty(recordList)){
            return NewResponseUtil.makeSuccess();
        }
        List<ComputingRecord> computingRecords = Lists.newArrayList();
        for(ComputingToolRecordDO record : recordList){
            ComputingRecord computingRecord = new ComputingRecord();
            computingRecord.setRecordId((int) record.getId());
            computingRecord.setOperator(record.getOperator());
            computingRecord.setOperatorOrg(record.getOperatorOrg());
            computingRecord.setCalculateTime(record.getCreateTime());
            computingRecords.add(computingRecord);
        }
        return NewResponseUtil.makeSuccess(computingRecords);
    }
}
