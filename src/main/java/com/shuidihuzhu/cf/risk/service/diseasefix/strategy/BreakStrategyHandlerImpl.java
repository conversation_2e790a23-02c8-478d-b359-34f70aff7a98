package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class BreakStrategyHandlerImpl implements IStrategyHandler{
    @Override
    public DiseaseFixContext process(ChildLogic logic, DiseaseFixContext diseaseFixContext) {
        diseaseFixContext.setNeedBreak(true);
        return diseaseFixContext;
    }

    @Override
    public String getDoType() {
        return "break";
    }
}
