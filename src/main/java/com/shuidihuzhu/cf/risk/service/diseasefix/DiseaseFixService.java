package com.shuidihuzhu.cf.risk.service.diseasefix;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;

/**
 * <AUTHOR>
 */
public interface DiseaseFixService {

    /**
     * @param preFixContext 修正前计算结果
     * @param crowdfundingInfo
     * @return 修正后计算结果
     */
    DiseaseDecideContext decideByFix(DiseaseDecideContext preFixContext, CrowdfundingInfo crowdfundingInfo);
}
