package com.shuidihuzhu.cf.risk.service.impl;

import com.shuidihuzhu.cf.risk.dao.highrisk.CarInfoDao;
import com.shuidihuzhu.cf.risk.dao.highrisk.CityRiskInfoDao;
import com.shuidihuzhu.cf.risk.model.highrisk.CarInfoDO;
import com.shuidihuzhu.cf.risk.model.highrisk.CityRiskInfoDO;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.cf.risk.service.RiskStrategyService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RefreshScope
public class RiskStrategyServiceImpl implements RiskStrategyService {

    private static final Integer USE = 1;
    private static final Integer NOT_USE = 0;

    private static final Integer IS_LUXURY = 1;

    @Autowired
    private CityRiskInfoDao cityRiskInfoDao;

    @Autowired
    private CarInfoDao carRiskInfoDao;

    @Value("${apollo.risk.city.amount.threshold:1000}")
    private int amountThreshold;

    @Override
    public Boolean isFamousCar(String carInfo) {
        final CarInfoDO carRiskInfo = carRiskInfoDao.getCarRiskInfo(carInfo);
        if (carRiskInfo == null) {
            return null;
        }
        final int luxury = carRiskInfo.getLuxury();
        return luxury == IS_LUXURY;
    }

    @Override
    public Integer getCityAmonutThreshold(CityParam param) {
        if(param == null){
            return amountThreshold;
        }
        Integer cityAmountThreshold = getCityRiskInfo(param).getCityAmountThreshold();
        if(cityAmountThreshold == null){
            // 使用apollo配置
            cityAmountThreshold = amountThreshold;
        }
        return cityAmountThreshold;
    }

    private CityRiskInfoDO getCityRiskInfo(CityParam param) {
        CityRiskInfoDO cityRiskInfoDO = cityRiskInfoDao.getCityRiskInfo(param.getProvince(), param.getCity(), param.getCounty());
        if (cityRiskInfoDO == null) {
            log.info("未查询到城市信息，cityParam:{}", param);
            cityRiskInfoDO = new CityRiskInfoDO();
            cityRiskInfoDO.setUseFloor(NOT_USE);
            cityRiskInfoDO.setUseCommunity(NOT_USE);
            return cityRiskInfoDO;
        }
        if(cityRiskInfoDO.getUseFloor() == null) {
            cityRiskInfoDO.setUseFloor(NOT_USE);
        }
        if(cityRiskInfoDO.getUseCommunity() == null) {
            cityRiskInfoDO.setUseCommunity(NOT_USE);
        }
        return cityRiskInfoDO;
    }
}

