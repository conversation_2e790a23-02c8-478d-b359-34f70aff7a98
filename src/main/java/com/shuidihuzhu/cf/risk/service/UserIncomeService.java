package com.shuidihuzhu.cf.risk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.biz.UserIncomeBiz;
import com.shuidihuzhu.cf.risk.model.UserIncomeThird;
import com.shuidihuzhu.cf.risk.model.enums.UserInComeEnum;
import com.shuidihuzhu.cf.risk.model.enums.UserInComeShangyongEnum;
import com.shuidihuzhu.cf.risk.model.vo.UserIncomeThirdVo;
import com.shuidihuzhu.cf.risk.util.NewEncryptUtil;
import com.shuidihuzhu.cf.risk.util.third.BairongUtil;
import com.shuidihuzhu.cf.risk.util.third.ShangyongUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@Slf4j
@Service
public class UserIncomeService {

    @Autowired
    private UserIncomeBiz userIncomeBiz;
    @Autowired
    private BairongUtil bairongUtil;
    @Autowired
    private ShangyongUtil shangyongUtil;
    @Autowired
    private NewEncryptUtil newEncryptUtil;

    public Map<String, Map<String, String>> getThirdData(String idCard, String mobile, String realName, String modelType) {
        Map<String, String> bairongData = getBairongData(idCard, mobile, realName);
        Map<String, String> shangyongData = getShangyongData(idCard, mobile, realName, modelType);
        return ImmutableMap.of(UserInComeEnum.ThirdType.BAIRONG.getName(), bairongData, UserInComeEnum.ThirdType.SHANGYONG.getName(), shangyongData);
    }

    public Map<String, String> getBairongData(String idCard, String mobile, String realName) {
        UserIncomeThird userIncomeThirdBairong = this.userIncomeBiz.getBairongByIdCard(newEncryptUtil.encrypt(idCard), realName);
        if(userIncomeThirdBairong == null) {
            UserIncomeThirdVo bairongData = this.bairongUtil.getPortrayalData(idCard, mobile, realName);
            userIncomeThirdBairong = new UserIncomeThird();
            userIncomeThirdBairong.buildQuestParam("", "", newEncryptUtil.encrypt(idCard), realName,  newEncryptUtil.encrypt(mobile));
            userIncomeThirdBairong.buildByUserIncomeThirdVo(bairongData);
            this.userIncomeBiz.save(userIncomeThirdBairong);
        }
        Map<String, String> resultMap = Maps.newHashMap();
        resultMap.put("data", userIncomeThirdBairong.getThirdData());
        JSONObject jsonObject = JSON.parseObject(userIncomeThirdBairong.getThirdData());
        if(jsonObject.getJSONObject("CreditModelEva") != null) {
            String radical = jsonObject.getJSONObject("CreditModelEva").getString("radical");
            resultMap.put("level", radical);
        }
        return resultMap;
    }

    public Map<String, String> getShangyongData(String idCard, String mobile, String realName, String modelType) {
        UserIncomeThird userIncomeThirdShangyong = this.userIncomeBiz.getShangyongByIdCard(newEncryptUtil.encrypt(idCard), realName, modelType);
        if (userIncomeThirdShangyong == null || System.currentTimeMillis() - userIncomeThirdShangyong.getCreateTime().getTime() > 30 * 24 * 60 * 60 * 1000L) {
            String orderNo = UUID.randomUUID().toString().replaceAll("-", "");
            String seqNo = UUID.randomUUID().toString().replaceAll("-", "");
            UserIncomeThirdVo shangyongData = this.shangyongUtil.getPortrayalData(idCard, mobile, realName, modelType, orderNo, seqNo);
            userIncomeThirdShangyong = new UserIncomeThird();
            userIncomeThirdShangyong.buildQuestParam(orderNo, seqNo, newEncryptUtil.encrypt(idCard), realName, newEncryptUtil.encrypt(mobile));
            userIncomeThirdShangyong.buildByUserIncomeThirdVo(shangyongData);
            this.userIncomeBiz.save(userIncomeThirdShangyong);
        }
        Map<String, String> resultMap = Maps.newHashMap();
        resultMap.put("data", userIncomeThirdShangyong.getThirdData());
        JSONObject jsonObject = JSON.parseObject(userIncomeThirdShangyong.getThirdData());
        if(jsonObject.getJSONObject("data") != null) {
            String wealthCode = jsonObject.getJSONObject("data").getString("wealthCode");
            UserInComeShangyongEnum.WealthCode wealthCodeEnum = UserInComeShangyongEnum.WealthCode.getByCode(wealthCode);
            resultMap.put("level", wealthCodeEnum == null ? "" : wealthCodeEnum.getName());
        }
        return resultMap;
    }

}
