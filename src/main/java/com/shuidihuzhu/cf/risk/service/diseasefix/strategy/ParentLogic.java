package com.shuidihuzhu.cf.risk.service.diseasefix.strategy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ParentLogic {

    @ApiModelProperty("判断规则")
    private String doType;

    @ApiModelProperty("全等匹配归一后疾病名")
    private String containsDisease;

    @ApiModelProperty("模糊匹配归一后疾病的治疗方案")
    private String containsLikeTreatment;

    @ApiModelProperty("全等匹配归一后疾病的治疗方案")
    private String containsTreatment;

    @ApiModelProperty("全等匹配关键词")
    private String containsKeywords;

    @ApiModelProperty("命中判断规则处理动作列表")
    private List<ChildLogic> yesDo;

    @ApiModelProperty("未命中判断规则处理动作列表")
    private List<ChildLogic> noDo;
}
