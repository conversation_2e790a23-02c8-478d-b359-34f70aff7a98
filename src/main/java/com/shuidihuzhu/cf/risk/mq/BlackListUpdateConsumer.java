package com.shuidihuzhu.cf.risk.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constants.MqTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = CfRiskMQTagCons.BLACK_LIST_UPDATE,
        group = CfRiskMQTagCons.BLACK_LIST_UPDATE + "_cf-risk-rpc-api",
        tags = CfRiskMQTagCons.BLACK_LIST_UPDATE,
        topic = MQTopicCons.CF)
@Slf4j
public class BlackListUpdateConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        final String payload = consumerMessage.getPayload();
        // 选择需要刷新缓存的类型发送刷新mq
        return MaliMQComponent.builder()
                .setTags(MqTagCons.BLACK_LIST_CACHE_MULTI_POD_UPDATE)
                .setPayload(payload)
                .send()
                .ok();
    }

    @Override
    protected int maxRetryCount() {
        return 1;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
