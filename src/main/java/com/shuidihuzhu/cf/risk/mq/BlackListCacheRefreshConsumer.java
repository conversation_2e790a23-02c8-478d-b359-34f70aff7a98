package com.shuidihuzhu.cf.risk.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.cache.blacklist.CfBlackListCacheService;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constants.MqTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MqTagCons.BLACK_LIST_CACHE_MULTI_POD_UPDATE,
        group = MqTagCons.BLACK_LIST_CACHE_MULTI_POD_UPDATE + "_cf-risk-rpc-api",
        tags = MqTagCons.BLACK_LIST_CACHE_MULTI_POD_UPDATE,
        topic = MQTopicCons.CF)
@Slf4j
public class BlackListCacheRefreshConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Autowired
    private CfBlackListCacheService cfBlackListCacheService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        log.info("触发黑名单缓存刷新");
        cfBlackListCacheService.refresh();
        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 1;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
