package com.shuidihuzhu.cf.risk.model.disease;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class DiseaseAmountSpecialInfo implements IDiseaseAmountInfo {

    private double minAmount;

    private double maxAmount;

    private boolean occupyPlace;

    private String diseaseClassName;

    private int raiseType;

    private String treatmentInfo;

    public static DiseaseAmountSpecialInfo create(double minAmount,
                                                  double maxAmount,
                                                  boolean occupyPlace,
                                                  String diseaseClassName,
                                                  int raiseType,
                                                  String treatmentInfo) {
        final DiseaseAmountSpecialInfo v = new DiseaseAmountSpecialInfo();
        v.setOccupyPlace(occupyPlace);
        v.setMinAmount(minAmount);
        v.setMaxAmount(maxAmount);
        v.setDiseaseClassName(diseaseClassName);
        v.setRaiseType(raiseType);
        v.setTreatmentInfo(treatmentInfo);
        return v;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiseaseAmountSpecialInfo that = (DiseaseAmountSpecialInfo) o;
        return StringUtils.equals(
                this.getDiseaseClassName(),
                that.getDiseaseClassName()
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(diseaseClassName);
    }
}
