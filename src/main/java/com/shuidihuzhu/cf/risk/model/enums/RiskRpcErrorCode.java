package com.shuidihuzhu.cf.risk.model.enums;

import com.shuidihuzhu.common.web.enums.MyErrorCode;

/**
 * <AUTHOR>
 * @date 2020/2/6
 */
public enum RiskRpcErrorCode implements MyErrorCode {

    SUCCESS(0, "success"),

    SYSTEM_PARAM_ERROR(10001, "参数错误"),
    SYSTEM_JSON_PARSE_ERROR(10002, "JSON解析错误"),
    SYSTEM_UPDATE_FAILED(10003, "更新失败"),
    SYSTEM_GET_FAILED(10004, "查看失败"),
    SYSTEM_PARAM_IS_NULL(10005, "参数为空"),
    SYSTEM_UNRECOGNIZED_ERROR(10201, "未识别错误"),
    SYSTEM_ERROR(10301, "系统错误"),
    ILLEGAL_ARGUMENT(10305, "参数错误"),

    INFO_NOT_EXIST(100005, "筹款项目不存在"),
    DISCUSSION_NOT_EXIST(100011, "项目暂无评议"),


    STRATEGY_REQUEST_ERROR(101001, "策略请求无效，请检查参数"),

    ;



    private int code;

    private String msg;

    RiskRpcErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }



    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
