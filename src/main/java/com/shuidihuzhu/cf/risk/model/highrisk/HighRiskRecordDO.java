package com.shuidihuzhu.cf.risk.model.highrisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HighRiskRecordDO {
    private long id;
    private int caseId;
    @ApiModelProperty("初版：0 v3版本： 3")
    private int version;
    @ApiModelProperty("工单id")
    private long orderId;
    @ApiModelProperty("操作人id")
    private long operatorId;
    @ApiModelProperty("是否命中")
    private boolean hit;
    @ApiModelProperty("调用来源 1: 增信, 2: 报备, 3: 手动")
    private int triggerSource;
    @ApiModelProperty("命中规则代码 eg: HRA001,HRA003,HRA013")
    private String hitCodes;
    @ApiModelProperty("调用入参")
    private String reqParam;

}
