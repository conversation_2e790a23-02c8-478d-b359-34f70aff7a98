package com.shuidihuzhu.cf.risk.model.vo;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.risk.model.CfRiskComment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/2/10
 */
@Data
@ApiModel("点赞的列表Vo")
public class CommentVo {
    @ApiModelProperty("评论id")
    private long id;
    @ApiModelProperty("评论内容")
    private String content;
    @ApiModelProperty("点赞数")
    private int praiseCount;
    @ApiModelProperty("昵称")
    private String nickName;
    @ApiModelProperty("头像")
    private String headUrl;
    @ApiModelProperty("当前用户是否点赞")
    private boolean praiseStatus;
    @ApiModelProperty("评论时间")
    private Timestamp commentTime;

    private long userId;

    public CommentVo(long id, String content, Timestamp commentTime) {
        this.id = id;
        this.content = content;
        this.commentTime = commentTime;
    }
}
