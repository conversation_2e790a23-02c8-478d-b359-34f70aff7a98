package com.shuidihuzhu.cf.risk.model.disease;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 */
@ApiModel("疾病花费信息")
public interface IDiseaseAmountInfo {

    /**
     * 获取最小金额
     * @return 最小金额
     */
    double getMinAmount();

    /**
     * 获取最大金额
     * @return 最大金额
     */
    double getMaxAmount();

    /**
     * 是否占用前n个疾病位置
     */
    boolean isOccupyPlace();

    String getDiseaseClassName();

    int getRaiseType();

    String getTreatmentInfo();
}
