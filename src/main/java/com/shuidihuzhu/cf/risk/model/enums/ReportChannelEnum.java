package com.shuidihuzhu.cf.risk.model.enums;

public enum ReportChannelEnum {

    //举报渠道
    USER_CALL(1, "用户来电举报"),
    USER_SERVICE(2, "用户在线客服举报"),
    SERVICE_SIGN(3,"客服团队标记举报"),
    AUDIT_REPORT(4,"预审团队标记举报"),
    MATERIAL_REPORT(5,"材审团队标记举报"),
    THIRD_REPORT(6,"第三方舆情监控举报"),
    OFFLINE_REPORT(7,"线下筹款顾问举报"),
    COMMUNICATION_REPORT(8,"首次沟通团队标记举报"),
    ORGANIZATION_REPORT(9,"相关机构反馈（公安机关、民政部等）"),
    MOBILE_REPORT(10,"用户移动端提交举报"),
    OTHER(11,"其他"),
    ABNORMAL_CAPITAL(12, "资金用途异常"),
    FUNDRAISING(13, "多次筹款"),
    ;

    int code;
    String description;

    ReportChannelEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (ReportChannelEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
