package com.shuidihuzhu.cf.risk.model.disease;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
public class RiskDiseaseData implements PageHasId {

    private long id;

    private String diseaseClassName;

    private String medicalName;

    private String normalName;

    private String diseaseMergeRule;

    private int raiseType;

    private String raiseTypeDesc;

    /**
     * @see com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum
     */
    private int choiceType;

    /**
     * 是否是高风险病种
     */
    private boolean highRisk;

    public void setRaiseTypeDesc(String raiseTypeDesc) {
        if (raiseType == 0) {
            this.raiseTypeDesc = "";
            return;
        }
        this.raiseTypeDesc = RaiseTypeEnum.findByCode(raiseType).desc;
    }

    @Getter
    public enum RaiseTypeEnum {

        DEFAULT(0, "默认类型"),
        CAN_RAISE(1, "可发起"),
        CAN_NOT_RAISE(2, "不可发起"),
        SPECIAL_RAISE(3, "特殊可发起"),
        ;
        int code;
        String desc;

        RaiseTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static RaiseTypeEnum findByCode(int code) {
            for (RaiseTypeEnum value : RaiseTypeEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    public enum TreatmentProjectEnum {
        no_limit(1, "无要求"),
        need_operation(2, "做手术"),
        steel_needle(3, "取钢针"),
        custom(4, "自定义"),
        ;
        int code;
        String desc;

        TreatmentProjectEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TreatmentProjectEnum findByCode(int code) {
            for (TreatmentProjectEnum value : TreatmentProjectEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }
}
