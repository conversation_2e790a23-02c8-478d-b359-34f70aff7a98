package com.shuidihuzhu.cf.risk.model.executestrategy;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Data
public class RiskExecuteStrategyTransit {

    /**
     * 是否继续 默认为继续
     */
    private boolean haNext = true;

    /**
     * 归一结果
     */
    private List<DiseaseClassifyVOV2> diseaseClassifyVOV2List = Lists.newArrayList();

    private List<String> mergeResult  = Lists.newArrayList();

    //归一疾病的可发类型
    private List<DiseaseInfoVo> diseaseInfoList = Lists.newArrayList();

    private List<String> matchList  = Lists.newArrayList();

    private String matchLocation = "";

    private String treatmentInfo;

    private RiskDiseaseData.RaiseTypeEnum caseRaiseType;

    /**
     * 目标金额 单位元
     */
    private int targetAmount;

    private boolean judgeRaise;
    //是否判断过待录入
    private boolean judgeMaterialType;

    private InfoReasonableAmountResultVo resultVo;

    private long prePoseId;

    public void setDiseaseClassifyVOV2List(List<DiseaseClassifyVOV2> diseaseClassifyVOV2List) {
        this.diseaseClassifyVOV2List = diseaseClassifyVOV2List;
        this.mergeResult =diseaseClassifyVOV2List.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .reduce(Lists.newArrayList(), (all, v) ->
                {
                    all.addAll(v);
                    return all;
                });
    }
}
