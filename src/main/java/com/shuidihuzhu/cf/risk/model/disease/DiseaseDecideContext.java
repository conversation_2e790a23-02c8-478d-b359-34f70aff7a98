package com.shuidihuzhu.cf.risk.model.disease;

import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.JudgeDiseaseInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class DiseaseDecideContext {

    @ApiModelProperty("入参疾病")
    private List<RiskDiseaseData> allDiseaseDataList;

    @ApiModelProperty("入参")
    private DecideReasonableInfo decideReasonableInfo;

    @ApiModelProperty("结果")
    private InfoReasonableAmountResultVo result;

    @ApiModelProperty("疾病花费信息列表 占位")
    private Set<IDiseaseAmountInfo> diseaseAmountInfoList;

    @ApiModelProperty("疾病花费信息列表 不占位置")
    private Set<IDiseaseAmountInfo> diseaseAmountInfoNoPlaceList;

    @ApiModelProperty("疾病花费信息列表 所有")
    private Set<IDiseaseAmountInfo> diseaseAmountInfoAllList;

    @ApiModelProperty("所有的方案选项信息")
    private Map<String, List<Long>> allChoiceMap;

    @ApiModelProperty("所有的方案详情")
    private List<JudgeDiseaseInfoVO> judgeDiseaseInfoList;

    @ApiModelProperty("合并症信息")
    private List<String> complications;

}
