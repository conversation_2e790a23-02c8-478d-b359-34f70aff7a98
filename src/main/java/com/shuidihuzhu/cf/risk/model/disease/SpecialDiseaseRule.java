package com.shuidihuzhu.cf.risk.model.disease;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseRuleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9
 * 特殊疾病规则
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpecialDiseaseRule {
    private long id;
    private String diseaseClass;
    private String diseaseClassName;
    private String diseaseContainClassName;
    private double mergeAmount;
    private int ariseCount;
    /**
     * @see SpecialDiseaseRuleTypeEnum
     */
    private int type;

    private List<String> diseaseContainClassNameList;

    public void setDiseaseContainClassName(String diseaseContainClassName) {
        this.diseaseContainClassName = diseaseContainClassName;
        if (StringUtils.isNotBlank(diseaseContainClassName)){
            diseaseContainClassNameList = Splitter.on(",")
                    .splitToList(diseaseContainClassName);
        }
    }
}
