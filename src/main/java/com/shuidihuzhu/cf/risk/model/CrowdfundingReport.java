package com.shuidihuzhu.cf.risk.model;

import com.shuidihuzhu.cf.enums.crowdfunding.ReportSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by roy on 16/7/29.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CrowdfundingReport {
    /**
     * CREATE TABLE `shuidi_dev`.`crowdfunding_report`(
     * `id` INT(11) NOT NULL AUTO_INCREMENT comment 'id',
     * `user_id` INT(11) DEFAULT null comment '用户id',
     * `activity_id` int(11) NOT NULL COMMENT '众筹id',
     * `image_urls` TEXT DEFAULT null COMMENT '图片地址',
     * `content` TEXT comment '举报内容',
     * `contact` TEXT NOT NULL COMMENT '联系方式',
     * `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
     * `is_newreport` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为新增举报',
     * `case_follow_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '举报所属案例的跟进状态 0 未跟进  1 跟进中  2  案例跟进完成',
     * <p>
     * PRIMARY KEY (`id`),
     * KEY `idx_user_id`(`user_id`),
     * KEY `idx_activity_id`(`activity_id`)
     * ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment='众筹举报表';
     */
    private Integer id;
    private Integer activityId;
    private String content;
    private String contact;
    private String imageUrls;
    private long userId;
    private Date createTime;
    private int operatorId;
    private int dealStatus;
    private int isNewreport;
    private int caseFollowStatus;
    private Timestamp lastModified;
    //0:默认值(老数据都是0) 1:实名举报 2:非实名举报
    private int realNameReport;
    private String name;
    private String identity;
    //处理状态 0:默认(历史数据) 1:未处理 2:已处理(举报重构增加的新字段)
    private int handleStatus;
    //接通状态 0:默认(历史数据) 1:未接通 2:已接通(举报重构增加的新字段)
    private int connectStatus;
    //举报新逻辑的操作人
    private int newOperatorId;
    /**
     * 是否命中黑名单
     */
    private boolean hitBlackList;
    /**
     * 举报渠道
     */
    private int reportChannel;
    /**
     * 其他举报渠道
     */
    private String reportChannelOther;
    /**
     * 风险标签
     */
    private String riskLabel;

    /**
     * 参加ReportSourceEnum, 默认值为通过老环境发起
     *
     * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.ReportSourceEnum}
     */
    private int reportSource = ReportSourceEnum.ORIGIN.getSource();

}
