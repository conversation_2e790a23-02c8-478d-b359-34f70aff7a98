package com.shuidihuzhu.cf.risk.model.po.hit;

import lombok.Data;

import java.util.Date;

@Data
public class RiskStrategyHitRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 案例发起时间
     */
    private Date launchTime;

    /**
     * 命中时机,BlacklistCallPhaseEnum
     */
    private Integer hitPhase;

    /**
     * 命中策略,RiskStrategyEnum
     */
    private Integer riskStrategy;

    /**
     * 命中二级策略，RiskStrategySecondEnum
     */
    private Integer secondStrategy;

    /**
     * 风险类型：IdentifySpotInitiatorHitEnum
     */
    private Integer riskType;

    /**
     * 处理状态，RiskHandleStatusEnum
     */
    private Integer status;

    /**
     * 风险核实结果，RiskHandleResultEnum
     */
    private Byte result;

    /**
     * 处理动作，RiskHandleActionEnum
     */
    private String action;

    /**
     * 处理次数
     */
    private Integer handleCount;

    /**
     * 是否操作过解锁，0 否，1 是
     */
    private Byte lifting;

    /**
     * 操作人
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 命中详情
     */
    private String hitInfo;

}