package com.shuidihuzhu.cf.risk.model.disease;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
public class RiskDiseaseTreatmentProject {

    private long id;

    private long diseaseId;

    private String projectName;

    private String projectMergeRule;

    private int minTreatmentFee;

    private int maxTreatmentFee;

    private List<Long> noSameTimeId;

    //1可发起 0不可发起
    private int raiseType;

    public void setNoSameTimeId(String noSameTimeId) {
        if (StringUtils.isBlank(noSameTimeId)){
            return;
        }
        List<String> noSameTimeIdList = Splitter.on(",").splitToList(noSameTimeId);
        if (CollectionUtils.isEmpty(noSameTimeIdList)) {
            return;
        }
        List<Long> ids = Lists.newArrayList();
        for (String id : noSameTimeIdList) {
            ids.add(Long.parseLong(id));
        }
        this.noSameTimeId = ids;
    }
}
