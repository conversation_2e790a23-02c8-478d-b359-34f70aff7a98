package com.shuidihuzhu.cf.risk.model.po.blacklist;

import java.util.Date;

public class RiskBlacklistData {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 通过用户id查询的用户绑定手机号
     */
    private String encryptMobileBind;

    /**
     * 用户身份证号
     */
    private String encryptIdCard;

    /**
     * 用户手机号
     */
    private String encryptMobile;

    /**
     * 用户手机号反查到的用户id
     */
    private Long userIdBind;

    /**
     * 用户出生证
     */
    private String encryptBornCard;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 操作原因
     */
    private String operateReason;

    /**
     * 操作人id
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 用户id
     * @return user_id 用户id
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 用户id
     * @param userId 用户id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 通过用户id查询的用户绑定手机号
     * @return encrypt_mobile_bind 通过用户id查询的用户绑定手机号
     */
    public String getEncryptMobileBind() {
        return encryptMobileBind;
    }

    /**
     * 通过用户id查询的用户绑定手机号
     * @param encryptMobileBind 通过用户id查询的用户绑定手机号
     */
    public void setEncryptMobileBind(String encryptMobileBind) {
        this.encryptMobileBind = encryptMobileBind == null ? null : encryptMobileBind.trim();
    }

    /**
     * 用户身份证号
     * @return encrypt_id_card 用户身份证号
     */
    public String getEncryptIdCard() {
        return encryptIdCard;
    }

    /**
     * 用户身份证号
     * @param encryptIdCard 用户身份证号
     */
    public void setEncryptIdCard(String encryptIdCard) {
        this.encryptIdCard = encryptIdCard == null ? null : encryptIdCard.trim();
    }

    /**
     * 用户手机号
     * @return encrypt_mobile 用户手机号
     */
    public String getEncryptMobile() {
        return encryptMobile;
    }

    /**
     * 用户手机号
     * @param encryptMobile 用户手机号
     */
    public void setEncryptMobile(String encryptMobile) {
        this.encryptMobile = encryptMobile == null ? null : encryptMobile.trim();
    }

    /**
     * 用户手机号反查到的用户id
     * @return user_id_bind 用户手机号反查到的用户id
     */
    public Long getUserIdBind() {
        return userIdBind;
    }

    /**
     * 用户手机号反查到的用户id
     * @param userIdBind 用户手机号反查到的用户id
     */
    public void setUserIdBind(Long userIdBind) {
        this.userIdBind = userIdBind;
    }

    /**
     * 用户出生证
     * @return encrypt_born_card 用户出生证
     */
    public String getEncryptBornCard() {
        return encryptBornCard;
    }

    /**
     * 用户出生证
     * @param encryptBornCard 用户出生证
     */
    public void setEncryptBornCard(String encryptBornCard) {
        this.encryptBornCard = encryptBornCard == null ? null : encryptBornCard.trim();
    }

    /**
     * 用户姓名
     * @return user_name 用户姓名
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 用户姓名
     * @param userName 用户姓名
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 操作原因
     * @return operate_reason 操作原因
     */
    public String getOperateReason() {
        return operateReason;
    }

    /**
     * 操作原因
     * @param operateReason 操作原因
     */
    public void setOperateReason(String operateReason) {
        this.operateReason = operateReason == null ? null : operateReason.trim();
    }

    /**
     * 操作人id
     * @return operate_id 操作人id
     */
    public Long getOperateId() {
        return operateId;
    }

    /**
     * 操作人id
     * @param operateId 操作人id
     */
    public void setOperateId(Long operateId) {
        this.operateId = operateId;
    }

    /**
     * 操作人
     * @return operate_name 操作人
     */
    public String getOperateName() {
        return operateName;
    }

    /**
     * 操作人
     * @param operateName 操作人
     */
    public void setOperateName(String operateName) {
        this.operateName = operateName == null ? null : operateName.trim();
    }

    /**
     * 是否删除，0 否，1 是
     * @return is_delete 是否删除，0 否，1 是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除，0 否，1 是
     * @param isDelete 是否删除，0 否，1 是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}