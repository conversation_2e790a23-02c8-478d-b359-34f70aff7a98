package com.shuidihuzhu.cf.risk.model.enums;

/**
 * 审核状态枚举
 * <AUTHOR>
 * @date 2020/2/6
 *
 * check_status	TINTINT	审核状态（0下发，1提交未审核，2通过，3拒绝）
 */
public enum DiscussionCheckStatusEnum {

    ISSUE(0, "评议下发"),
    WAIT_APPROVE(1, "评议待审核"),
    APPROVE_SUCCESS(2, "评议通过"),
    APPROVE_FAIL(3, "评议拒绝"),
    ;

    int code;
    String description;

    DiscussionCheckStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
