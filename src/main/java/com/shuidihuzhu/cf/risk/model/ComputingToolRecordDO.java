package com.shuidihuzhu.cf.risk.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ComputingToolRecordDO {
    /**
     * CREATE TABLE `shuidi_cf_risk`.`computing_tool_record` (
     *   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
     *   `ext_name` varchar(50) NOT NULL DEFAULT '' COMMENT '扩展名称',
     *   `ext_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '扩展id',
     *   `source_json` text NOT NULL COMMENT '提交json',
     *   `result_json` text NOT NULL COMMENT '计算结果json',
     *   `operator` varchar(100) NOT NULL DEFAULT 'system' COMMENT '操作人',
     *   `operator_org` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人组织',
     *   `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否逻辑删除 0有效，1删除',
     *   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     *   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     *   PRIMARY KEY (`id`),
     *   KEY `idx_create_time` (`create_time`),
     *   KEY `idx_update_time` (`update_time`)
     * ) ENGINE=InnoDB AUTO_INCREMENT=259 DEFAULT CHARSET=utf8mb4 COMMENT='家庭计算小工具使用记录表';
     */

    private long id;
    private String extName;
    private long extId;
    private String sourceJson;
    private String resultJson;
    private String operator;
    private String operatorOrg;
    private String createTime;
    private String updateTime;

}
