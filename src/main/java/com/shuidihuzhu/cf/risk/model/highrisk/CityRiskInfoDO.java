package com.shuidihuzhu.cf.risk.model.highrisk;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * CREATE TABLE `shuidi_cf_risk`.`city_risk_info` (
 *   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
 *   `province` varchar(10) NOT NULL DEFAULT '' COMMENT '省',
 *   `city` varchar(50) NOT NULL DEFAULT '' COMMENT '市',
 *   `county` varchar(50) NOT NULL DEFAULT '' COMMENT '区/县',
 *   `city_level` bigint(200) NOT NULL DEFAULT '' COMMENT '城市等级',
 *   `use_community` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否填到小区',
 *   `use_floor` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否填写层数',
 *   `city_amount_threshold` bigint(4) NOT NULL DEFAULT '0' COMMENT '城市房产阈值 单位: 万',
 *   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 *   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 *   `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
 *   PRIMARY KEY (`id`),
 *   UNIQUE KEY `uniq_province_city_county` (`province`,`city`,`county`),
 *   KEY `idx_create_time` (`create_time`),
 *   KEY `idx_update_time` (`update_time`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='城市风控信息表';
 * <AUTHOR>
 */
@Data
public class CityRiskInfoDO {
    private Long id;
    private String province;
    private String city;
    private String county;
    private Integer cityLevel;
    @ApiModelProperty(value = "是否填到小区 0:否 1:是")
    private Integer useCommunity;
    @ApiModelProperty(value = "是否填写层数 0:否 1:是")
    private Integer useFloor;
    private Integer cityAmountThreshold;
}
