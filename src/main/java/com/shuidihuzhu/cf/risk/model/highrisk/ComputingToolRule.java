package com.shuidihuzhu.cf.risk.model.highrisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ComputingToolRule {
    @ApiModelProperty("规则代码")
    private String ruleCode;

    private String msg;

    @ApiModelProperty("风险点")
    private String riskPoint;

    @ApiModelProperty("发起情况")
    private int raiseType;

    @ApiModelProperty("命中高价值房产(刚需)扣减")
    private boolean hitHighValueHouse;

    @ApiModelProperty("命中闲置房产总净值扣减")
    private boolean hitIdleHouse;

    @ApiModelProperty("命中非营生车总净值扣减")
    private boolean hitNonBusinessCar;
}
