package com.shuidihuzhu.cf.risk.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 评论总表
 * <AUTHOR>
 * @date 2020/2/7
 *
 * id	BIGINT	主键
 * case_id	VARCHAR	案例id
 * biz_id	BIGINT	业务对应的id
 * user_id  BIGINT	用户id
 * content	VARCHAR（200）	用户评论内容
 * sensitive_word	VARCHAR（30）	命中的敏感词列表
 * sensitive_status	boolean	是否包含敏感词:
 * 0表示无敏感词
 * 1表示有敏感词
 * type	TINYINT	1：评议评论
 */
@Data
public class CfRiskComment {
    private long id;
    private long caseId;
    private long bizId;
    private long userId;
    private String content;
    private String sensitiveWord = "";
    private boolean sensitiveStatus;
    private int type;
    private Timestamp createTime;

}
