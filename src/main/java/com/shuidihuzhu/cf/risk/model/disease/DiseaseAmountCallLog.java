package com.shuidihuzhu.cf.risk.model.disease;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/28
 * 疾病库调用日志
 */
@Data
public class DiseaseAmountCallLog {

    private long id;
    private long caseId;
    private int workOrderId;
    private int targetAmountInFen;
    private boolean canRaiseStatus;
    private String requestDisease;
    /**
     * 建议最小金额 单位：元
     */
    private int adviseMinAmount;

    /**
     * 建议最大金额 单位：元
     */
    private int adviseMaxAmount;
    private String calculateDiseaseName;
    private String treatmentInfo;
    private String channel;


}
