package com.shuidihuzhu.cf.risk.model.highrisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CityThreshold {

    @ApiModelProperty("房产审核阈值")
    private int houseAuditInYuan;

    @ApiModelProperty("房产上限阈值")
    private int houseMaxInYuan;

    @ApiModelProperty("车产审核阈值")
    private int carAuditInYuan;

    @ApiModelProperty("车产上限阈值")
    private int carMaxInYuan;

    @ApiModelProperty("年收入审核阈值")
    private int yearIncomeAuditInYuan;
}
