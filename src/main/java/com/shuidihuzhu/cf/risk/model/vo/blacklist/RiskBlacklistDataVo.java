package com.shuidihuzhu.cf.risk.model.vo.blacklist;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataActionRef;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@NoArgsConstructor
@Data
public class RiskBlacklistDataVo {

    public RiskBlacklistDataVo(String data, int verifyType, String operateReason,
                               String userName, List<RiskBlacklistDataActionRef> dataActionRefs,
                               List<Long> dataTypeIds){
        this.id = dataActionRefs.get(0).getId();
        this.verifyType = verifyType;
        this.data = data;
        this.userName = userName;
        this.operateReason = operateReason;
        this.typeIds = dataTypeIds;
        this.actionIds = dataActionRefs.stream().map(RiskBlacklistDataActionRef::getActionId).collect(Collectors.toList());
    }

    public static List<RiskBlacklistDataVo> convertValidDataList(List<RiskBlacklistDataActionRef> dataActionRef,
                                                                                                         RiskBlacklistData blacklistDataList,
                                                                                                         List<Long> dataTypeIds){
        List<RiskBlacklistDataVo> riskBlacklistDataVos = Lists.newArrayListWithCapacity(6);

        String operateReason = "";//blacklistDataList.getOperateReason();太大了，暂时没用，不放到内存了
        if (blacklistDataList.getUserId() > 0) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getUserId()+"", BlacklistVerifyTypeEnum.USER_ID.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (blacklistDataList.getUserIdBind() > 0) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getUserIdBind()+"", BlacklistVerifyTypeEnum.USER_ID.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (StringUtils.isNotBlank(blacklistDataList.getEncryptMobile())) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getEncryptMobile(), BlacklistVerifyTypeEnum.MOBILE.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (StringUtils.isNotBlank(blacklistDataList.getEncryptMobileBind())) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getEncryptMobileBind(), BlacklistVerifyTypeEnum.MOBILE.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (StringUtils.isNotBlank(blacklistDataList.getEncryptIdCard())) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getEncryptIdCard(), BlacklistVerifyTypeEnum.ID_CARD.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (StringUtils.isNotBlank(blacklistDataList.getEncryptBornCard())) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getEncryptBornCard(), BlacklistVerifyTypeEnum.BORN_CARD.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }
        if (StringUtils.isNotEmpty(blacklistDataList.getUserName())) {
            riskBlacklistDataVos.add(new RiskBlacklistDataVo(blacklistDataList.getUserName(), BlacklistVerifyTypeEnum.USER.getCode(),
                    operateReason, blacklistDataList.getUserName(), dataActionRef, dataTypeIds));
        }

        return riskBlacklistDataVos;
    }


    /**
     * {@link RiskBlacklistDataActionRef} 主键
     */
    private Long id;

    /**
     * 验证类型 {@link BlacklistVerifyTypeEnum}
     */
    private int verifyType;

    /**
     * 黑名单数据内容
     */
    private String data;

    /**
     * 黑名单分类ids
     */
    private List<Long> typeIds;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 操作原因
     */
    private String operateReason;

    /**
     * 动作列表
     */
    private List<Long> actionIds;

}