package com.shuidihuzhu.cf.risk.model.highrisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * 每个规则对应的动作
 */
@Data
@NoArgsConstructor
public class RiskRuleResult {

    @ApiModelProperty("规则代码")
    private String ruleCode;

    private String msg;

    @ApiModelProperty("附加动作 10 请综合评估发起方案  20 请升级应急组处理")
    private int extAction;

    @ApiModelProperty("标红字段 eg 49,50")
    private List<Integer> redFieldCodes;

    @ApiModelProperty("风险点")
    private String riskPoint;

    @ApiModelProperty("审核方式")
    private String auditMethod;

    @ApiModelProperty("代录入文案")
    private String daiLuRu;

    @ApiModelProperty("可发起")
    private boolean canRaise;

    @ApiModelProperty("扣减金额")
    private int deductAmount;


    public RiskRuleResult(String ruleCode, String msg, int extAction, List<Integer> redFieldCodes) {
        this.ruleCode = ruleCode;
        this.msg = msg;
        this.extAction = extAction;
        this.redFieldCodes = redFieldCodes;
    }
}
