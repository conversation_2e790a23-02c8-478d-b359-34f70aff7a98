package com.shuidihuzhu.cf.risk.model.enums;

/**
 * <AUTHOR>
 * @date 2020/2/6
 * status	TINYINT	决议状态（0关闭，1开启）
 */
public enum DiscussionPublicityStatusEnum {
    OFF(0, "关闭"),
    ON(1, "开始"),
    ;

    int code;
    String description;

    DiscussionPublicityStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
