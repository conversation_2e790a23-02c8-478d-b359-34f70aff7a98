package com.shuidihuzhu.cf.risk.model.disease;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class DiseaseAmountTreatmentInfo implements IDiseaseAmountInfo{

    private boolean occupyPlace;

    private RiskDiseaseTreatmentProject treatment;

    private String diseaseClassName;

    private int raiseType;

    private String treatmentInfo;

    public static DiseaseAmountTreatmentInfo create(RiskDiseaseData diseaseData,
                                                    RiskDiseaseTreatmentProject treatment,
                                                    boolean occupyPlace) {
        final DiseaseAmountTreatmentInfo v = new DiseaseAmountTreatmentInfo();
        v.setTreatment(treatment);
        v.setOccupyPlace(occupyPlace);
        v.setDiseaseClassName(diseaseData.getDiseaseClassName());
        v.setRaiseType(diseaseData.getRaiseType());
        v.setTreatmentInfo(treatment.getProjectName());
        return v;
    }

    @Override
    public double getMinAmount() {
        return treatment.getMinTreatmentFee();
    }

    @Override
    public double getMaxAmount() {
        return treatment.getMaxTreatmentFee();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiseaseAmountTreatmentInfo that = (DiseaseAmountTreatmentInfo) o;
        return StringUtils.equals(
                this.getDiseaseClassName() + this.getTreatment().getDiseaseId(),
                that.getDiseaseClassName() + that.getTreatment().getDiseaseId()
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(diseaseClassName, treatment.getId());
    }
}
