package com.shuidihuzhu.cf.risk.model.enums;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 特殊规则枚举 type
 * <AUTHOR>
 * @date 2020/6/8
 */
public enum SpecialDiseaseRuleTypeEnum {

    DEFAULT(0, "默认"),
    OTHER(1, "替换"),
    MERGE(2, "合并"),
    ;

    int code;
    String description;

    SpecialDiseaseRuleTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Map<Integer, SpecialDiseaseRuleTypeEnum> map;

    static {
        map = Maps.newHashMap();
        for (SpecialDiseaseRuleTypeEnum value : SpecialDiseaseRuleTypeEnum.values()) {
            map.put(value.getCode(), value);
        }
    }


    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }


    public static SpecialDiseaseRuleTypeEnum getByCode(String code) {
        if(StringUtils.isBlank(code)) {
            return null;
        }
        return map.get(code);
    }
}
