package com.shuidihuzhu.cf.risk.model.enums;

import com.shuidihuzhu.cf.risk.model.Discussion;
import com.shuidihuzhu.cf.risk.model.DiscussionDTO;
import com.shuidihuzhu.common.util.DateUtil;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2020/2/6
 *
 * 哪里评议状态枚举
 * 0:无评议
 * 1:下发评议
 * 2:提交评议待审核
 * 3:评议驳回
 * 4:评议公式中
 * 5:评议结束
 */
public enum DiscussionStatusEnum {
    NO(0, "无评议"),
    DISCUSSION_ISSUE(1, "评议下发"),
    DISCUSSION_WAIT_APPROVE(2, "评议待审核"),
    DISCUSSION_APPROVE_FAIL(3, "评议驳回"),
    DISCUSSION_PUBLICITY(4, "评议公示中"),
    DISCUSSION_END(5, "评议结束"),
    ;

    int code;
    String description;

    DiscussionStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static int getDisscussionStatus(DiscussionDTO discussion) {
        if (discussion == null || discussion.getStatus() == DiscussionPublicityStatusEnum.OFF.code) {
            return NO.code;
        }
        //如果评议审核状态不是审核通过  则返回对应状态
        if (discussion.getCheckStatus() == DiscussionCheckStatusEnum.APPROVE_SUCCESS.code){
            if (discussion.getCloseTime().before(DateUtil.getCurTimestamp())) {
                return  DISCUSSION_END.code;
            }
            return DISCUSSION_PUBLICITY.code;
        } else if (discussion.getCheckStatus() == DiscussionCheckStatusEnum.ISSUE.code) {
            return DISCUSSION_ISSUE.code;
        } else if (discussion.getCheckStatus() == DiscussionCheckStatusEnum.WAIT_APPROVE.code) {
            return DISCUSSION_WAIT_APPROVE.code;
        } else if (discussion.getCheckStatus() == DiscussionCheckStatusEnum.APPROVE_FAIL.code) {
            return DISCUSSION_APPROVE_FAIL.code;
        }
        return NO.code;
    }

    public static int getDisscussionStatus(Discussion discussion) {
        DiscussionDTO discussionDTO = new DiscussionDTO();
        BeanUtils.copyProperties(discussion, discussionDTO);
        return getDisscussionStatus(discussionDTO);
    }


    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
