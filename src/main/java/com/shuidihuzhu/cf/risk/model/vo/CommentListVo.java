package com.shuidihuzhu.cf.risk.model.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/10
 */
@Data
public class CommentListVo {
    private int pageSize;
    private int current;
    private boolean hasNext;


    private int commentCount;
    private List<CommentVo> commentList = Lists.newArrayList();

    private CommentListVo(int commentCount, int current, int pageSize) {
        this.commentCount = commentCount;
        this.current = current;
        this.pageSize = pageSize;
    }

    public static CommentListVo build(int commentCount, int current, int pageSize) {
        return new CommentListVo(commentCount, current, pageSize);
    }
}
