package com.shuidihuzhu.cf.risk.model.executestrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("日志")
public class RiskExecuteStrategyLog {

    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("工单id")
    private long workOrderId;
    @ApiModelProperty("调用时间")
    private Date executeTime;
    @ApiModelProperty("调用策略名称")
    private String strategyName;
    @ApiModelProperty("调用策略结果")
    private String strategyResult;
    @ApiModelProperty("结果描述")
    private String strategyResultDesc;
    @ApiModelProperty("其他信息")
    private String otherInfo;
    @ApiModelProperty("操作人及其组织架构")
    private String operator;
}
