package com.shuidihuzhu.cf.risk.model.enums;

import com.google.common.collect.Maps;

import java.util.Map;

public enum BlacklistWordTypeEnum {
    OTHER(0, "其他"),
    CROWDFUNDING(1, "筹款"),
    MINA(2, "小程序")
    ;

    private int code;
    private String desc;
    private static final Map<Integer, BlacklistWordTypeEnum> map = Maps.newHashMap();

    BlacklistWordTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Map<Integer, BlacklistWordTypeEnum> getMap() {
        return map;
    }

    static {
        for (BlacklistWordTypeEnum cfBlacklistWordTypeEnum : BlacklistWordTypeEnum.values()) {
            map.put(cfBlacklistWordTypeEnum.getCode(), cfBlacklistWordTypeEnum);
        }
    }

    public static BlacklistWordTypeEnum getByCode(int code) {
        return map.getOrDefault(code, BlacklistWordTypeEnum.OTHER);
    }

}
