package com.shuidihuzhu.cf.risk.model;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Optional;

@Data
public class PreposeMaterialInfo {
    private Long id;

    /**
     * bd的唯一标示
     */
    private String volunteerUniqueCode;

    // 是否远程发起   0 不是  1 是
    private int remoteRaise=-1;
    /**
     * 案例类型
     *
     * @see PreposeMaterialModel.AccidentType
     */
    private int accidentType=-1;

    /**
     * 事故定责
     * @see PreposeMaterialModel.AccidentDuty
     */
    private int accidentDuty=-1;

    /**
     * 事故经过
     */
    private String accidentDetail;

    /**
     * 赔偿信息
     *
     * @see PreposeMaterialModel.PayForState
     */
    private int payForState=-1;

    /**
     * 患者身份
     *
     * @see PreposeMaterialModel.PatientIdentity
     */
    private int patientIdentity=-1;

    /**
     * 发起人信息
     * 手机号 姓名 与患者关系
     *
     * @see PreposeMaterialModel.RaiserPatientRelation
     */
    private String raiseMobile;
    private String raiseName;
    private int raisePatientRelation=-1;

    /**
     * 患者信息
     * 姓名 疾病名称 发起医院 发起科室  身份证或出生证
     */
    private String patientName;
    private String diseaseName;
    private String diseaseDetail;


    // 1、身份证    2、出生证
    private int patientIdCardType=-1;
    private String patientIdCard;

    private String hospital;
    private String department;

    /**
     * 花费
     * 已花费 预计花费(医疗费用缺口) 筹款目标  单位：元
     */
    private int hasCost=-1;
    private int expectCost=-1;
    private int targetAmount=-1;
    /**
     * 车
     * 车数量、品牌、总价值  单位：元
     */
    /**
     * 新版本字段含义变了
     * 无车产、一辆车、两辆车、三辆及以上
     * @see PreposeMaterialModel.CarNumEnum
     */
    private int carNum=-1;
    private String carBrand;
    private int carValue=-1;

    /**
     * 是否变卖了。  0 没有 1、2、3辆
     * 新版本字段含义变了
     * @see PreposeMaterialModel.HasSellEnum
     * 未变卖、已变卖、变卖中
     */
    private int carHasSell=-1;
    private String carSellDetail;
    /**
     * 房
     * 房产价值 数量
     */
    /**
     * 新版本字段含义变了
     * @see PreposeMaterialModel.HouseNumEnum
     * 无房产、一套房、两套房、三套及以上
     */
    private int houseNum=-1;
    private int houseValue=-1;
    // 家庭年收入 单位 ： 元
    private int homeIncome=-1;
    private int homeDeposit=-1;
    /**
     * 新版本字段含义变了
     * @see PreposeMaterialModel.HasSellEnum
     * 未变卖、已变卖、变卖中
     */
    private int houseHasSell=-1;
    private String houseSellDetail;

    /**
     * 商保 医保  0没有 1 有
     * 保额单位：元
     */
    private int commercialInsurance=-1;
    /** 商保类型
     * @see PreposeMaterialModel.CommercialInsuranceType
     */
    private int commercialInsuranceType=-1;
    private int commercialAmount=-1;
    // 医保
    private int medicalInsurance=-1;
    /** 医保的类型
     * @see PreposeMaterialModel.MedicalInsuranceType
     */
    private int medicalInsuranceType=-1;
    // 医保报销比率
    private int medicalInsuranceRate=-1;

    // 贫困户 0 不是   1 是
    private int poverty=-1;

    // 是否有特殊补助：0 没有、1 有
    private int hasSupply=-1;
    private String supplyDetail;

    // 是否涉及媒体或政府关注：0未涉及、1 涉及了
    private int mediaGovernmentNotice=-1;

    /**
     * @see ENGLISH_COMMA
     * 发起材料-图片/视频  多个用 ENGLISH_COMMA 分隔
     */
    private String raiseMaterialPic;
    private String raiseMaterialVedio;

    /**
     * @see PreposeMaterialModel.MaterialType
     * 材料类型
     */
    private int materialType;

    private int riskLevel;
    private String riskLabels;
    private String comment;

    private Date createTime;
    private Date updateTime;

    private int materialId;

    private String operatorMis;

    private int preposeType;

    /***************************版本2.0 加字段*********************************/

    /**
     * 款项用途是否用于本人治疗
     * @see org.apache.commons.lang3.BooleanUtils
     * 0:flase   1:true
     */
    private int hasToPatientOwn=-1;

    /**
     * 所在单位是否允许筹款
     * @see org.apache.commons.lang3.BooleanUtils
     * 0:flase   1:true
     */
    private int companyCrowdfundingPermit=-1;
    /**
     * 变卖中的车产数量
     * @see PreposeMaterialModel.CarSellingCountEnum
     */
    private int carSellingCount=-1;

    /**
     * @see PreposeMaterialModel.CarSellingAmountAreaEnum
     * 车产总价值
     */
    private int carAmountArea=-1;
    /**
     * @see PreposeMaterialModel.CarSellingAmountAreaEnum
     * 变卖中的车产价值区间
     */
    private int carSellingAmountArea=-1;
    /**
     * 变卖中的车产价值
     */
    private int carSellingAmount=-1;
    /**
     * 变卖中房产数量
     * @see PreposeMaterialModel.HouseSellingCountEnum
     */
    private int houseSellingCount=-1;
    /**
     * @see PreposeMaterialModel.HouseSellingAmountAreaEnum
     * 房产总价值
     */
    private int houseAmountArea=-1;
    /**
     * @see PreposeMaterialModel.HouseSellingAmountAreaEnum
     * 变卖中房产价值区间
     */
    private int houseSellingAmountArea=-1;
    /**
     * 变卖中的房产价值
     */
    private int houseSellingAmount=-1;
    /**
     * @see PreposeMaterialModel.HomeIncomeAreaEnum
     * 年收入区间
     */
    private int homeIncomeArea=-1;
    /**
     * 是否有金融资产
     * @see org.apache.commons.lang3.BooleanUtils
     * 0:false，1：true
     */
    private int hasFinancialAssets=-1;
    /**
     * @see PreposeMaterialModel.FinancialAssetsAmountAreaEnum
     * 金融资产价值区间
     */
    private int financialAssetsAmountArea=-1;
    /**
     * 金融资产价值
     */
    private int financialAssetsAmount=-1;
    /**
     * 家庭欠款
     */
    private int homeOwningAmount=-1;
    /**
     * @see PreposeMaterialModel.MedicalInsuranceRateAreaEnum
     * 医保报销比例区间
     */
    private int medicalInsuranceRateArea=-1;
    /**
     * 医保可报销金额
     */
    private int medicalReimbursementAmount=-1;
    /**
     * 已报销金额
     */
    private int reimbursementedAmount=-1;
    /**
     * 是否有人身险
     */
    private int hasPersonalInsurance=-1;
    /**
     * 是否有车险等财产险
     */
    private int hasCarInsurance=-1;
    /**
     * 其他渠道救助金额(元)
     */
    private int supplyAmount=-1;
    /**
     * 政府医疗救助金额
     */
    private int mediaGovernmentAmount=-1;
    /**
     * 已赔付金额
     */
    private int paidAmount=-1;
    /**
     * 后续将赔付金额
     */
    private int willPayAmount=-1;

    /**
     * @see PreposeMaterialModel.RelationVersionEnum
     * 关系版本
     */
    private int relationVersion;
    /**
     * @see PreposeMaterialModel.VersionEnum
     * 报备字段大版本
     */
    private int version;

    /**
     * 是否均为自居住用房
     */
    private int selfOccupiedHouse=-1;
    /**
     * 出生年月日
     */
    private String birthday="";
    /**
     * 性别 1男  2女
     */
    private int sex=-1;

    /*******************前置报备1.9 优化拨备字段及流程 新增字段***********************/
    private String selfCryptoIdcard="";//'本人加密的身份证号',
    private String preAuditImageUrl="";//'上传医疗材料',
    private String title="";//'筹款标题',
    private String content=""; // '筹款文字内容',
    private String pictureUrl="";// '添加照片';

    /**
     * 后续将要赔付金额区间
     */
    private int willPayAmountArea=-1;

    private int communicationWay=0;

	/**
	 * 病房号
	 */
	private String sickroom;
	/**
	 * 病床号
	 */
	private String sickbed;

//******************2.0 新字段

    @ApiModelProperty("是否享受低保 0 否 1 是")
    private Integer livingAllowance=-1;

    @ApiModelProperty("低保上报证明")
    private String allowanceImg="";

    @ApiModelProperty("是否建档立卡贫困户 0 否 1 是")
    private Integer hasPoverty=-1;

    @ApiModelProperty("贫困户 上传证明")
    private String povertyImg="";

    @ApiModelProperty("是否在其他平台发起筹款, 0 否 1 是")
    private Integer hasRaise=-1;

    @ApiModelProperty("筹得款项 单位：元")
    private Integer raiseAmount=-1;

    @ApiModelProperty("剩余款项 单位：元")
    private Integer remainAmount=-1;

    @ApiModelProperty("是否用于医疗费用 0 否 1 是")
    private Integer useForMedical=-1;

    @ApiModelProperty("款项用途")
    private String moneyUseFor="";


	public PreposeMaterialInfo() {
    }

    public PreposeMaterialInfo(Long id, String volunteerUniqueCode, int remoteRaise, int accidentType, int accidentDuty, String accidentDetail,
                               int payForState, int patientIdentity, String raiseMobile, String raiseName, int raisePatientRelation,
                               String patientName, String diseaseName, String diseaseDetail, int patientIdCardType, String patientIdCard,
                               String hospital, String department, int hasCost, int expectCost, int targetAmount, int carNum, String carBrand,
                               int carValue, int carHasSell, String carSellDetail, int houseNum, int houseValue, int homeIncome, int homeDeposit,
                               int houseHasSell, String houseSellDetail, int commercialInsurance, int commercialInsuranceType, int commercialAmount,
                               int medicalInsurance, int medicalInsuranceType, int medicalInsuranceRate, int poverty, int hasSupply, String supplyDetail,
                               int mediaGovernmentNotice, String raiseMaterialPic, String raiseMaterialVedio, int materialType, int riskLevel, String comment,
                               String operatorMis, int preposeType , int hasToPatientOwn, int companyCrowdfundingPermit, int carSellingCount, int carAmountArea,
                               int carSellingAmountArea, int carSellingAmount, int houseSellingCount, int houseAmountArea, int houseSellingAmountArea,
                               int houseSellingAmount, int homeIncomeArea, int hasFinancialAssets, int financialAssetsAmountArea, int financialAssetsAmount,
                               int homeOwningAmount, int medicalInsuranceRateArea, int medicalReimbursementAmount, int reimbursementedAmount,
                               int hasPersonalInsurance, int hasCarInsurance, int supplyAmount, int mediaGovernmentAmount, int paidAmount, int willPayAmount,
                               int relationVersion, int version, int selfOccupiedHouse, String birthday, int sex, String selfCryptoIdcard, String preAuditImageUrl,
                               String title, String content, String pictureUrl, int willPayAmountArea, int communicationWay, String sickroom, String sickbed,
                               Integer livingAllowance, String allowanceImg, Integer hasPoverty, String povertyImg, Integer hasRaise,
                               Integer raiseAmount, Integer remainAmount, Integer useForMedical, String moneyUseFor
    ) {
        this.id = id;
        this.volunteerUniqueCode = volunteerUniqueCode;
        this.remoteRaise = remoteRaise;
        this.accidentType = accidentType;
        this.accidentDuty = accidentDuty;
        this.accidentDetail = accidentDetail;
        this.payForState = payForState;
        this.patientIdentity = patientIdentity;
        this.raiseMobile = raiseMobile;
        this.raiseName = raiseName;
        this.raisePatientRelation = raisePatientRelation;
        this.patientName = patientName;
        this.diseaseName = diseaseName;
        this.diseaseDetail = diseaseDetail;
        this.patientIdCardType = patientIdCardType;
        this.patientIdCard = patientIdCard;
        this.hospital = hospital;
        this.department = department;
        this.hasCost = hasCost;
        this.expectCost = expectCost;
        this.targetAmount = targetAmount;
        this.carNum = carNum;
        this.carBrand = carBrand;
        this.carValue = carValue;
        this.carHasSell = carHasSell;
        this.carSellDetail = carSellDetail;
        this.houseNum = houseNum;
        this.houseValue = houseValue;
        this.homeIncome = homeIncome;
        this.homeDeposit = homeDeposit;
        this.houseHasSell = houseHasSell;
        this.houseSellDetail = houseSellDetail;
        this.commercialInsurance = commercialInsurance;
        this.commercialInsuranceType = commercialInsuranceType;
        this.commercialAmount = commercialAmount;
        this.medicalInsurance = medicalInsurance;
        this.medicalInsuranceType = medicalInsuranceType;
        this.medicalInsuranceRate = medicalInsuranceRate;
        this.poverty = poverty;
        this.hasSupply = hasSupply;
        this.supplyDetail = supplyDetail;
        this.mediaGovernmentNotice = mediaGovernmentNotice;
        this.raiseMaterialPic = raiseMaterialPic;
        this.raiseMaterialVedio = raiseMaterialVedio;
        this.materialType = materialType;
        this.riskLevel = riskLevel;
        this.comment = comment;
        this.operatorMis = operatorMis;
        this.preposeType = preposeType;
        this.hasToPatientOwn = hasToPatientOwn;
        this.companyCrowdfundingPermit = companyCrowdfundingPermit;
        this.carSellingCount = carSellingCount;
        this.carAmountArea = carAmountArea;
        this.carSellingAmountArea = carSellingAmountArea;
        this.carSellingAmount = carSellingAmount;
        this.houseSellingCount = houseSellingCount;
        this.houseAmountArea = houseAmountArea;
        this.houseSellingAmountArea = houseSellingAmountArea;
        this.houseSellingAmount = houseSellingAmount;
        this.homeIncomeArea = homeIncomeArea;
        this.hasFinancialAssets = hasFinancialAssets;
        this.financialAssetsAmountArea = financialAssetsAmountArea;
        this.financialAssetsAmount = financialAssetsAmount;
        this.homeOwningAmount = homeOwningAmount;
        this.medicalInsuranceRateArea = medicalInsuranceRateArea;
        this.medicalReimbursementAmount = medicalReimbursementAmount;
        this.reimbursementedAmount = reimbursementedAmount;
        this.hasPersonalInsurance = hasPersonalInsurance;
        this.hasCarInsurance = hasCarInsurance;
        this.supplyAmount = supplyAmount;
        this.mediaGovernmentAmount = mediaGovernmentAmount;
        this.paidAmount = paidAmount;
        this.willPayAmount = willPayAmount;
        this.relationVersion = relationVersion;
        this.version = version;
        this.selfOccupiedHouse = selfOccupiedHouse;
        this.birthday = birthday;
        this.sex = sex;
        this.selfCryptoIdcard = selfCryptoIdcard;
        this.preAuditImageUrl = preAuditImageUrl;
        this.title = title;
        this.content = content;
        this.pictureUrl = pictureUrl;
        this.willPayAmountArea = willPayAmountArea;
        this.communicationWay = communicationWay;
        this.sickroom = sickroom;
        this.sickbed = sickbed;
        this.livingAllowance = livingAllowance;
        this.allowanceImg = allowanceImg;
        this.hasPoverty = hasPoverty;
        this.povertyImg = povertyImg;
        this.hasRaise = hasRaise;
        this.raiseAmount = raiseAmount;
        this.remainAmount = remainAmount;
        this.useForMedical = useForMedical;
        this.moneyUseFor = moneyUseFor;
    }

    public PreposeMaterialInfo(Long id, String volunteerUniqueCode, int remoteRaise, int accidentType, int accidentDuty, String accidentDetail,
                               int payForState, int patientIdentity, String raiseMobile, String raiseName, int raisePatientRelation,
                               String patientName, String diseaseName, String diseaseDetail, int patientIdCardType, String patientIdCard,
                               String hospital, String department, int hasCost, int expectCost, int targetAmount, int carNum, String carBrand,
                               int carValue, int carHasSell, String carSellDetail, int houseNum, int houseValue, int homeIncome, int homeDeposit,
                               int houseHasSell, String houseSellDetail, int commercialInsurance, int commercialInsuranceType, int commercialAmount,
                               int medicalInsurance, int medicalInsuranceType, int medicalInsuranceRate, int poverty, int hasSupply, String supplyDetail,
                               int mediaGovernmentNotice, String raiseMaterialPic, String raiseMaterialVedio, int materialType, int riskLevel,
                               String riskLabels, String comment, Date createTime, Date updateTime, int materialId, String operatorMis, int preposeType,
                               int hasToPatientOwn, int companyCrowdfundingPermit, int carSellingCount, int carAmountArea, int carSellingAmountArea,
                               int carSellingAmount, int houseSellingCount, int houseAmountArea, int houseSellingAmountArea, int houseSellingAmount,
                               int homeIncomeArea, int hasFinancialAssets, int financialAssetsAmountArea, int financialAssetsAmount, int homeOwningAmount,
                               int medicalInsuranceRateArea, int medicalReimbursementAmount, int reimbursementedAmount, int hasPersonalInsurance,
                               int hasCarInsurance, int supplyAmount, int mediaGovernmentAmount, int paidAmount, int willPayAmount, int relationVersion,
                               int version, int selfOccupiedHouse, String birthday, int sex, String selfCryptoIdcard, String preAuditImageUrl, String title,
                               String content, String pictureUrl, int willPayAmountArea, int communicationWay, String sickroom, String sickbed) {
        this.id = id;
        this.volunteerUniqueCode = volunteerUniqueCode;
        this.remoteRaise = remoteRaise;
        this.accidentType = accidentType;
        this.accidentDuty = accidentDuty;
        this.accidentDetail = accidentDetail;
        this.payForState = payForState;
        this.patientIdentity = patientIdentity;
        this.raiseMobile = raiseMobile;
        this.raiseName = raiseName;
        this.raisePatientRelation = raisePatientRelation;
        this.patientName = patientName;
        this.diseaseName = diseaseName;
        this.diseaseDetail = diseaseDetail;
        this.patientIdCardType = patientIdCardType;
        this.patientIdCard = patientIdCard;
        this.hospital = hospital;
        this.department = department;
        this.hasCost = hasCost;
        this.expectCost = expectCost;
        this.targetAmount = targetAmount;
        this.carNum = carNum;
        this.carBrand = carBrand;
        this.carValue = carValue;
        this.carHasSell = carHasSell;
        this.carSellDetail = carSellDetail;
        this.houseNum = houseNum;
        this.houseValue = houseValue;
        this.homeIncome = homeIncome;
        this.homeDeposit = homeDeposit;
        this.houseHasSell = houseHasSell;
        this.houseSellDetail = houseSellDetail;
        this.commercialInsurance = commercialInsurance;
        this.commercialInsuranceType = commercialInsuranceType;
        this.commercialAmount = commercialAmount;
        this.medicalInsurance = medicalInsurance;
        this.medicalInsuranceType = medicalInsuranceType;
        this.medicalInsuranceRate = medicalInsuranceRate;
        this.poverty = poverty;
        this.hasSupply = hasSupply;
        this.supplyDetail = supplyDetail;
        this.mediaGovernmentNotice = mediaGovernmentNotice;
        this.raiseMaterialPic = raiseMaterialPic;
        this.raiseMaterialVedio = raiseMaterialVedio;
        this.materialType = materialType;
        this.riskLevel = riskLevel;
        this.riskLabels = riskLabels;
        this.comment = comment;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.materialId = materialId;
        this.operatorMis = operatorMis;
        this.preposeType = preposeType;
        this.hasToPatientOwn = hasToPatientOwn;
        this.companyCrowdfundingPermit = companyCrowdfundingPermit;
        this.carSellingCount = carSellingCount;
        this.carAmountArea = carAmountArea;
        this.carSellingAmountArea = carSellingAmountArea;
        this.carSellingAmount = carSellingAmount;
        this.houseSellingCount = houseSellingCount;
        this.houseAmountArea = houseAmountArea;
        this.houseSellingAmountArea = houseSellingAmountArea;
        this.houseSellingAmount = houseSellingAmount;
        this.homeIncomeArea = homeIncomeArea;
        this.hasFinancialAssets = hasFinancialAssets;
        this.financialAssetsAmountArea = financialAssetsAmountArea;
        this.financialAssetsAmount = financialAssetsAmount;
        this.homeOwningAmount = homeOwningAmount;
        this.medicalInsuranceRateArea = medicalInsuranceRateArea;
        this.medicalReimbursementAmount = medicalReimbursementAmount;
        this.reimbursementedAmount = reimbursementedAmount;
        this.hasPersonalInsurance = hasPersonalInsurance;
        this.hasCarInsurance = hasCarInsurance;
        this.supplyAmount = supplyAmount;
        this.mediaGovernmentAmount = mediaGovernmentAmount;
        this.paidAmount = paidAmount;
        this.willPayAmount = willPayAmount;
        this.relationVersion = relationVersion;
        this.version = version;
        this.selfOccupiedHouse = selfOccupiedHouse;
        this.birthday = birthday;
        this.sex = sex;
        this.selfCryptoIdcard = selfCryptoIdcard;
        this.preAuditImageUrl = preAuditImageUrl;
        this.title = title;
        this.content = content;
        this.pictureUrl = pictureUrl;
        this.willPayAmountArea = willPayAmountArea;
        this.communicationWay = communicationWay;
        this.sickroom = sickroom;
        this.sickbed = sickbed;
    }

    public static String getIdCardHandleUpper(String idCard) {

        if (StringUtils.isEmpty(idCard)) {
            return idCard;
        }

        return idCard.toUpperCase();
    }
}
