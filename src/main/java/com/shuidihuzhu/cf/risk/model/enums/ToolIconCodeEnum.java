package com.shuidihuzhu.cf.risk.model.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 */
public enum ToolIconCodeEnum {
    // 1 可发 2 不可发 3 目标金额需调整
    CAN_RAISE(1, "可发"),
    CAN_NOT_RAISE(2, "不可发"),
    NEED_ADJUST(3, "目标金额需调整")
    ;

    private int code;
    private String desc;
    private static final Map<Integer, ToolIconCodeEnum> map = Maps.newHashMap();

    ToolIconCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Map<Integer, ToolIconCodeEnum> getMap() {
        return map;
    }

    static {
        for (ToolIconCodeEnum toolIconCodeEnum : ToolIconCodeEnum.values()) {
            map.put(toolIconCodeEnum.getCode(), toolIconCodeEnum);
        }
    }

    public static ToolIconCodeEnum getByCode(int code) {
        return map.getOrDefault(code, ToolIconCodeEnum.CAN_NOT_RAISE);
    }
}
