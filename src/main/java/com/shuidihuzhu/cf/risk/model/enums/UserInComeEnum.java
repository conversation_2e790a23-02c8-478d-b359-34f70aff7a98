package com.shuidihuzhu.cf.risk.model.enums;

/**
 * <AUTHOR>
 * @date 2020-02-19
 **/
public interface UserInComeEnum {

    public enum ThirdType {
        BAIRONG(1, "bairong"),
        SHANGYONG(2, "shangyong"),
        ;

        private int code;
        private String name;

        ThirdType(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    public enum ThirdStatus {
        SUCCESS(2),
        FAILED(3),
        ;

        private int code;

        ThirdStatus(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }
    }
}
