package com.shuidihuzhu.cf.risk.model;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class CfBlacklistWordDO {

    private long id;
    private String word;
    /**
     * @see com.shuidihuzhu.cf.risk.model.enums.BlacklistWordTypeEnum
     */
    private int type;
    private int disable;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public CfBlacklistWordDO() {
    }

    public CfBlacklistWordDO(String word, int type) {
        this.word = word;
        this.type = type;
    }

}
