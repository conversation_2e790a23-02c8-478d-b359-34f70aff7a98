package com.shuidihuzhu.cf.risk.model.ocr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("百度ocr识别返回")
@Data
public class OCRBaiDuResponse {

    @ApiModelProperty("唯一的log id，用于问题定位")
    private Long log_id;

    @ApiModelProperty("识别结果数组")
    private List<Result> words_result;

    @ApiModelProperty("识别结果数，表示words_result的元素个数")
    private Integer words_result_num;

    @Data
    public static class Result{

        @ApiModelProperty("识别结果字符串")
        private String words;
    }
}
