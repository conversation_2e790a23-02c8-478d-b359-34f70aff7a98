package com.shuidihuzhu.cf.risk.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class DiscussionCheckRecord implements PageHasId {

    private long id;
    private long discussionId;
    private int status;
    private String refuseReason;
    private String operator;
    private int refuseOption;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

}
