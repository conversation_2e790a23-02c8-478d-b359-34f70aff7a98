package com.shuidihuzhu.cf.risk.model.enums;

/**
 * <AUTHOR>
 */

public enum RiskCarUseType {
    // 车辆使用类型, 0：营生车，1 非营生车
    BUSINESS_CAR(0, "营生车"),
    NON_BUSINESS_CAR(1, "非营生车")
    ;
    int code;
    String desc;

    RiskCarUseType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RiskCarUseType getByCode(int code) {
        for (RiskCarUseType value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
