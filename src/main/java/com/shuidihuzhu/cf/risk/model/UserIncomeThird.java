package com.shuidihuzhu.cf.risk.model;

import com.shuidihuzhu.cf.risk.model.vo.UserIncomeThirdVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@Data
public class UserIncomeThird {

    private long id;
    private String orderNo;
    private String seqNo;
    private String idCard;
    private String realName;
    private String mobile;
    private int thirdType;
    private int thirdStatus;
    private String thirdNo;
    private String thirdData;
    private String modelType;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public void buildQuestParam(String orderNo, String seqNo, String idCard, String realName, String mobile) {
        this.setOrderNo(orderNo);
        this.setSeqNo(seqNo);
        this.setIdCard(idCard);
        this.setRealName(realName);
        this.setMobile(mobile);
    }

    public void buildByUserIncomeThirdVo(UserIncomeThirdVo userIncomeThirdVo) {
        this.setThirdStatus(userIncomeThirdVo.getStatus().getCode());
        this.setThirdType(userIncomeThirdVo.getType().getCode());
        this.setModelType(StringUtils.trimToEmpty(userIncomeThirdVo.getModelType()));
        this.setThirdData(StringUtils.trimToEmpty(userIncomeThirdVo.getData()));
        this.setThirdNo(StringUtils.trimToEmpty(userIncomeThirdVo.getThirdNo()));
    }
}
