package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeV2Service;
import com.shuidihuzhu.cf.risk.biz.impl.HighRiskV2Strategy;
import com.shuidihuzhu.cf.risk.client.highrisk.HighRiskClient;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskManualEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 11:51
 */
@Slf4j
@RestController
public class HighRiskController implements HighRiskClient {

    @Resource
    HighRiskJudgeV2Service highRiskJudgeV2Service;

    @Resource
    private HighRiskV2Strategy highRiskV2Strategy;

    @Override
    public Response<List<HighRiskRecordDto>> getListByCaseId(int caseId) {
        return highRiskJudgeV2Service.getLastByCaseId(caseId);
    }

    @Override
    public Response<List<String>> getHighRiskManualEnum() {
        return NewResponseUtil.makeSuccess(HighRiskManualEnum.getHighRiskManualAll());
    }

    @Override
    public Response<String> getCityLevelByCaseId(int caseId) {
        return highRiskV2Strategy.getCityLevelByCaseId(caseId);
    }
}
