package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeV2Service;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeService;
import com.shuidihuzhu.cf.risk.client.rpc.PreposeMaterialRiskClient;
import com.shuidihuzhu.cf.risk.model.risk.CfPreposeMaterialRiskVo;
import com.shuidihuzhu.cf.risk.model.risk.HouseThresholdParam;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoPrePoseModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-05-14
 **/
@Slf4j
@RestController
public class PreposeMaterialRiskController implements PreposeMaterialRiskClient {

    @Autowired
    private HighRiskJudgeService highRiskJudgeService;

    @Autowired
    private HighRiskJudgeV2Service highRiskJudgeV2Service;

    @Autowired
    private AlarmClient alarmClient;

    /**
     * 前置报备
     * @param info
     * @return
     */
    @Override
    public Response<CfPreposeMaterialRiskVo> markRisk(PreposeMaterialModel.MaterialInfoVo info) {
        log.info("markRisk : {}", JSON.toJSONString(info));
        CfPreposeMaterialRiskVo cfPreposeMaterialRiskVo = this.highRiskJudgeService.judgeDaiLuRu(info);
        log.info("markRisk : {}", JSON.toJSONString(cfPreposeMaterialRiskVo));
        return NewResponseUtil.makeSuccess(cfPreposeMaterialRiskVo);
    }

    /**
     * 增信
     * @param insuranceInfo
     * @return
     */
    @Override
    public Response<CfPreposeMaterialRiskVo> markPropertyRisk(CfPropertyInsuranceInfoModel insuranceInfo) {
        log.info("markPropertyRisk : {}", JSON.toJSONString(insuranceInfo));
        CfPreposeMaterialRiskVo cfPreposeMaterialRiskVo = this.highRiskJudgeService.judgeCase(insuranceInfo);
        log.info("markPropertyRisk : {}", JSON.toJSONString(cfPreposeMaterialRiskVo));
        return NewResponseUtil.makeSuccess(cfPreposeMaterialRiskVo);
    }

    @Override
    public Response<HighRiskJudgeResult> judgeHighRiskV2(HighRiskJudgeInfoModel infoModel) {
        try {
            return highRiskJudgeV2Service.judge(infoModel);
        } catch (Exception e) {
            log.error("高风险策略调用异常 normal error, infoModel:{}", JSON.toJSONString(infoModel), e);
            return NewResponseUtil.makeSuccess(getDefaultResult());
        }
    }

    @Override
    public Response<HighRiskJudgeResult> judgeHighRiskV2PrePose(HighRiskJudgeInfoPrePoseModel infoModel) {
        try {
            return highRiskJudgeV2Service.judgePrePose(infoModel);
        } catch (Exception e) {
            log.error("高风险策略调用异常 待录入 error, infoModel:{}", JSON.toJSONString(infoModel), e);
            return NewResponseUtil.makeSuccess(getDefaultResult());
        }
    }

    @Override
    public Response<Integer> getMResult(String patientIdCard, String raiserIdCard) {
        return highRiskJudgeV2Service.getMResult(patientIdCard, raiserIdCard);
    }

    @Override
    public Response<Integer> getMResultByParam(HouseThresholdParam houseThresholdParam) {
        return highRiskJudgeV2Service.getMResultByParam(houseThresholdParam);
    }

    @Override
    public Response<Integer> getHouseThreshold(String patientIdCard, String raiserIdCard) {
        return highRiskJudgeV2Service.getHouseThreshold(patientIdCard, raiserIdCard);
    }

    @NotNull
    private HighRiskJudgeResult getDefaultResult() {
        final HighRiskJudgeResult r = new HighRiskJudgeResult();
        r.setRiskTips(Lists.newArrayList());
        r.setRiskLabels(Lists.newArrayList());
        r.setHighRisk(false);
        r.setHitCodes(Sets.newHashSet());
        r.setTotalRiskTip("");
        return r;
    }
}

