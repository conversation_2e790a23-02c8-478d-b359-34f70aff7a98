package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.impl.HandleNetWorthService;
import com.shuidihuzhu.cf.risk.client.risk.RiskStrategyBizClient;
import com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.cf.risk.model.risk.CarNetValueInfo;
import com.shuidihuzhu.cf.risk.model.risk.ComputingRecord;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.cf.risk.model.risk.HouseNetValueInfo;
import com.shuidihuzhu.cf.risk.service.ComputingToolService;
import com.shuidihuzhu.cf.risk.service.RiskStrategyService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class RiskStrategyBizFeignController implements RiskStrategyBizClient {

    @Autowired
    private RiskStrategyService riskStrategyService;

    @Autowired
    private HandleNetWorthService handleNetWorthService;

    @Autowired
    private ComputingToolService computingToolService;

    @Override
    public Response<Boolean> checkIfLuxury(String carBrand) {
        return NewResponseUtil.makeSuccess(riskStrategyService.isFamousCar(carBrand));
    }

    @Override
    public Response<List<Integer>> countHouseNetValue(List<HouseNetValueInfo> houseValue) {
        if(CollectionUtils.isEmpty(houseValue)){
            return NewResponseUtil.makeSuccess();
        }
        return NewResponseUtil.makeSuccess(handleNetWorthService.countHouseNetValue(houseValue));
    }

    @Override
    public Response<List<Integer>> countCarNetValue(List<CarNetValueInfo> carNetValueInfos) {
        if(CollectionUtils.isEmpty(carNetValueInfos)){
            return NewResponseUtil.makeSuccess();
        }
        return NewResponseUtil.makeSuccess(handleNetWorthService.countCarNetValue(carNetValueInfos));
    }

    @Override
    public Response<ComputingToolResult> submit(String extName, long extId, long operatorId, String submitJson) {
        if(StringUtil.isBlank(submitJson)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return computingToolService.getResultAndSubmit(extName, extId, operatorId, submitJson);
    }

    @Override
    public Response<FamilyFinancialSituation> getRecordById(String extName, long extId, int recordId) {

        ComputingToolRecordDO recordDO= computingToolService.getRecordById(extName, extId, recordId);
        if(recordDO == null){
            return NewResponseUtil.makeSuccess();
        }
        if(StringUtil.isBlank(recordDO.getSourceJson())){
            return NewResponseUtil.makeError(ErrorCode.DEFAULT);
        }
        return NewResponseUtil.makeSuccess(JSONObject.parseObject(recordDO.getSourceJson(), FamilyFinancialSituation.class));
    }

    @Override
    public Response<List<ComputingRecord>> getRecordList(String extName, long extId) {
        return computingToolService.getRecordList(extName, extId);
    }

    @Override
    public Response<ComputingToolResult> getResultRecordById(String extName, long extId, int recordId) {
        ComputingToolRecordDO recordDO= computingToolService.getRecordById(extName, extId, recordId);
        if(recordDO == null){
            return NewResponseUtil.makeSuccess();
        }
        if(StringUtil.isBlank(recordDO.getResultJson())){
            return NewResponseUtil.makeError(ErrorCode.DEFAULT);
        }
        return NewResponseUtil.makeSuccess(JSONObject.parseObject(recordDO.getResultJson(), ComputingToolResult.class));
    }
}
