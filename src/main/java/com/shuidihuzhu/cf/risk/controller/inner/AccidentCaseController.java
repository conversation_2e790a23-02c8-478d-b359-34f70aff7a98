package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.biz.AccidentCaseBiz;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.model.risk.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 11:51
 */
@Slf4j
@RestController
public class AccidentCaseController implements AccidentCaseClient {

    @Autowired
    private AccidentCaseBiz accidentCaseBiz;

    @Override
    public Response<List<Integer>> followAccidentCaseStrategy(Participate participate) {
        return NewResponseUtil.makeSuccess(accidentCaseBiz.followAccidentCaseStrategy(participate));
    }

    @Override
    public Response<Boolean> judgeAccidentCaseStrategy(ParticipateCaseInfo participateCaseInfo) {
        return NewResponseUtil.makeSuccess(accidentCaseBiz.judgeAccidentCaseStrategy(participateCaseInfo));
    }
}
