//package com.shuidihuzhu.cf.risk.controller.inner;
//
//import com.shuidihuzhu.cf.risk.client.rpc.AIDiseaseNormClient;
//import com.shuidihuzhu.cf.risk.delegate.DiseaseNormDelegate;
//import com.shuidihuzhu.common.web.model.Response;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@RestController
//public class AIDiseaseNormController implements AIDiseaseNormClient {
//
//    @Resource
//    private DiseaseNormDelegate diseaseNormDelegate;
//    @Override
//    public Response<Map<String, Set<String>>> agent(List<String> diseaseList) {
//        return NewResponseUtil.makeSuccess(diseaseNormDelegate.agent(diseaseList));
//    }
//}
