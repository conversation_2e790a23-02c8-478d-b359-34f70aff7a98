package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cf.risk.model.risk.*;
import com.shuidihuzhu.cf.risk.service.ICfBlacklistService;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum.*;

/**
 * <AUTHOR>
 * @date 2020/8/6 11:51
 */
@Slf4j
@RestController
public class BlacklistVerifyController implements BlacklistVerifyClient {

    @Resource
    private ICfBlacklistService cfBlacklistService;

    @Override
    public Response<List<BlacklistVerifyDto>> verify(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        long start = System.currentTimeMillis();
        try {
            cfBlacklistService.checkHit(blacklistVerifyDtos);
        } catch (Exception e) {
            log.error("", e);
        }
        log.info("resp:{}, cost:{}", blacklistVerifyDtos, System.currentTimeMillis()-start);
        return NewResponseUtil.makeSuccess(blacklistVerifyDtos);
    }

    @Override
    public Response<List<BlacklistVerifyDto>> verifyV2(List<BlacklistVerifyDto> blacklistVerifyDtos) {
        long start = System.currentTimeMillis();
        try {
            cfBlacklistService.checkHitV2(blacklistVerifyDtos);
        } catch (Exception e) {
            log.error("", e);
        }
        log.info("verifyV2 resp:{}, cost:{}", blacklistVerifyDtos, System.currentTimeMillis()-start);
        return NewResponseUtil.makeSuccess(blacklistVerifyDtos);
    }

    @Override
    public Response<Boolean> preTrialAdoption(BlacklistPreTrialAdoptionDto preTrialAdoptionDto) {
        log.info("预审通过-验证是否命中黑名单, params:{}", preTrialAdoptionDto);

        boolean result = cfBlacklistService.checkPreTrialAdoption(preTrialAdoptionDto);

        log.info("预审通过-验证是否命中黑名单, resp:{}", result);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Boolean> payeeLimit(BlacklistPayeeLimitDto payeeLimitDto) {
        log.info("限制收款人通过-验证是否命中黑名单, params:{}", payeeLimitDto);

        boolean result = cfBlacklistService.checkPayeeLimit(payeeLimitDto);

        log.info("限制收款人通过-验证是否命中黑名单, resp:{}", result);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Boolean> launchCase(BlacklistLunchCaseDto lunchCaseDto) {
        log.info("发起案例-验证是否命中黑名单, params:{}", lunchCaseDto);

        if (StringUtils.isBlank(lunchCaseDto.getPatientIdNumber()) && lunchCaseDto.getPatientIdType() > 0 ||
                StringUtils.isNotBlank(lunchCaseDto.getPatientIdNumber()) && lunchCaseDto.getPatientIdType() <= 0) {
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "患者证件类型和患者证件号非法传参", null);
        }

        boolean result = cfBlacklistService.checkLaunchCase(lunchCaseDto);

        log.info("发起案例-验证是否命中黑名单, resp:{}", result);

        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<List<BlacklistVerifyDto>> reportQuestioner(BlacklistReportDto blacklistReportDto) {
        log.info("举报-验证是否命中黑名单, params:{}", blacklistReportDto);
        List<BlacklistVerifyDto> result;
        try {
            result = cfBlacklistService.checkFriendReport(blacklistReportDto, this::isReportLimitBackend);
        } catch (IllegalArgumentException e) {
            log.warn("", e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        log.info("举报-验证是否命中黑名单, resp:{}", result);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<List<BlacklistVerifyDto>> reportQuestionerFrontend(BlacklistReportDto blacklistReportDto) {
        log.info("举报-验证是否命中黑名单（限制举报）, params:{}", blacklistReportDto);
        List<BlacklistVerifyDto> result;
        try {
            result = cfBlacklistService.checkFriendReport(blacklistReportDto, this::isReportLimitFrontend);
        } catch (IllegalArgumentException e) {
            log.warn("", e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        log.info("举报-验证是否命中黑名单（限制举报）, resp:{}", result);
        return NewResponseUtil.makeSuccess(result);
    }

    private boolean isReportLimitBackend(Long limitId){
        return Set.of(FLAG_FRIEND_REPORT.getId(), FLAG_SUSPECT_FRIEND_REPORT.getId(), FORBIDDEN_REPORT.getId()).contains(limitId);
    }

    private boolean isReportLimitFrontend(Long limitId){
        return RESTRICT_REAL_NAME_REPORT.getId() == limitId;
    }

    @Override
    public Response<Boolean> queryBlackValid(long userId, int limitType) {

        CfRiskBlackListEnum.LimitType limitTypeEnum = CfRiskBlackListEnum.LimitType.getTypeByValue(limitType);
        if(userId <= 0 || limitTypeEnum == CfRiskBlackListEnum.LimitType.NO){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        return ResponseUtil.makeSuccess(cfBlacklistService.isBlackValid(userId, limitType));
    }

    @Override
    public Response<BlacklistHitHighRiskDto> queryBlackValidHighRisk(int caseId) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(cfBlacklistService.isBlackByHighRisk(caseId));
    }

}
