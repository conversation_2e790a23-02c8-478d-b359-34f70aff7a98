package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.rpc.DiscussionInfoClient;
import com.shuidihuzhu.cf.risk.model.DiscussionVO;
import com.shuidihuzhu.cf.risk.service.DiscussionService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/10
 */
@RestController
@Slf4j
public class DiscussionInnerController implements DiscussionInfoClient {

    @Autowired
    private DiscussionService discussionService;

    @Override
    public Response<List<DiscussionVO>> findDiscussionInfoByCaseId(@RequestParam("caseIdList")List<Integer> caseIdList) {
        if (CollectionUtils.isEmpty(caseIdList)) {
            return NewResponseUtil.makeSuccess(null);
        }
        return discussionService.findDiscussionInfoByCaseId(caseIdList);
    }
}
