package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.client.rpc.CfReportClient;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import com.shuidihuzhu.cf.risk.model.enums.RiskRpcErrorCode;
import com.shuidihuzhu.cf.risk.service.CfReportService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-04-01
 **/
@Slf4j
@RestController
public class CfReportClientController implements CfReportClient {

    @Autowired
    private CfReportService cfReportService;

    @Override
    public Response<List<ReportHitStrategyRecord>> getStrategyRecord(List<Integer> reportIds) {
        if (CollectionUtils.isEmpty(reportIds)) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        var strategyRecord = cfReportService.getStrategyRecord(reportIds);
        return NewResponseUtil.makeSuccess(strategyRecord);
    }

    @Override
    public Response addStrategyRecord(List<ReportHitStrategyRecord> reportHitStrategyRecords) {
        if (CollectionUtils.isEmpty(reportHitStrategyRecords)) {
            return NewResponseUtil.makeError(RiskRpcErrorCode.SYSTEM_PARAM_ERROR);
        }
        var result = cfReportService.addStrategyRecord(reportHitStrategyRecords);
        return NewResponseUtil.makeSuccess(result);
    }

    public static void main(String[] args) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("content", "迁移测试");
        params.add("images", "");
        params.add("contact", "18612345678");
        params.add("infoUuid", "7a22772c-c957-4865-aedb-d0854f784c4d");
        params.add("contextUserId", "27135");
        System.out.println(JSON.toJSONString(params));
    }

}
