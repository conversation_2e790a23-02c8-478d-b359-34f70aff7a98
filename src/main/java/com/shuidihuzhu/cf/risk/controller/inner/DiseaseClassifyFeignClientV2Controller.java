package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21
 */
@RestController
@Slf4j
public class DiseaseClassifyFeignClientV2Controller implements DiseaseClassifyFeignClientV2 {

    @Autowired
    private DiseaseRpcService diseaseRpcService;

    @Override
    public Response<List<DiseaseClassifyVOV2>> diseaseNorm(List<String> diseaseList) {
        if (log.isDebugEnabled()){
            log.debug("diseaseResourceList:{}", diseaseList);
        }
        return NewResponseUtil.makeSuccess(diseaseRpcService.diseaseNorm(diseaseList));
    }
}
