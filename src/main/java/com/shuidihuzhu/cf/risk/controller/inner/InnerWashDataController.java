package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.service.CfReportService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RefreshScope
@RequestMapping("/innerapi/cf-risk-admin/wash-data/")
public class InnerWashDataController {

    @Autowired
    private CfReportService cfReportService;

    @RequestMapping(path = "record",method = RequestMethod.POST)
    public Response<Void> addUserBlackList(@RequestParam("beginId") int beginId){
        cfReportService.washData(beginId);
        return NewResponseUtil.makeSuccess();
    }

    @RequestMapping(path = "record-null",method = RequestMethod.POST)
    public Response<Void> addUserBlackListNull(@RequestParam("beginId") int beginId){
        cfReportService.washDataV2(beginId);
        return NewResponseUtil.makeSuccess();
    }

}
