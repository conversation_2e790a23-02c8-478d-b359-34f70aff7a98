package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.rpc.DiscussionClient;
import com.shuidihuzhu.cf.risk.model.DiscussionDTO;
import com.shuidihuzhu.cf.risk.service.DiscussionService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class DiscussionAdminInnerController implements DiscussionClient {

    @Autowired
    private DiscussionService discussionService;

    @Override
    public Response<DiscussionDTO> getDiscussionInfo(int caseId) {
        return NewResponseUtil.makeSuccess(discussionService.findByCaseId(caseId));
    }
}
