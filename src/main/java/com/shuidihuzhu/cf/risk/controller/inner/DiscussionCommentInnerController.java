package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.rpc.DiscussionCommentClient;
import com.shuidihuzhu.cf.risk.model.CommentVO;
import com.shuidihuzhu.cf.risk.service.CfRiskCommentService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/10
 */
@RestController
@Slf4j
public class DiscussionCommentInnerController implements DiscussionCommentClient {

    @Autowired
    private CfRiskCommentService cfRiskCommentService;

    @Override
    public Response<List<CommentVO>> findById(@RequestParam("commentIdList") List<Long> commentIdList) {
        if (CollectionUtils.isEmpty(commentIdList)) {
            return NewResponseUtil.makeSuccess(null);
        }
        if (commentIdList.size() > 200) {
            return NewResponseUtil.makeSuccess(null);
        }
        return cfRiskCommentService.findByCommentIdsInner(commentIdList);
    }
}
