package com.shuidihuzhu.cf.risk.controller.inner;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseAmountCallLogBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.RiskRpcErrorCode;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import com.shuidihuzhu.cf.risk.service.DiseaseStrategyService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/16
 */
@RestController
@Slf4j
public class DiseaseController implements DiseaseClient {

    @Autowired
    private DiseaseRpcService diseaseRpcService;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private DiseaseStrategyService diseaseStrategyService;
    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;
    @Autowired
    private DiseaseAmountCallLogBiz diseaseAmountCallLogBiz;

    @Override
    public Response<InfoReasonableAmountResultVo> decideInfoAmountReasonable(@RequestBody DecideReasonableInfo decideReasonableInfo) {

        log.info("decideReasonableInfo:{}", JSON.toJSONString(decideReasonableInfo));
        if ( CollectionUtils.isEmpty(decideReasonableInfo.getDiseaseNameList())) {
            return NewResponseUtil.makeError(RiskRpcErrorCode.SYSTEM_PARAM_ERROR);
        }
        int targetAmount = 0;
        if (decideReasonableInfo.getCaseId() > 0){
            FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getCaseInfoById(decideReasonableInfo.getCaseId());
            if (response.notOk() || response.getData() == null) {
                log.info("response:{}", JSON.toJSONString(response));
                return  NewResponseUtil.makeError(RiskRpcErrorCode.INFO_NOT_EXIST);
            }
            targetAmount = response.getData().getTargetAmount();
        }
        //验证案例是否存在
        return diseaseRpcService.decideInfoAmountReasonable(targetAmount, decideReasonableInfo);
    }


    @Override
    public Response<SpecialDiseaseChoiceInfoVo> specialChoiceInfo(@RequestBody DecideReasonableInfo decideReasonableInfo) {
        log.info("specialChoiceInfo diseaseNameList:{}, specialRaiseInfo:{}", JSON.toJSONString(decideReasonableInfo.getDiseaseNameList()), decideReasonableInfo.getSpecialRaiseChoiceInfo());
        return diseaseRpcService.specialChoiceInfo(decideReasonableInfo.getDiseaseNameList(), decideReasonableInfo.getSpecialRaiseChoiceInfo());
    }

    @Override
    public Response<SpecialDiseaseChoiceInfoVo> specialRaiseChoiceInfo(List<String> diseaseNameList) {
        log.info("specialRaiseChoiceInfo diseaseNameList:{}", JSON.toJSONString(diseaseNameList));
        return diseaseRpcService.specialRaiseChoiceInfo(diseaseNameList);
    }

    @Override
    public Response<Set<String>> getAllDiseaseName() {
        return diseaseRpcService.getAllDiseaseName();
    }

    @Override
    public Response<Page<String>> findByDiseaseName(@RequestBody DiseaseNameQuery diseaseNameQuery) {
        return NewResponseUtil.makeSuccess(riskDiseaseDataRpcBiz.findByDiseaseName(diseaseNameQuery));
    }

    @Override
    public Response<DiseaseStrategyResponse> diseaseStrategy(DiseaseStrategyRequest diseaseStrategyRequest) {
        log.info("diseaseStrategy diseaseStrategyRequest:{}", JSON.toJSONString(diseaseStrategyRequest));
        if (diseaseStrategyRequest == null ){
            return NewResponseUtil.makeError(RiskRpcErrorCode.STRATEGY_REQUEST_ERROR);
        }
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList()) && diseaseStrategyRequest.getCaseId() <= 0){
            return NewResponseUtil.makeError(RiskRpcErrorCode.STRATEGY_REQUEST_ERROR);
        }
        if (DiseaseStrategyEnum.findByCode(diseaseStrategyRequest.getExecuteStrategyEnum()) == null){
            return NewResponseUtil.makeError(RiskRpcErrorCode.STRATEGY_REQUEST_ERROR);
        }
        return diseaseStrategyService.diseaseStrategy(diseaseStrategyRequest);
    }

    @Override
    public Response<List<DiseaseStrategyLog>> strategyResult(int caseId) {
        log.info("strategyResult caseId:{}", caseId);
        if (caseId <= 0){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return diseaseStrategyService.strategyResult(caseId);
    }

    @Override
    public Response<String> maxFeeOfDiseaseProjects(String diseases, int caseId) {
        log.info("sortDisease diseases:{} caseId:{}", diseases, caseId);
        if (StringUtils.isEmpty(diseases)){
            return NewResponseUtil.makeSuccess("");
        }
        return NewResponseUtil.makeSuccess(diseaseRpcService.maxFeeOfDiseaseProjectStr(diseases, caseId));
    }

    @Override
    public Response<List<DiseaseProjectVO>> maxFeeOfDiseaseProjectList(String diseases, int caseId) {
        log.info("sortDisease diseases:{} caseId:{}", diseases, caseId);
        if (StringUtils.isEmpty(diseases)){
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeSuccess(diseaseRpcService.maxFeeOfDiseaseProjectList(diseases, caseId));
    }

    @Override
    public Response<List<String>> findByDiseaseNameKeyWord(String diseaseNameKeyWord) {
        log.info("DiseaseController findByDiseaseName diseaseNameKeyWord:{}", diseaseNameKeyWord);
        if (StringUtils.isEmpty(diseaseNameKeyWord)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<String> byDiseaseName = riskDiseaseDataRpcBiz.findByDiseaseName(diseaseNameKeyWord);
        return NewResponseUtil.makeSuccess(byDiseaseName);
    }

    @Override
    public Response<List<RiskDiseaseDataVO>> getRiskDiseaseDataByClassNameList(List<String> diseaseNameList) {
        List<RiskDiseaseData> byClassNameList = riskDiseaseDataRpcBiz.getByClassNameList(diseaseNameList);
        List<RiskDiseaseDataVO> diseaseDataVOList = byClassNameList.stream()
                .map(m -> {
                    RiskDiseaseDataVO riskDiseaseDataVO = new RiskDiseaseDataVO();
                    BeanUtils.copyProperties(m, riskDiseaseDataVO);
                    return riskDiseaseDataVO;
                })
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(diseaseDataVOList);
    }

    @Override
    public Response<List<DiseaseAmountResultRecord>> getAmountResultRecordByCaseId(int caseId) {
        return NewResponseUtil.makeSuccess(diseaseAmountCallLogBiz.getRecordWithoutToolByCaseId(caseId));
    }

    @Override
    public Response<List<DiseaseAmountResultRecord>> getToolRecordByCaseId(int caseId) {
        return NewResponseUtil.makeSuccess(diseaseAmountCallLogBiz.getToolRecordByCaseId(caseId));
    }

    @Override
    public Response<Void> saveToolRecord(DiseaseAmountResultRecord diseaseAmountResultRecord) {
        diseaseAmountCallLogBiz.saveToolRecord(diseaseAmountResultRecord);
        return NewResponseUtil.makeSuccess();
    }

}
