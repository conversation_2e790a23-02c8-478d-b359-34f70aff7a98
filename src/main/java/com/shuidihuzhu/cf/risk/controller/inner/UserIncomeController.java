package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.rpc.UserIncomeClient;
import com.shuidihuzhu.cf.risk.model.enums.RiskRpcErrorCode;
import com.shuidihuzhu.cf.risk.service.UserIncomeService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/3
 */
@RestController
@Slf4j
public class UserIncomeController implements UserIncomeClient {

    @Autowired
    private UserIncomeService userIncomeService;

    @Override
    public Response<Map<String, Map<String, String>>> getThirdInfo(String idCard, String mobile, String name, String modelType) {
        log.info("getPortrayalData idCard:{}, mobile:{}, name:{}, modelType:{}", idCard, mobile, name, modelType);
        if(StringUtils.isBlank(idCard) || StringUtils.isBlank(mobile) || StringUtils.isBlank(name)) {
            return NewResponseUtil.makeError(RiskRpcErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Map<String, String>> resultMap = this.userIncomeService.getThirdData(idCard, mobile, name, modelType);
        log.info("getPortrayalData resultMap:{}", resultMap);
        return NewResponseUtil.makeSuccess(resultMap);
    }

    @Override
    public Response<Map<String, String>> getBairongData(String idCard, String mobile, String name) {
        Map<String, String> resultMap = this.userIncomeService.getBairongData(idCard, mobile, name);
        log.info("getPortrayalData resultMap:{}", resultMap);
        return NewResponseUtil.makeSuccess(resultMap);
    }

    @Override
    public Response<Map<String, String>> getShangyongData(String idCard, String mobile, String name, String modelType) {
        Map<String, String> resultMap = this.userIncomeService.getShangyongData(idCard, mobile, name, modelType);
        log.info("getPortrayalData resultMap:{}", resultMap);
        return NewResponseUtil.makeSuccess(resultMap);
    }

}
