package com.shuidihuzhu.cf.risk.controller.inner;

import com.shuidihuzhu.cf.risk.client.risk.BrokenPromisesClientV2;
import com.shuidihuzhu.cf.risk.service.BrokenPromisesService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/8/3 7:21 下午
 */
@Slf4j
@RestController
public class BrokenPromisesController implements BrokenPromisesClientV2 {

    @Resource
    private BrokenPromisesService brokenPromisesService;

    @Override
    public Response<Boolean> checkInfo(String name, String idCard) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(idCard)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeFail("身份证号码不正确");
        }
        log.info("BrokenPromisesController checkInfo name:{},idCard:{}", name, idCard);
        return brokenPromisesService.isBrokenPromises(idCard, name);
    }
}
