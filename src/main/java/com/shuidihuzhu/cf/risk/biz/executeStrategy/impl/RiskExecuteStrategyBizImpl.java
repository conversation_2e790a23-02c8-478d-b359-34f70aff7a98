package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl;

import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyBiz;
import com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyDao;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Service
public class RiskExecuteStrategyBizImpl implements RiskExecuteStrategyBiz {

    @Autowired
    private RiskExecuteStrategyDao riskExecuteStrategyDao;

    @Override
    public RiskExecuteStrategy getByType(int type) {
        if (type <= 0){
            return null;
        }
        return riskExecuteStrategyDao.getByType(type);
    }
}
