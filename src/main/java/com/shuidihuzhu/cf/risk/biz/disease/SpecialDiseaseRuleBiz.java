package com.shuidihuzhu.cf.risk.biz.disease;

import com.shuidihuzhu.cf.risk.app.configuration.RiskDS;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseRuleTypeEnum;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
public interface SpecialDiseaseRuleBiz {

    SpecialDiseaseRule getByClassName( String diseaseClassName);

    SpecialDiseaseRule getByClassNameCache(String diseaseClassName);

    List<SpecialDiseaseRule> findByClassNameList(List<String> diseaseClassNameList);

    List<String> findAllSpecialClassNameCache();

    List<SpecialDiseaseRule> findCacheByClassNameList(String key, List<String> diseaseClassNameList);

    List<SpecialDiseaseRule> findCacheByClassNameList(String key,
                                                      List<String> diseaseClassNameList,
                                                      SpecialDiseaseRuleTypeEnum typeEnum);

    List<SpecialDiseaseRule> findByClassNameListAndType(List<String> diseaseClassNameList, SpecialDiseaseRuleTypeEnum type);
}
