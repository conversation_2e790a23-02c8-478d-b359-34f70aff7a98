package com.shuidihuzhu.cf.risk.biz.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeV2Service;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeService;
import com.shuidihuzhu.cf.risk.biz.PreposeMaterialRiskBiz;
import com.shuidihuzhu.cf.risk.model.risk.CfPreposeMaterialRiskVo;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 适配逻辑
 *
 * <AUTHOR>
 */
@Service
public class HighRiskJudgeServiceImpl implements HighRiskJudgeService {

    @Autowired
    private PreposeMaterialRiskBiz preposeMaterialRiskBiz;

    @Autowired
    private HighRiskJudgeV2Service highRiskJudgeV2Service;

    @Override
    public CfPreposeMaterialRiskVo judgeCase(CfPropertyInsuranceInfoModel insuranceInfo) {
        return preposeMaterialRiskBiz.markPropertyRisk(insuranceInfo);
//        return judgeCaseV2(insuranceInfo);
    }

    @Override
    public CfPreposeMaterialRiskVo judgeDaiLuRu(PreposeMaterialModel.MaterialInfoVo info) {
        return preposeMaterialRiskBiz.markRisk(info);
//        return judgeDaiLuRuV2(info);
    }
//
//    private CfPreposeMaterialRiskVo judgeCaseV2(CfPropertyInsuranceInfoModel insuranceInfo) {
//        HighRiskJudgeInfoModel infoModel = cCase2Info(insuranceInfo);
//        Response<HighRiskJudgeResult> result = highRiskJudgeV2Service.judge(infoModel);
//        return result2V1Result(result);
//    }
//
//
//    private CfPreposeMaterialRiskVo judgeDaiLuRuV2(PreposeMaterialModel.MaterialInfoVo info) {
//        HighRiskJudgeInfoModel infoModel = cDaiLuRu2Info(info);
//        Response<HighRiskJudgeResult> result = highRiskJudgeV2Service.judge(infoModel);
//        return result2V1Result(result);
//    }
//
//    private HighRiskJudgeInfoModel cDaiLuRu2Info(PreposeMaterialModel.MaterialInfoVo info) {
//        HighRiskJudgeInfoModel v = new HighRiskJudgeInfoModel();
//        return v;
//    }
//
//    private HighRiskJudgeInfoModel cCase2Info(CfPropertyInsuranceInfoModel insuranceInfo) {
//        HighRiskJudgeInfoModel v = new HighRiskJudgeInfoModel();
//        return v;
//    }
//
//    private CfPreposeMaterialRiskVo result2V1Result(Response<HighRiskJudgeResult> result) {
//        CfPreposeMaterialRiskVo v = new CfPreposeMaterialRiskVo();
//        return v;
//    }

    private boolean isV1Enable() {
        Config config = ConfigService.getConfig("sdc.inner");
        return config.getBooleanProperty("apollo.common.high-risk.v1-enable", false);
    }

}
