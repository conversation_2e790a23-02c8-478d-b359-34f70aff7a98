package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 疾病高风险标记
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
public class DiseaseHighRiskModel extends ExecuteStrategyModelTemplate {

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noRiskResult = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.NO_AUDIT);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> riskResult = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.NO_AUDIT_RISK);
        }
    };


    /**
     * 非风险病种案例目标金额判断标准
     */
    private static int noRiskTargetAmount = 50000000;

    /**
     * 高风险病种案例目标金额判断标准
     */
    private static int riskTargetAmount = 10000000;

    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;

    @Override
    public String getCode() {
        return "disease_judge_high_risk";
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        List<RiskDiseaseData>  riskDiseaseDataList =
                riskDiseaseDataRpcBiz.getByClassNameList(transit.getMergeResult());
        if (CollectionUtils.isEmpty(riskDiseaseDataList)) {
            return;
        }
        List<RiskDiseaseData> highRiskDiseaseDataList = riskDiseaseDataList.stream()
                .filter(RiskDiseaseData::isHighRisk).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(highRiskDiseaseDataList)) {
            //不包含高风险疾病  判断目标金额 是否大于50w
            //目标小于50w则无需审核目标金额
            if (transit.getTargetAmount() >= noRiskTargetAmount) {
               return;
            }
            diseaseStrategyResponse.setDiseaseAmountStrategyResult(
                    resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), noRiskResult)));
            transit.setHaNext(false);
            return;
        }
        if (transit.getTargetAmount() >= riskTargetAmount) {
            return;
        }
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(
                resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), riskResult)));
        transit.setHaNext(false);
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }




    @Override
    public boolean preDecide() {
        return true;
    }
}
