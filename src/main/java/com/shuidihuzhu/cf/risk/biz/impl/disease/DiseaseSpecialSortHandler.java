package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/11
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialSortHandler implements DiseaseSpecialHandler {

    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;


    @Value("${disease.special.sort-class-name.class:肿瘤}")
    private String sortNameClassName;

    private List<String> specialSortClassNameList;

    private Map<String, List<String>> specialSortClassNameMap;


    @Value("${disease.special.sort-class-name.detail:肝硬化}")
    public void setSpecialSortClassNameMap(String specialSortClassName) {
        log.info("specialSortClassName:{}", specialSortClassName);
        if (StringUtils.isBlank(specialSortClassName)) {
            return;
        }
        Map<String, SpecialDiseaseRule>  specialDiseaseRuleMap =  findSpecialDiseaseRuleMap(sortNameClassName);
        this.specialSortClassNameMap = dealClassNameList(Lists.newArrayList(Splitter.on(",").splitToList(specialSortClassName)), specialDiseaseRuleMap);
        this.specialSortClassNameList = Lists.newArrayList(specialSortClassNameMap.keySet());
    }

    private Map<String, SpecialDiseaseRule> findSpecialDiseaseRuleMap(String sortNameClassName) {
        List<SpecialDiseaseRule> specialDiseaseRuleList = specialDiseaseRuleBiz.findByClassNameList(Splitter.on(",").splitToList(sortNameClassName));
        if (CollectionUtils.isEmpty(specialDiseaseRuleList)) {
            return Maps.newHashMap();
        }
        return specialDiseaseRuleList.stream().collect(Collectors.toMap(SpecialDiseaseRule::getDiseaseClassName, Function.identity()));
    }

    /**
     * 对肿瘤进行特殊处理
     * @param classNameList 特殊规则类名
     * @return 真正需要提前的特殊规则名
     */
    private Map<String, List<String>> dealClassNameList(List<String> classNameList,
                                                        Map<String, SpecialDiseaseRule> specialDiseaseRuleMap) {
        Map<String, List<String>> specialSortResult = Maps.newHashMap();
        classNameList.addAll(specialDiseaseRuleMap.keySet());
        for (String className : classNameList ) {
            SpecialDiseaseRule specialDiseaseRule = specialDiseaseRuleMap.get(className);
            if (specialDiseaseRule == null) {
                specialSortResult.put(className, Lists.newArrayList(className));
                continue;
            }
            specialSortResult.put(className, specialDiseaseRule.getDiseaseContainClassNameList());
        }
        return specialSortResult;
    }

    /**
     * 肝硬化和除肝癌之外的任何疾病同时出现时，肝硬化排第一，然后再取其它大病的归一结果，进行未来花费金额的计算
     */
    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit, List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        for (String s : specialSortClassNameList) {
            List<String> filterSortClassNameList = diseaseNameList.stream().filter(specialSortClassNameMap.get(s)::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSortClassNameList)){
                continue;
            }
            diseaseNameList.removeAll(filterSortClassNameList);
            //将对应的顺序提前
            for (String diseaseName : filterSortClassNameList) {
                diseaseNameList.add(0, diseaseName);
            }
        }
        return Lists.newArrayList();
    }

    public static void main(String[] args) {
        System.out.println(StringUtils.replace("肺癌、贲门癌、鼻咽癌、鼻窦癌、鼻腔癌、黑色素瘤、浆细胞瘤、嗅神经恶性肿瘤、扁桃体癌、肠癌、胃肠道间质瘤、胃肠道间皮瘤、恶性胃泌素瘤、胃癌、肝癌、肝母细胞瘤、食管癌、甲状腺癌、肾癌、卵巢癌、膀胱癌、胰腺癌、外阴癌、皮肤癌、纵隔恶性肿瘤、淋巴结转移癌、乳腺癌、口腔癌、颌窦癌、阴囊癌、睾丸癌、前列腺癌、宫颈癌、子宫癌、恶性肿瘤晚期、肉瘤、纤维肉瘤、骨瘤、喉癌、恶性肿瘤放化疗、神经母细胞瘤、精原细胞瘤、恶性胸膜间皮瘤、甲状腺乳头状瘤、恶性卵黄囊瘤、鳞状细胞癌、恶性畸胎瘤、绒毛膜癌、滋养细胞肿瘤","、",","));
    }
}
