package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseBurnEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseSpecialInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/10
 * 烧伤金额计算
 */
@Service
@Slf4j
public class DiseaseSpecialBurnHandler implements DiseaseSpecialHandler {




    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit, List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        if (!diseaseNameList.contains(DiseaseCommonConfig.BURN_NAME)) {
            return Lists.newArrayList();
        }
        //兼容为空的情况
        List<DiseaseSpecialInfoVo> diseaseSpecialInfoVos = fullInfoVos(specialChoiceInfoVos);
        double burnAmount = 0;
        for (DiseaseSpecialInfoVo specialInfoVo : diseaseSpecialInfoVos) {
            burnAmount += getBurnAmount(specialInfoVo);
        }
        //如果全部没写面积则烧伤金额为5
        if (burnAmount == 0) {
            burnAmount = 5;
        }
        addResultDiseaseName(Lists.newArrayList(DiseaseCommonConfig.BURN_NAME), resultVo, true);
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + burnAmount);
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + burnAmount);
        replaceDiseaseName(diseaseNameList, Lists.newArrayList(DiseaseCommonConfig.BURN_NAME), false, false);
        setBurnOtherInfo(resultVo, diseaseSpecialInfoVos);
        return Lists.newArrayList();
    }

    private List<DiseaseSpecialInfoVo> fullInfoVos(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfo) {
        if (CollectionUtils.isEmpty(specialChoiceInfo)) {
            return Lists.newArrayList();
        }
        //获取烧伤的特殊信息
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos =
                filterSpecialChoice(specialChoiceInfo, List.of(DiseaseCommonConfig.BURN_NAME));
        //移除已经处理的特殊值
        specialChoiceInfo.removeAll(specialDiseaseChoiceInfos);
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return Lists.newArrayList();
        }
        List<DiseaseSpecialInfoVo> diseaseSpecialInfoVos = Lists.newArrayList();
        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : specialDiseaseChoiceInfos) {
            List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList = specialDiseaseChoiceInfo.getChoiceInfoList();
            if (CollectionUtils.isEmpty(choiceInfoList)) {
                return Lists.newArrayList();
            }
            for (SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo : choiceInfoList) {
                DiseaseTreamentChoiceTypeEnum choiceType = DiseaseTreamentChoiceTypeEnum.findByCode(choiceInfo.getChoiceType());
                if (choiceType == null || choiceType == DiseaseTreamentChoiceTypeEnum.DEFAULT) {
                    return null;
                }
                DiseaseSpecialInfoVo diseaseSpecialInfoVo = new DiseaseSpecialInfoVo();
                diseaseSpecialInfoVo.setSpecialDiseaseName(specialDiseaseChoiceInfo.getSpecialDiseaseName());
                diseaseSpecialInfoVo.setBurnArea(Integer.parseInt(StringUtils.isBlank(choiceInfo.getInputValue()) ? "0" : choiceInfo.getInputValue()));
                diseaseSpecialInfoVo.setBurnLevel(SpecialDiseaseBurnEnum.findByDesc(choiceInfo.getChoiceName()).getCode());
                diseaseSpecialInfoVos.add(diseaseSpecialInfoVo);
            }
        }
        return diseaseSpecialInfoVos;
    }

    private void setBurnOtherInfo(InfoReasonableAmountResultVo resultVo, List<DiseaseSpecialInfoVo> diseaseSpecialInfoVos) {
        if (CollectionUtils.isEmpty(diseaseSpecialInfoVos)){
            return;
        }
        StringBuilder builder = new StringBuilder().append("其他信息：").append("<br>");
        builder.append("疾病名称：").append(DiseaseCommonConfig.BURN_NAME).append("<br>");
        for (DiseaseSpecialInfoVo infoVo : diseaseSpecialInfoVos) {
            builder.append("烧伤等级：").append(SpecialDiseaseBurnEnum.findByCode(infoVo.getBurnLevel()).getDesc())
                    .append("   烧伤面积：").append(infoVo.getBurnArea()).append("<br>");
        }
        resultVo.setOtherInfo(builder.toString());
    }

    private double getBurnAmount(DiseaseSpecialInfoVo diseaseSpecialInfoVo) {
        if(diseaseSpecialInfoVo.getBurnArea() <=0 || diseaseSpecialInfoVo.getBurnArea() > 100) {
            return 0;
        }
        switch (diseaseSpecialInfoVo.getBurnLevel()) {
            case 1:
                return  diseaseSpecialInfoVo.getBurnArea() * 0.5;
            case 2:
                return  diseaseSpecialInfoVo.getBurnArea();
            default:
                //如果不写度数和面积 定值为5万
                return 0;
        }
    }

}
