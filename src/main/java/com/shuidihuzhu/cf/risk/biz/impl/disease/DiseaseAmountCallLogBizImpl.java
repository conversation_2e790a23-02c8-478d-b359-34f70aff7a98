package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseAmountCallLogBiz;
import com.shuidihuzhu.cf.risk.dao.DiseaseAmountCallLogDao;
import com.shuidihuzhu.cf.risk.dao.DiseaseAmountResultRecordDao;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseAmountCallLog;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/9/28
 */
@Service
@Slf4j
public class DiseaseAmountCallLogBizImpl implements DiseaseAmountCallLogBiz {

    @Resource
    private DiseaseAmountCallLogDao diseaseAmountCallLogDao;
    @Resource
    private DiseaseAmountResultRecordDao diseaseAmountResultRecordDao;

    @Override
    @Async
    public void saveLog(InfoReasonableAmountResultVo reasonableAmountResultVo,
                        DecideReasonableInfo decideReasonableInfo,
                        int targetAmount) {
        if (reasonableAmountResultVo == null || decideReasonableInfo == null
                || CollectionUtils.isEmpty(decideReasonableInfo.getDiseaseNameList())) {
            log.error("reasonableAmountResultVo:{} decideReasonableInfo:{}", JSON.toJSONString(reasonableAmountResultVo),
                    JSON.toJSONString(decideReasonableInfo));
            return;
        }
        DiseaseAmountCallLog diseaseAmountCallLog =  new DiseaseAmountCallLog();
        diseaseAmountCallLog.setCaseId(decideReasonableInfo.getCaseId());
        diseaseAmountCallLog.setTargetAmountInFen(targetAmount);
        diseaseAmountCallLog.setRequestDisease(String.join(",", decideReasonableInfo.getDiseaseNameList()));
        if (reasonableAmountResultVo.getCanRaiseStatus() != null){
            diseaseAmountCallLog.setCanRaiseStatus(reasonableAmountResultVo.getCanRaiseStatus());
        }
        diseaseAmountCallLog.setAdviseMinAmount((int) (reasonableAmountResultVo.getAdviseMinAmount() * 10000));
        diseaseAmountCallLog.setAdviseMaxAmount((int) (reasonableAmountResultVo.getAdviseMaxAmount() * 10000));
        diseaseAmountCallLog.setCalculateDiseaseName(reasonableAmountResultVo.getCalculateDiseaseNames());
        diseaseAmountCallLog.setTreatmentInfo(reasonableAmountResultVo.getTreatmentInfo());
        diseaseAmountCallLog.setChannel(StringUtils.trimToEmpty(decideReasonableInfo.getRequestChannel()));
        diseaseAmountCallLogDao.save(diseaseAmountCallLog);
    }

    @Override
    public void insertAmountRecord(DiseaseStrategyRequest diseaseStrategyRequest,
                                   RiskExecuteStrategyTransit transit,
                                   InfoReasonableAmountResultVo reasonableAmountResultVo,
                                   DecideReasonableInfo decideReasonableInfo,
                                   DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum amountReasonableEnum,
                                   int targetAmount,
                                   int version) {
        if (reasonableAmountResultVo == null || decideReasonableInfo == null
                || CollectionUtils.isEmpty(decideReasonableInfo.getDiseaseNameList())) {
            log.error("reasonableAmountResultVo:{} decideReasonableInfo:{}", JSON.toJSONString(reasonableAmountResultVo),
                    JSON.toJSONString(decideReasonableInfo));
            return;
        }
        DiseaseAmountResultRecord diseaseAmountResultRecord = new DiseaseAmountResultRecord();
        diseaseAmountResultRecord.setCaseId(diseaseStrategyRequest.getCaseId());
        diseaseAmountResultRecord.setWorkOrderId(diseaseStrategyRequest.getWorkOrderId());
        diseaseAmountResultRecord.setWorkOrderTypeCode(0);
        diseaseAmountResultRecord.setPrePoseId(transit.getPrePoseId());
        diseaseAmountResultRecord.setReasonAmountType(diseaseStrategyRequest.getReasonAmountType());
        diseaseAmountResultRecord.setTargetAmountInFen(targetAmount);
        diseaseAmountResultRecord.setAmountReasonable(Objects.equals(amountReasonableEnum, DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.AMOUNT_REASONABLE));
        diseaseAmountResultRecord.setAmountReasonableType(amountReasonableEnum.getCode());
        diseaseAmountResultRecord.setDiseaseName(String.join(",", diseaseStrategyRequest.getDiseaseNameList()));
        diseaseAmountResultRecord.setDiseaseNormName(String.join(",", transit.getMergeResult()));
        diseaseAmountResultRecord.setAdviseMinAmount((int) (reasonableAmountResultVo.getAdviseMinAmount() * 10000));
        diseaseAmountResultRecord.setAdviseMaxAmount((int) (reasonableAmountResultVo.getAdviseMaxAmount() * 10000));
        diseaseAmountResultRecord.setCalculateDiseaseName(reasonableAmountResultVo.getCalculateDiseaseNames());
        diseaseAmountResultRecord.setTreatmentInfo(reasonableAmountResultVo.getTreatmentInfo());
        diseaseAmountResultRecord.setOperatorId(diseaseStrategyRequest.getUserId());
        diseaseAmountResultRecord.setVersion(version);
        diseaseAmountResultRecord.setOperator("");
        diseaseAmountResultRecordDao.insert(diseaseAmountResultRecord);
    }

    @Override
    public void insertAmountRecord(DiseaseStrategyRequest diseaseStrategyRequest,
                                   RiskExecuteStrategyTransit transit,
                                   InfoReasonableAmountResultVo reasonableAmountResultVo,
                                   DecideReasonableInfo decideReasonableInfo,
                                   DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum amountReasonableEnum,
                                   int targetAmount) {
        insertAmountRecord(diseaseStrategyRequest, transit, reasonableAmountResultVo, decideReasonableInfo,
                amountReasonableEnum, targetAmount, 0);
    }

    @Override
    public List<DiseaseAmountResultRecord> getRecordWithoutToolByCaseId(int caseId) {
        List<Integer> reasonTypeList = Lists.newArrayList();
        reasonTypeList.add(DiseaseAmountReasonableTypeEnum.INITIAL_AUDIT_PASS.getCode());
        reasonTypeList.add(DiseaseAmountReasonableTypeEnum.SMART_INITIAL_AUDIT.getCode());
        reasonTypeList.add(DiseaseAmountReasonableTypeEnum.INITIAL_DETAIL.getCode());
        reasonTypeList.add(DiseaseAmountReasonableTypeEnum.HIGH_RIS_INITIAL_DETAIL.getCode());
        reasonTypeList.add(DiseaseAmountReasonableTypeEnum.TARGET_AMOUNT_REASONABLE.getCode());
        return diseaseAmountResultRecordDao.getRecordByCaseIdAndReasonTypeList(caseId, reasonTypeList);
    }

    @Override
    public List<DiseaseAmountResultRecord> getToolRecordByCaseId(int caseId) {
        return diseaseAmountResultRecordDao.getRecordByCaseIdAndReasonTypeList(caseId,
                Lists.newArrayList(DiseaseAmountReasonableTypeEnum.DISEASE_CALCULATE_TOOL.getCode()));

    }

    @Override
    public void saveToolRecord(DiseaseAmountResultRecord diseaseAmountResultRecord) {
        if(diseaseAmountResultRecord == null){
            log.info("diseaseAmountResultRecord is null");
            return;
        }
        diseaseAmountResultRecord.setPrePoseId(0);
        diseaseAmountResultRecord.setAmountReasonable(false);
        diseaseAmountResultRecord.setAmountReasonableType(0);
        diseaseAmountResultRecord.setVersion(0);
        diseaseAmountResultRecordDao.insert(diseaseAmountResultRecord);
    }

}
