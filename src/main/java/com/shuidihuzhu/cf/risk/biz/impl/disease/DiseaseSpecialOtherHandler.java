package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseRuleTypeEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.PfLocalMapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 替换类型的计算 (A, B) -> A
 *
 * <AUTHOR>
 * @date 2020/4/10
 *
 */
@Service
@RefreshScope
public class DiseaseSpecialOtherHandler implements DiseaseSpecialHandler {


    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;

    private static final String OTHER_KEY = "disease_special_other_rule";

    private static List<String> topClassNameList = Lists.newArrayList("重型肝炎");


    private List<String> otherClassNameList;

    @Value("${disease.special.other-class-name:血管炎,肝癌,重症感染,重症肺炎,急重症胰腺炎,脑膜瘤,脑膜炎,颅内占位,急性脑梗死,重型颅脑损伤," +
            "肝癌,早产儿,动脉导管未闭,卵圆孔未闭,肺动脉瓣狭窄,房间隔缺损,室间隔缺损,法洛氏四联症,右室双出口,主动脉缩窄,完全型肺静脉异位引流," +
            "部分型肺静脉异位引流,完全性大动脉转位,完全型（部分型）心内膜垫缺损,二尖瓣疾病,三尖瓣疾病,主动脉瓣疾病,肺动脉瓣疾病,机械瓣功能障碍," +
            "肝硬化,婴儿痉挛症,重型肝炎,主动脉夹层,农药中毒,早产儿,重症心肌炎,烧伤,重度蜂蜇伤,重症破伤风,重型再生障碍性贫血,重度地中海贫血,脑动脉瘤")
    public void setOtherClassNameList(String otherClassName) {
        this.otherClassNameList = Splitter.on(",").splitToList(otherClassName);
    }

    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //首先判断疾病中是是否包含特殊疾病
        //1. 获取前三个疾病种类
        //List<String> topThreeDiseaseName = getTopLimit(Lists.newArrayList(diseaseNameList), diseaseJudgeLimit);
        //2. 获取计算的病例
        List<String> filterNameList = filterSpecialDisease(diseaseNameList, Lists.newArrayList(otherClassNameList));
        if (CollectionUtils.isEmpty(filterNameList)){
            return Lists.newArrayList();
        }
        //保存被替换掉的病
        //List<String> specialDiseaseFilterNames = Lists.newArrayList();
        List<SpecialDiseaseRule> specialDiseaseRules = getSpecialDiseaseRuleList(filterNameList);
        for (SpecialDiseaseRule specialDiseaseRule : specialDiseaseRules) {
            //移除需要替换的病
            diseaseNameList.removeAll(specialDiseaseRule.getDiseaseContainClassNameList());
            //specialDiseaseFilterNames.add(specialDiseaseRule.getDiseaseClassName());
            //置顶的特殊病例此处不处理
            if (!topClassNameList.contains(specialDiseaseRule.getDiseaseClassName())) {
                continue;
            }
            //特殊病例特殊处理  置顶
            List<String> needTops  = diseaseNameList.stream().filter( v ->
                    specialDiseaseRule.getDiseaseContainClassNameList().contains(v)).collect(Collectors.toList());
            //先取主页面
            int mainIndex = diseaseNameList.indexOf(specialDiseaseRule.getDiseaseClassName());
            int minIndex = mainIndex;
            for (String noTop : needTops) {
                int noTopIndex = diseaseNameList.indexOf(noTop);
                minIndex = Math.min(minIndex, noTopIndex);
            }
            if (minIndex == mainIndex){
                continue;
            }
            diseaseNameList.set(minIndex, specialDiseaseRule.getDiseaseClassName());
        }
        //addResultDiseaseName(specialDiseaseFilterNames, resultVo, false);
        //删除多余的
        return Lists.newArrayList();
    }


    /**
     *
     * @param filterNameList        需要过滤的规则
     * @return
     */
    private List<SpecialDiseaseRule> getSpecialDiseaseRuleList(List<String> filterNameList) {
        List<SpecialDiseaseRule> specialDiseaseRules =
                specialDiseaseRuleBiz.findCacheByClassNameList(OTHER_KEY, otherClassNameList, SpecialDiseaseRuleTypeEnum.OTHER);
        return specialDiseaseRules.stream().filter(v -> filterNameList.contains(v.getDiseaseClassName())).collect(Collectors.toList());
    }


    private List<String> getSpecialDiseaseRule(List<String> filterNameList) {
        List<String> fromDBList = Lists.newArrayList();
        List<String> returnNameList = Lists.newArrayList();
        for (String name : filterNameList) {
            List<String> containClassNameList = PfLocalMapUtil.get(getKey(name));
            if (CollectionUtils.isEmpty(containClassNameList)) {
                fromDBList.add(name);
                continue;
            }
            returnNameList.addAll(containClassNameList);
        }
        returnNameList.addAll(getFromDBList(fromDBList));
        return returnNameList;
    }


    private List<String> getFromDBList(List<String> classNameList) {
        List<SpecialDiseaseRule> specialDiseaseRuleList =
                specialDiseaseRuleBiz.findByClassNameList(classNameList);
        List<String> diseaseContainList = Lists.newArrayList();
        for (SpecialDiseaseRule rule : specialDiseaseRuleList) {
            if (CollectionUtils.isEmpty(rule.getDiseaseContainClassNameList())) {
                continue;
            }
            PfLocalMapUtil.put(getKey(rule.getDiseaseClassName()), rule.getDiseaseContainClassNameList(),
                    RedissonHandler.ONE_HOUR);
            diseaseContainList.addAll(rule.getDiseaseContainClassNameList());
        }
        return diseaseContainList;
    }
}
