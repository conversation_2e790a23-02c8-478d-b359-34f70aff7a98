package com.shuidihuzhu.cf.risk.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.apipure.feign.CaseCityFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.client.base.result.IOperationResult;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.HighRiskJudgeV2Service;
import com.shuidihuzhu.cf.risk.dao.highrisk.HighRiskCityLevelDao;
import com.shuidihuzhu.cf.risk.dao.highrisk.HighRiskRecordDao;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskAutoEnum;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskManualEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.*;
import com.shuidihuzhu.cf.risk.model.risk.HouseThresholdParam;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoPrePoseModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.client.baseservice.pay.model.pingan.PaBankRecord;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=894890131
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class HighRiskJudgeV2ServiceImpl implements HighRiskJudgeV2Service {

    @Resource
    private CaseCityFeignClient caseCityFeignClient;

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Resource
    private HighRiskRecordDao highRiskRecordDao;

    @Resource
    private HighRiskV2Strategy highRiskV2Strategy;

    @Resource
    private HighRiskV3Strategy highRiskV3Strategy;

    @Resource
    private CfMaterialReadClient cfMaterialReadClient;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Data
    public static class Config {

        private List<RiskRuleResult> ruleResultList;

        private Map<String, CityThreshold> levelThresholdMap;

    }

    /**
     * 参数检查
     * 阈值补充
     * 规则判断
     * 结果收集
     * 记录日志
     */
    @Override
    public Response<HighRiskJudgeResult> judge(HighRiskJudgeInfoModel infoModel) {

        log.debug("judge v2 caseId {} {}", infoModel.getCaseId(), infoModel);

        // 参数检查
        Integer caseId = infoModel.getCaseId();
        if (caseId == null || caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Integer source = infoModel.getSource();
        if (source == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        final JudgeContext judgeContext = new JudgeContext();
        judgeContext.setVersion(infoModel.getAuthenticityIndicator() == null ? 0 : 3);

        // 若非手动调用 已有调用记录直接返回上次结果
        Long workOrderId = infoModel.getWorkOrderId();
        if (workOrderId != null && workOrderId > 0 && source != HighRiskJudgeConst.Source.MANUAL) {
            HighRiskRecordDO lastByCondition = highRiskRecordDao.getLastByCondition(caseId, workOrderId, source);
            if (lastByCondition != null) {
                return NewResponseUtil.makeSuccess(promoteResult(lastByCondition));
            }
        }

        // 参数补充
        // 填充目标金额
        FeignResponse<CrowdfundingInfo> caseInfoResp = crowdfundingFeignClient.getCaseInfoById(caseId);
        if (caseInfoResp.notOk()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        CrowdfundingInfo fundingInfo = caseInfoResp.getData();
        judgeContext.setContent(fundingInfo.getContent());
        judgeContext.setContentForHighRisk(fundingInfo.getContent());
        judgeContext.setTargetAmountInYuan(fundingInfo.getTargetAmount() / 100);

        // 初审工单驳回后再提交，用新的暂存的文章内容
        Response<CaseInfoApproveStageDO> stageInfo = caseInfoApproveStageFeignClient.getStageInfo(caseId);
        if(stageInfo.ok() && stageInfo.getData() != null && StringUtils.isNotBlank(stageInfo.getData().getContent())){
            judgeContext.setContentForHighRisk(stageInfo.getData().getContent());
        }

        // 阈值补充
        IOperationResult<CaseCityInfoVO> caseCityInfoResp = caseCityFeignClient.getCityInfoByCaseId(caseId);
        if (caseCityInfoResp.isFail()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        CaseCityInfoVO cityInfoVO = caseCityInfoResp.getData();

        // 规则判断
        // 结果收集
        judgeContext.setRequestParam(infoModel);
        judgeContext.setCityInfoVO(cityInfoVO);
        HighRiskJudgeResult result = exec(judgeContext);

        // 记录日志
        HighRiskRecordDO record = buildRecord(judgeContext, result);
        highRiskRecordDao.insert(record);

        return NewResponseUtil.makeSuccess(result);
    }

    private HighRiskJudgeResult promoteResult(HighRiskRecordDO record) {
        final int version = record.getVersion();
        if (version == 0) {
            return highRiskV2Strategy.promoteResult(record.getHitCodes());
        }
        return highRiskV3Strategy.promoteResult(record);
    }

    private HighRiskJudgeResult exec(JudgeContext judgeContext) {
        final int version = judgeContext.getVersion();
        if (version == 0) {
            return highRiskV2Strategy.exec(judgeContext);
        }
        return highRiskV3Strategy.exec(judgeContext);
    }

    @Override
    public Response<HighRiskJudgeResult> judgePrePose(HighRiskJudgeInfoPrePoseModel infoModel) {
        log.info("judgePrePose caseId {} {}", infoModel.getCaseId(), JSON.toJSONString(infoModel));
        final JudgeContext judgeContext = new JudgeContext();
        judgeContext.setRequestParam(infoModel);
        judgeContext.setVersion(infoModel.getAuthenticityIndicator() == null ? 0 : 3);
        CaseCityInfoVO cityInfoVO = new CaseCityInfoVO();
        cityInfoVO.setBdCityCode(infoModel.getBdCityCode());
        cityInfoVO.setPatientCityCode(infoModel.getPatientCityCode());
        cityInfoVO.setRaiserCityCode(infoModel.getRaiserCityCode());
        judgeContext.setCityInfoVO(cityInfoVO);
        judgeContext.setContent(infoModel.getContent());
        judgeContext.setContentForHighRisk(infoModel.getContent());
        judgeContext.setTargetAmountInYuan(infoModel.getTargetAmountInYuan());
        // 规则判断
        // 结果收集
        HighRiskJudgeResult result = exec(judgeContext);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Integer> getMResult(String patientIdCard, String raiserIdCard) {
        // 按先拿患者身份证号，读取对应的城市归属几线城市，按房产审核阈值返回数据->拿不到读发起人身份证->取不到对应身份证所属城市阈值则按兜底阈值（五线城市的审核阈值）计算
        CaseCityInfoVO cityInfoVO = new CaseCityInfoVO();
        if(StringUtils.isNotBlank(patientIdCard)){
            String cityCode = getCityCodeByCard(patientIdCard);
            // 患者身份证号未解析到，用发起人到再解析一次
            if(StringUtils.isBlank(cityCode)){
                cityCode = getCityCodeByCard(raiserIdCard);
            }
            cityInfoVO.setPatientCityCode(cityCode);
        } else if(StringUtils.isNotBlank(raiserIdCard)) {
            cityInfoVO.setRaiserCityCode(getCityCodeByCard(raiserIdCard));
        }
        Pair<CityThreshold, String> pair = highRiskV2Strategy.promoteCity(cityInfoVO, null);
        CityThreshold threshold = pair.getLeft();
        // 获取对应几线城市房产审核阈值
        int houseAuditInYuan = threshold.getHouseAuditInYuan();
//        // 获取m值 = 房产审核阈值 * 1.5
//        int mResult = (int) (houseAuditInYuan * 1.5);
//        log.info("getMResult patientIdCard:{} raiserIdCard:{} cityInfo:{} threshold:{} mResult {}",
//                patientIdCard, raiserIdCard, cityInfoVO, threshold, mResult);
        return NewResponseUtil.makeSuccess(houseAuditInYuan);
    }

    @Override
    public Response<Integer> getMResultByParam(HouseThresholdParam houseThresholdParam) {

        if (Objects.isNull(houseThresholdParam)) {
            return NewResponseUtil.makeSuccess(null);
        }

        if ((Objects.isNull(houseThresholdParam.getCaseId()) || houseThresholdParam.getCaseId() == 0) && Objects.isNull(houseThresholdParam.getInfoUuid())) {
            return getMResult(houseThresholdParam.getPatientIdCard(), houseThresholdParam.getRaiserIdCard());
        }

        // 获取案例
        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfoByParam(houseThresholdParam);
        if (Objects.isNull(crowdfundingInfo)) {
            return getMResult(houseThresholdParam.getPatientIdCard(), houseThresholdParam.getRaiserIdCard());
        }

        // 查询历史m值
        Integer mResult = getHistoryMResult(crowdfundingInfo.getId());
        if (Objects.isNull(mResult)) {
            return getMResult(houseThresholdParam.getPatientIdCard(), houseThresholdParam.getRaiserIdCard());
        }

        return NewResponseUtil.makeSuccess(mResult);
    }

    private Integer getHistoryMResult(Integer caseId) {
        RpcResult<CfPropertyInsuranceInfoModel> result =
                cfMaterialReadClient.selectCfPropertyInsuranceInfo(caseId);
        CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel = Optional.ofNullable(result)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(cfPropertyInsuranceInfoModel)) {
            return null;
        }

        return cfPropertyInsuranceInfoModel.getNetWorthThreshold();
    }

    private CrowdfundingInfo getCrowdfundingInfoByParam(HouseThresholdParam houseThresholdParam) {
        FeignResponse<CrowdfundingInfo> response = Objects.isNull(houseThresholdParam.getCaseId())
                ? crowdfundingFeignClient.getCrowdfundingByuuid(houseThresholdParam.getInfoUuid())
                : crowdfundingFeignClient.getCaseInfoById(houseThresholdParam.getCaseId());
        return Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null);
    }

    private String getCityCodeByCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return null;
        }
//        // 判断是否身份证
//        IdcardInfoExtractor isIdcard = new IdcardInfoExtractor(idCard);
//        if (StringUtils.isBlank(isIdcard.getProvince())) {
//            // 身份证未解析到，返回null
//            log.info("getCityCodeByCard idCard:{} is not idCard", idCard);
//            return null;
//        }
        // 只校验身份证长度
        if (idCard.length() != 18 && idCard.length() != 15){
            log.info("getCityCodeByCard idCard:{} is not idCard", idCard);
            return null;
        }
        // 截取身份证前六位为cityCode判断几线城市
        return StringUtils.substring(idCard, 0, 6);
    }

    @Override
    public Response<Integer> getHouseThreshold(String patientIdCard, String raiserIdCard) {
        // 按先拿患者身份证号，读取对应的城市归属几线城市，按房产审核阈值返回数据->拿不到读发起人身份证->取不到对应身份证所属城市阈值则按兜底阈值（五线城市的审核阈值）计算
        CaseCityInfoVO cityInfoVO = new CaseCityInfoVO();
        if(StringUtils.isNotBlank(patientIdCard)){
            String cityCode = getCityCodeByCard(patientIdCard);
            // 患者身份证号未解析到，用发起人到再解析一次
            if(StringUtils.isBlank(cityCode)){
                cityCode = getCityCodeByCard(raiserIdCard);
            }
            cityInfoVO.setPatientCityCode(cityCode);
        } else if(StringUtils.isNotBlank(raiserIdCard)) {
            cityInfoVO.setRaiserCityCode(getCityCodeByCard(raiserIdCard));
        }
        CityThreshold threshold = highRiskV2Strategy.promoteCity(cityInfoVO);
        // 获取对应几线城市房产审核阈值
        int houseAuditInYuan = threshold.getHouseAuditInYuan();
        return NewResponseUtil.makeSuccess(houseAuditInYuan);
    }


    @Override
    public Response<List<HighRiskRecordDto>> getLastByCaseId(int caseId) {
        List<HighRiskRecordDO> list = highRiskRecordDao.getListByCaseId(caseId);
        final ArrayList<HighRiskRecordDto> views = Lists.newArrayList();
        for (HighRiskRecordDO record : list) {
            final HighRiskJudgeResult result = promoteResult(record);
            final HighRiskRecordDto dto = convert2RecordDTO(record);
            dto.setRedFieldCodes(result.getRiskLabels());
            views.add(dto);
        }
        return NewResponseUtil.makeSuccess(views);
    }

    private HighRiskRecordDto convert2RecordDTO(HighRiskRecordDO record) {
        HighRiskRecordDto dto = new HighRiskRecordDto();
        dto.setId(record.getId());
        dto.setCaseId(record.getCaseId());
        dto.setOrderId(record.getOrderId());
        dto.setOperatorId(record.getOperatorId());
        dto.setHit(record.isHit());
        dto.setTriggerSource(record.getTriggerSource());
        dto.setHitCodes(record.getHitCodes());
        dto.setReqParam(record.getReqParam());
        return dto;
    }

    private HighRiskRecordDO buildRecord(JudgeContext judgeContext, HighRiskJudgeResult result) {
        final HighRiskJudgeInfoModel infoModel = judgeContext.getRequestParam();
        HighRiskRecordDO record = new HighRiskRecordDO();
        record.setCaseId(infoModel.getCaseId());
        if (infoModel.getWorkOrderId() != null) {
            record.setOrderId(infoModel.getWorkOrderId());
        }
        if (infoModel.getOperatorId() != null) {
            record.setOperatorId(infoModel.getOperatorId());
        }
        record.setHit(result.isHighRisk());
        record.setVersion(judgeContext.getVersion());
        record.setTriggerSource(infoModel.getSource());
        record.setHitCodes(StringUtils.join(result.getHitCodes(), ","));
        //置位空字符串
        clearData(judgeContext);
        record.setReqParam(JSON.toJSONString(infoModel));
        return record;
    }

    private void clearData(JudgeContext judgeContext) {
        judgeContext.setContent(StringUtils.EMPTY);
    }

    public static void main(String[] args) {
        String idCard = "5454554545";
        IdcardInfoExtractor isIdcard = new IdcardInfoExtractor(idCard);
        if (StringUtils.isAnyBlank(isIdcard.getProvince(), isIdcard.getCity(), isIdcard.getRegion())) {
            System.out.println("is not idCard");

        }
        System.out.println(isIdcard);
    }

}
