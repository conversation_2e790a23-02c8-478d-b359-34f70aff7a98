package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialTumourHandler implements DiseaseSpecialHandler {


    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;
    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz treatmentProjectRpcBiz;

    private static final String TUMOUR_KEY = "disease_special_tumour_rule";

    @Value("${disease.special.treatment-name.no-know:不知道治疗方案}")
    private String noKnowTreatmentName;

    private List<String> tumourClassNameList;

    @Value("${disease.special.tumour-class-name:肿瘤}")
    public void setTumourClassNameList(String tumourClassName) {
        log.info("tumourClassName:{}", tumourClassName);
        if (StringUtils.isBlank(tumourClassName)){
            return;
        }
        this.tumourClassNameList = Splitter.on(",").splitToList(tumourClassName);
    }


    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //处理特殊规则的疾病
        List<SpecialDiseaseRule> specialDiseaseRules = specialDiseaseRuleBiz.findCacheByClassNameList(TUMOUR_KEY, tumourClassNameList);
        if (CollectionUtils.isEmpty(specialDiseaseRules)) {
            return Lists.newArrayList();
        }
        for (SpecialDiseaseRule specialDiseaseRule : specialDiseaseRules) {
            filterTumourRule(specialDiseaseRule, resultVo, diseaseNameList, specialChoiceInfoVos);
        }
        return Lists.newArrayList();
    }

    private List<String> filterTumourRule(SpecialDiseaseRule specialDiseaseRule,
                                          InfoReasonableAmountResultVo resultVo,
                                          List<String> diseaseNameList,
                                          List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //获取肿瘤剩余的疾病
        List<String> filterDiseaseName = filterSpecialDisease(diseaseNameList,
                specialDiseaseRule.getDiseaseContainClassNameList());
        if (log.isDebugEnabled()){
            log.debug("DiseaseSpecialTumourHandler diseaseNameList:{} filterDiseaseName:{}", JSON.toJSONString(diseaseNameList), JSON.toJSONString(filterDiseaseName));
        }
        //如果肿瘤为空直接进行下一个
        if (filterDiseaseName.size() < specialDiseaseRule.getAriseCount()) {
            return Lists.newArrayList();
        }
        log.info("filterDiseaseName:{}", JSON.toJSONString(filterDiseaseName));
        //处理金额 返回结果为单一计算的金额
        List<String> dealResult = dealTumOruAmount(specialDiseaseRule, resultVo, specialChoiceInfoVos, filterDiseaseName, diseaseNameList);
        //增加匹配结果
        addResultDiseaseName(dealResult, resultVo, true);
        //将特殊疾病的置为空
        replaceDiseaseName(diseaseNameList, filterDiseaseName, true, false);
        return Lists.newArrayList();
    }

    /**
     *
     * @param specialDiseaseRule
     * @param resultVo
     * @param specialChoiceInfoVos
     * @param filterDiseaseName
     * @param diseaseNameList
     * @return 不需要计算的疾病名称
     */
    private List<String> dealTumOruAmount(SpecialDiseaseRule specialDiseaseRule, InfoReasonableAmountResultVo resultVo,
                                  List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos,
                                   List<String> filterDiseaseName, List<String> diseaseNameList) {
        if (CollectionUtils.isEmpty(specialChoiceInfoVos)) {
            dealNormalTumour(specialDiseaseRule, resultVo);
            return filterDiseaseName;
        }
        //处理选择项
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> diseaseSpecialInfoVos =
                filterSpecialChoice(specialChoiceInfoVos, filterDiseaseName);
        specialChoiceInfoVos.removeAll(diseaseSpecialInfoVos);
        if (CollectionUtils.isEmpty(diseaseSpecialInfoVos)) {
            dealNormalTumour(specialDiseaseRule, resultVo);
            return filterDiseaseName;
        }
        //判断是否所有 选项是否全为不知道
        List<Long> treatmentIds = Lists.newArrayList();
        Map<Long, String> diseaseNameMap = Maps.newHashMap();
        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : diseaseSpecialInfoVos){
            List<Long> treatmentIdList =  getTreatmentProjectIdFilterNoKnow(specialDiseaseChoiceInfo.getChoiceInfoList());
            for (Long diseaseId : treatmentIdList) {
                diseaseNameMap.put(diseaseId, specialDiseaseChoiceInfo.getSpecialDiseaseName());
            }
            treatmentIds.addAll(treatmentIdList);
        }
        if (CollectionUtils.isEmpty(treatmentIds)){
            dealNormalTumour(specialDiseaseRule, resultVo);
            return filterDiseaseName;
        }
        //取出剩余治疗方案最多的进行计算
        List<RiskDiseaseTreatmentProject> treatmentProjects = treatmentProjectRpcBiz.findById(treatmentIds).stream()
                .filter(v -> !noKnowTreatmentName.equals(v.getProjectName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treatmentProjects)){
            dealNormalTumour(specialDiseaseRule, resultVo);
            return filterDiseaseName;
        }
        RiskDiseaseTreatmentProject maxTreatment = mergeSameDisease(treatmentProjects);
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + maxTreatment.getMinTreatmentFee() / 100.0);
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + maxTreatment.getMaxTreatmentFee() / 100.0);
        //写入治疗方案信息
        String builder = StringUtils.trimToEmpty(resultVo.getTreatmentInfo()) +
                "【" + diseaseNameMap.get(maxTreatment.getId()) + "】:" +
                maxTreatment.getProjectName() +
                "<br>";
        resultVo.setTreatmentInfo(resultVo.getTreatmentInfo() + builder);
        //移除不需要计算的肿瘤
        replaceDiseaseName(diseaseNameList, filterDiseaseName, true, false);
        return Lists.newArrayList(diseaseNameMap.get(maxTreatment.getId()));
    }

    private RiskDiseaseTreatmentProject mergeSameDisease(List<RiskDiseaseTreatmentProject> treatmentProjects) {
        return treatmentProjects.stream().sorted(Comparator.comparing(RiskDiseaseTreatmentProject::getMaxTreatmentFee)
                        .reversed()).collect(Collectors.toList()).get(0);
    }



    private List<Long> getTreatmentProjectIdFilterNoKnow(List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList) {
        if (CollectionUtils.isEmpty(choiceInfoList)){
            return Lists.newArrayList();
        }
        List<Long> treatmentProjectIds = Lists.newArrayList();
        for (SpecialDiseaseChoiceInfoVo.ChoiceInfo choiceInfo : choiceInfoList) {
            if (DiseaseCommonConfig.NO_KNOW_CHOICE_NAME.equals(choiceInfo.getChoiceName())){
                continue;
            }
            if (choiceInfo.isRealCalculateChoice()) {
                treatmentProjectIds.add(choiceInfo.getChoiceId());
            }
            if (CollectionUtils.isEmpty(choiceInfo.getChoiceInfoList())) {
                continue;
            }
            treatmentProjectIds.addAll(getTreatmentProjectIdFilterNoKnow(choiceInfo.getChoiceInfoList()));
        }
        return treatmentProjectIds;
    }


    private void dealNormalTumour(SpecialDiseaseRule specialDiseaseRule, InfoReasonableAmountResultVo resultVo) {
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + specialDiseaseRule.getMergeAmount());
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + specialDiseaseRule.getMergeAmount());
    }
}
