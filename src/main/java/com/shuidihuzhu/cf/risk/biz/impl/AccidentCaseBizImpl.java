package com.shuidihuzhu.cf.risk.biz.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.AccidentCaseBiz;
import com.shuidihuzhu.cf.risk.dao.CfRiskAccidentCaseRecordDao;
import com.shuidihuzhu.cf.risk.model.enums.risk.AccidentCaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.cf.risk.model.risk.ParticipateCaseInfo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@Slf4j
@Service
public class AccidentCaseBizImpl implements AccidentCaseBiz {

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CfRiskAccidentCaseRecordDao cfRiskAccidentCaseRecordDao;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Override
    public List<Integer> followAccidentCaseStrategy(Participate participate) {
        List<Integer> list = Lists.newArrayList();
        if (Objects.isNull(participate) || Objects.isNull(participate.getCaseId()) || participate.getCaseId() <= 0) {
            return list;
        }
        int caseId = participate.getCaseId();
        int operatorId = Objects.isNull(participate.getOperatorId()) ? 0 : participate.getOperatorId();
        long workOrderId = Objects.isNull(participate.getWorkOrderId()) ? 0L : participate.getWorkOrderId();
        String triggerTiming = participate.getTriggerTiming();

        Response<CaseInfoApproveStageDO> response = caseInfoApproveStageFeignClient.getStageInfo(caseId);
        CaseInfoApproveStageDO caseInfoApproveStageDO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        String title = StringUtils.EMPTY;
        String content = StringUtils.EMPTY;
        if (Objects.isNull(caseInfoApproveStageDO)) {
            FeignResponse<CrowdfundingInfo> feignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
            CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
            if (Objects.isNull(crowdfundingInfo)) {
                return list;
            }
            title = crowdfundingInfo.getTitle();
            content = crowdfundingInfo.getContent();
        } else {
            title = caseInfoApproveStageDO.getTitle();
            content = caseInfoApproveStageDO.getContent();
        }

        Set<Integer> hits = Sets.newHashSet();
        if (title.contains(AccidentCaseEnum.CAR_ACCIDENT.getDesc())) {
            hits.add(AccidentCaseEnum.CAR_ACCIDENT.getCode());
        }
        if ((content.contains(AccidentCaseEnum.CAR_ACCIDENT.getDesc()) && content.contains(AccidentCaseEnum.FRACTURE.getDesc()))) {
            hits.add(AccidentCaseEnum.CAR_ACCIDENT_FRACTURE.getCode());
        }
        if (title.contains(AccidentCaseEnum.CAUSE_TROUBLE.getDesc()) || content.contains(AccidentCaseEnum.CAUSE_TROUBLE.getDesc())) {
            hits.add(AccidentCaseEnum.CAUSE_TROUBLE.getCode());
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_POLICE.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_POLICE.getDesc())) {
            hits.add(AccidentCaseEnum.TRAFFIC_POLICE.getCode());
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT.getDesc())) {
            hits.add(AccidentCaseEnum.TRAFFIC_ACCIDENT.getCode());
        }
        if (title.contains(AccidentCaseEnum.EXPLODE.getDesc()) || content.contains(AccidentCaseEnum.EXPLODE.getDesc())) {
            hits.add(AccidentCaseEnum.EXPLODE.getCode());
        }
        if (title.contains(AccidentCaseEnum.BURN.getDesc()) || content.contains(AccidentCaseEnum.BURN.getDesc())) {
            hits.add(AccidentCaseEnum.BURN.getCode());
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT_V1.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT_V1.getDesc())) {
            hits.add(AccidentCaseEnum.TRAFFIC_ACCIDENT_V1.getCode());
        }

        String p = "被[^，]{0,40}车[^，]{0,40}撞";
        Pattern pattern = Pattern.compile(p);
        boolean titleMatches = pattern.matcher(title).find();
        boolean contentMatches = pattern.matcher(content).find();
        if (titleMatches || contentMatches) {
            hits.add(AccidentCaseEnum.HIT_BY_A_CAR.getCode());
        }

        //存到表里
        cfRiskAccidentCaseRecordDao.save(triggerTiming, caseId, operatorId, workOrderId, Joiner.on(",").join(hits), title, content);

        return Lists.newArrayList(hits);
    }

    @Override
    public boolean judgeAccidentCaseStrategy(ParticipateCaseInfo participateCaseInfo) {
        if (Objects.isNull(participateCaseInfo) ||
                StringUtils.isEmpty(participateCaseInfo.getTitle()) ||
                StringUtils.isEmpty(participateCaseInfo.getContent())) {
            return false;
        }

        String title = participateCaseInfo.getTitle();
        String content = participateCaseInfo.getContent();

        if (title.contains(AccidentCaseEnum.CAR_ACCIDENT.getDesc())) {
            return true;
        }
        if ((content.contains(AccidentCaseEnum.CAR_ACCIDENT.getDesc()) && content.contains(AccidentCaseEnum.FRACTURE.getDesc()))) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.CAUSE_TROUBLE.getDesc()) || content.contains(AccidentCaseEnum.CAUSE_TROUBLE.getDesc())) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_POLICE.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_POLICE.getDesc())) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT.getDesc())) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.EXPLODE.getDesc()) || content.contains(AccidentCaseEnum.EXPLODE.getDesc())) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.BURN.getDesc()) || content.contains(AccidentCaseEnum.BURN.getDesc())) {
            return true;
        }
        if (title.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT_V1.getDesc()) || content.contains(AccidentCaseEnum.TRAFFIC_ACCIDENT_V1.getDesc())) {
            return true;
        }

        String p = "被[^，]{0,40}车[^，]{0,40}撞";
        Pattern pattern = Pattern.compile(p);
        boolean titleMatches = pattern.matcher(title).find();
        boolean contentMatches = pattern.matcher(content).find();
        if (titleMatches || contentMatches) {
            return true;
        }

        return false;
    }

}
