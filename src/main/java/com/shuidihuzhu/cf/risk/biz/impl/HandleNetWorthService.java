package com.shuidihuzhu.cf.risk.biz.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.model.risk.CarNetValueInfo;
import com.shuidihuzhu.cf.risk.model.risk.HouseNetValueInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class HandleNetWorthService {

    /**
     * 计算房产净值 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
     */
    public List<Integer> countHouseNetValue(List<HouseNetValueInfo> houseValue) {
        if(CollectionUtils.isEmpty(houseValue)){
            return Lists.newArrayList();
        }
        List<Integer> res = Lists.newArrayList();
        for (HouseNetValueInfo houseNetValueInfo : houseValue) {
            if(houseNetValueInfo == null){
                res.add(0);
                continue;
            }
            if(houseNetValueInfo.getOwnershipRatio() <= 0){
                houseNetValueInfo.setOwnershipRatio(100);
            }
            double loanRatio = getLoanRatio(houseNetValueInfo.getLoanYear());
            // 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
            int houseNetValue = (int) (houseNetValueInfo.getHouseValue() * houseNetValueInfo.getOwnershipRatio() / 100 -
                    (houseNetValueInfo.getLoanMonthRepayment() * 12
                            * (houseNetValueInfo.getLoanYear() - houseNetValueInfo.getLoanPaidYear()) * loanRatio));
            res.add(houseNetValue);
        }
        return res;
    }

    /**
     * 计算车产净值 非营生车车产净值 = 车产价值-未还贷款
     */
    public List<Integer> countCarNetValue(List<CarNetValueInfo> carNetValueInfos) {
        if(CollectionUtils.isEmpty(carNetValueInfos)){
            return Lists.newArrayList();
        }
        List<Integer> res = Lists.newArrayList();
        for (CarNetValueInfo carNetValueInfo : carNetValueInfos) {
            if(carNetValueInfo == null){
                res.add(0);
                continue;
            }
            // 非营生车产价值 <= 3万，不计算在总净值内，计为0
            if (carNetValueInfo.getCarPrice() <= 30000) {
                res.add(0);
                continue;
            }
            // 非营生车车产净值 = 车产价值-未还贷款
            int carNetValue = carNetValueInfo.getCarPrice() - carNetValueInfo.getLoanAmount();
            res.add(carNetValue);
        }
        return res;
    }

    /**
     * 贷款年限系数
     * @param loanYear
     * @return
     */
    private double getLoanRatio(Double loanYear) {
        if (loanYear == null) {
            return 0;
        }
        if (loanYear > 0 && loanYear <= 6) {
            return 0.9;
        }
        if (loanYear > 6 && loanYear <= 14) {
            return 0.8;
        }
        if (loanYear > 14 && loanYear <= 21) {
            return 0.7;
        }
        if (loanYear > 21 && loanYear <= 30) {
            return 0.6;
        }
        return 0;
    }

}
