package com.shuidihuzhu.cf.risk.biz;

import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.cf.risk.model.risk.ParticipateCaseInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/11  5:29 下午
 */
public interface AccidentCaseBiz {
    List<Integer> followAccidentCaseStrategy(Participate participate);

    boolean judgeAccidentCaseStrategy(ParticipateCaseInfo participateCaseInfo);
}
