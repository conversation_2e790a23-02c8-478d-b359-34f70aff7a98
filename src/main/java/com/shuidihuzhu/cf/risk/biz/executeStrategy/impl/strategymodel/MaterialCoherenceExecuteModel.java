package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.risk.biz.disease.OCRBaiDuDelegate;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyLogBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.service.SeaAccountService;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/24
 */
@Service
@Slf4j
public class MaterialCoherenceExecuteModel  extends ExecuteStrategyModelTemplate {

    @Autowired
    private CfFirstApproveClient cfFirstApproveClient;
    @Autowired
    private OCRBaiDuDelegate ocrBaiDuDelegate;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskExecuteStrategyLogBiz strategyLogBiz;


    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noResultMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> partResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> allCanResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME);
        }
    };

    @Override
    public String getCode() {
        return "material_coherence";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        //ocr识别
        //获取医学材料
        RpcResult<CfFirsApproveMaterial> rpcResult =
                cfFirstApproveClient.selectFirstApproveByCaseId(diseaseStrategyRequest.getCaseId());
        if (rpcResult.isFail() || rpcResult.getData() == null) {
            log.error("rpcResult:{}", JSON.toJSONString(rpcResult));
            return;
        }


        //所有可发与特殊可发的疾病
        List<String> canRaiseDiseaseNameList =
                diseaseStrategyResponse.getDiseaseInfoList().stream()
                        .filter(v -> v.getDiseaseType() != RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode())
                        .map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList());
        // OCR识别结果
        String ocrResult = String.join("", ocrBaiDuDelegate.accurateGeneralList(rpcResult.getData().getImageUrl()));
        List<String> ocrDiseseaNameList = Lists.newArrayList();
        for (String s : canRaiseDiseaseNameList) {
            if (!ocrResult.contains(s)) {
                continue;
            }
            ocrDiseseaNameList.add(s);
        }
        List<String> beforeList = Lists.newArrayList(canRaiseDiseaseNameList);
        List<String> tmpList = Lists.newArrayList(canRaiseDiseaseNameList);
        //剩余的为未匹配到的
        canRaiseDiseaseNameList.removeAll(ocrDiseseaNameList);
        //去除无用的ocr数据
        tmpList.removeAll(canRaiseDiseaseNameList);
        ocrDiseseaNameList = Lists.newArrayList(tmpList);
        transit.setMatchList(ocrDiseseaNameList);
        RiskExecuteStrategyLog riskExecuteStrategyLog = buildBaseLog(diseaseStrategyRequest,
                beforeList, ocrDiseseaNameList, tmpList);
        //完全未匹配到
        if (CollectionUtils.isEmpty(ocrDiseseaNameList)
                || canRaiseDiseaseNameList.size() == beforeList.size()) {
            diseaseStrategyResponse.setDiseaseMaterialCoherenceStrategyResult(resultInfo(getMupliteResultEnum(
                    diseaseStrategyRequest.getExecuteStrategyEnum(), noResultMap), ocrDiseseaNameList));
            saveLog(diseaseStrategyResponse, riskExecuteStrategyLog);
            transit.setHaNext(false);
            return;
        }
        //部分匹配到
        if (Lists.newArrayList(ocrDiseseaNameList).size() != beforeList.size()) {
            diseaseStrategyResponse.setDiseaseMaterialCoherenceStrategyResult(resultInfo(getMupliteResultEnum(
                    diseaseStrategyRequest.getExecuteStrategyEnum(), partResultMap), ocrDiseseaNameList));
            saveLog(diseaseStrategyResponse, riskExecuteStrategyLog);
            return;
        }
        //完全匹配到
        diseaseStrategyResponse.setDiseaseMaterialCoherenceStrategyResult(resultInfo(getMupliteResultEnum(
                diseaseStrategyRequest.getExecuteStrategyEnum(), allCanResultMap), ocrDiseseaNameList));
        saveLog(diseaseStrategyResponse, riskExecuteStrategyLog);
    }

    private void saveLog(DiseaseStrategyResponse diseaseStrategyResponse, RiskExecuteStrategyLog riskExecuteStrategyLog) {
        riskExecuteStrategyLog.setStrategyResult(
                String.valueOf(diseaseStrategyResponse.getDiseaseMaterialCoherenceStrategyResult().getResult()));
        riskExecuteStrategyLog.setStrategyResultDesc(diseaseStrategyResponse.getDiseaseMaterialCoherenceStrategyResult().getResultDesc());
        strategyLogBiz.save(Lists.newArrayList(riskExecuteStrategyLog));
    }

    private RiskExecuteStrategyLog buildBaseLog(DiseaseStrategyRequest request,
                              List<String> beforeList, List<String> ocrDiseseaNameList,
                              List<String> canRaiseDiseaseNameList) {
        RiskExecuteStrategyLog riskExecuteStrategyLog = new RiskExecuteStrategyLog();
        riskExecuteStrategyLog.setCaseId(request.getCaseId());
        riskExecuteStrategyLog.setWorkOrderId(request.getWorkOrderId());
        riskExecuteStrategyLog.setExecuteTime(DateUtil.nowTime());
        riskExecuteStrategyLog.setStrategyName("用户填写疾病与医疗材料是否一致");
        Map<String, Object> otherMap = Maps.newHashMap();
        otherMap.put("diseaseName", StringUtils.join(beforeList, "，"));
        otherMap.put("contrastDiseaseName", StringUtils.join(ocrDiseseaNameList, "，"));
        otherMap.put("matchDiseaseName", StringUtils.join(canRaiseDiseaseNameList, "，"));
        otherMap.put("executeStrategyEnum", request.getExecuteStrategyEnum());
        riskExecuteStrategyLog.setOtherInfo(JSON.toJSONString(otherMap));
        riskExecuteStrategyLog.setOperator(seaAccountService.getCurrAdminUserNameWithOrg(request.getUserId()).getUserNameWithOrg());
        return riskExecuteStrategyLog;
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }
}
