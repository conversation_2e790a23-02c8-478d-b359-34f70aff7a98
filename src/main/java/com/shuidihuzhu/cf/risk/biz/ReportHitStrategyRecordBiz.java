package com.shuidihuzhu.cf.risk.biz;

import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-11-05 20:35
 **/
public interface ReportHitStrategyRecordBiz {

    int batchInsert(List<ReportHitStrategyRecord> reportHitStrategyRecordList);

    List<ReportHitStrategyRecord> getByReportIds(List<Integer> reportIds);

    List<ReportHitStrategyRecord> listByCursor(long lastId, int limit);

    int updateEncryptById(long id, String encryptMobile, String encryptReportMobile, String encryptIdCard);

    int updateMobileById(long id, String mobile, String reportMobile, String idCard);
}
