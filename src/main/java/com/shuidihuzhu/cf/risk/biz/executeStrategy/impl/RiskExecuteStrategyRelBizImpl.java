package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyRelBiz;
import com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyRelDao;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyRel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Service
public class RiskExecuteStrategyRelBizImpl implements RiskExecuteStrategyRelBiz {

    @Autowired
    private RiskExecuteStrategyRelDao riskExecuteStrategyRelDao;

    @Override
    public List<RiskExecuteStrategyRel> getByStrategyId(long strategyId) {
        if (strategyId <= 0){
            return Lists.newArrayList();
        }
        return riskExecuteStrategyRelDao.getByStrategyId(strategyId);
    }
}
