package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
@Slf4j
public class DiseaseSpecialChoiceExecuteModel extends ExecuteStrategyModelTemplate {

    @Autowired
    private DiseaseRpcService diseaseRpcService;
    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz riskDiseaseTreatmentProjectRpcBiz;
    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noCanRaiseResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> specialCanResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE);
        }
    };

    @Override
    public String getCode() {
        return "disease_judge_special_raise";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        if (StringUtils.isBlank(diseaseStrategyRequest.getSpecialRaiseChoiceInfo())) {
            return;
        }
        //归一特殊的疾病
        if (CollectionUtils.isEmpty(transit.getDiseaseClassifyVOV2List())) {
            List<DiseaseClassifyVOV2> classifyVoV2List = diseaseRpcService.diseaseNorm(diseaseStrategyRequest.getDiseaseNameList());
            transit.setDiseaseClassifyVOV2List(classifyVoV2List);
        }
        //1. 查询所有疾病的类型
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(transit.getMergeResult());
        Map<String, DiseaseInfoVo> diseaseInfoVoMap = diseaseRpcService.buildVoList(riskDiseaseDataList).stream()
                .collect(Collectors.toMap(DiseaseInfoVo::getDiseaseName, Function.identity()));
        //1.对特殊方案进行判断
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos =
                JSONObject.parseArray(diseaseStrategyRequest.getSpecialRaiseChoiceInfo(), SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
        Map<String, List<Long>> specialRaiseChoiceMap = diseaseRpcService.getChoiceMap(specialDiseaseChoiceInfos);
        List<RiskDiseaseTreatmentProject> treatmentProjects = riskDiseaseTreatmentProjectRpcBiz.findById(specialRaiseChoiceMap.values().stream()
                .reduce(Lists.newArrayList(), (all, one) -> {
                    all.addAll(one);
                    return all;
                }));
        boolean isCanRaise = CollectionUtils.isNotEmpty(treatmentProjects.stream()
                .filter(v -> v.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode())
                .collect(Collectors.toList()));
        setBuildVo(specialRaiseChoiceMap, treatmentProjects, diseaseInfoVoMap);
        //写入归一的结果
        transit.setDiseaseInfoList(Lists.newArrayList(diseaseInfoVoMap.values()));
        transit.setTreatmentInfo(buildTreatmentInfo(treatmentProjects, specialRaiseChoiceMap));
        transit.setCaseRaiseType(!isCanRaise ? RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE : RiskDiseaseData.RaiseTypeEnum.CAN_RAISE);
        diseaseStrategyResponse.setDiseaseInfoList(buildList(transit.getDiseaseClassifyVOV2List(),
                transit.getDiseaseInfoList(), getTreatmentMap(specialRaiseChoiceMap, treatmentProjects)));
        //写入结果
        if (isCanRaise) {
            diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                    resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), specialCanResultMap)));
            diseaseStrategyResponse.getDiseaseRaiseStrategyResult().setSpecialDiseaseChoiceInfoList(specialDiseaseChoiceInfos);
            return;
        }
        diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), noCanRaiseResultMap)));
        diseaseStrategyResponse.getDiseaseRaiseStrategyResult().setSpecialDiseaseChoiceInfoList(specialDiseaseChoiceInfos);
        if (diseaseStrategyRequest.getExecuteStrategyEnum() == DiseaseStrategyEnum.USER_WRITE.getCode()) {
            transit.setHaNext(false);
        }
    }

    private String buildTreatmentInfo(List<RiskDiseaseTreatmentProject> treatmentProjects, Map<String, List<Long>> specialRaiseChoiceMap) {
        StringBuilder treatmentInfo = new StringBuilder();
        for (Map.Entry<String, List<Long>> entry : specialRaiseChoiceMap.entrySet()) {
            List<RiskDiseaseTreatmentProject> treatmentProjectList = treatmentProjects.stream()
                    .filter(v -> entry.getValue().contains(v.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(treatmentProjectList)) {
                continue;
            }
            treatmentInfo.append("【").append(entry.getKey()).append("】：").append(treatmentProjectList.stream()
                    .map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.joining("\n")));
        }
        return treatmentInfo.toString();
    }


    private void setBuildVo(Map<String, List<Long>> specialRaiseChoiceMap,
                            List<RiskDiseaseTreatmentProject> treatmentProjects,
                            Map<String, DiseaseInfoVo> diseaseInfoVoMap) {
        for (Map.Entry<String, List<Long>> entry : specialRaiseChoiceMap.entrySet()) {
            DiseaseInfoVo diseaseInfoVo = diseaseInfoVoMap.get(entry.getKey());
            if (diseaseInfoVo == null) {
                log.warn("diseaseInfoVo is null, name:{}", entry.getKey());
                continue;
            }
            List<RiskDiseaseTreatmentProject> treatmentProjectList = treatmentProjects.stream()
                    .filter(v -> entry.getValue().contains(v.getDiseaseId()))
                    .filter(v -> v.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(treatmentProjectList)) {
                continue;
            }
            diseaseInfoVo.setDiseaseType(RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode());
        }

    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }
}
