package com.shuidihuzhu.cf.risk.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.model.CityInfo;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.client.apipure.feign.CaseCityFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.client.base.page.v1.model.AnchorPageVO;
import com.shuidihuzhu.cf.client.base.result.IOperationResult;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.EconomicSituationEnum;
import com.shuidihuzhu.cf.client.ugc.model.constant.RiskControlWordTypeConsts;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordDO;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitLocationEnum;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckParamV2;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResultV2;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.risk.dao.highrisk.HighRiskCityLevelDao;
import com.shuidihuzhu.cf.risk.model.enums.RiskCarUseType;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskAutoEnum;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskManualEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.CityThreshold;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskCityLevel;
import com.shuidihuzhu.cf.risk.model.highrisk.RiskRuleResult;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.risk.service.TracerService;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class HighRiskV2Strategy {

    private Map<String, RiskRuleResult> ruleCodeResultMap;

    @Resource
    private HighRiskCityLevelDao highRiskCityLevelDao;

    private Map<String, CityThreshold> levelThresholdMap;

    @Resource
    private CaseCityFeignClient caseCityFeignClient;

    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    @Resource
    private InitialAuditWorkOrderFeignClient initialAuditWorkOrderFeignClient;

    @Resource
    private RiskControlWordCheckFeignV2Client checkService;

    @Autowired
    private TracerService tracerService;

    @Autowired
    private RiskControlWordManageClient riskControlWordManageClient;

    @Value("${apollo.high-risk.config-json:{}}")
    public void setConfigJson(String configJson){
        log.info("HighRiskV2Strategy configJson:{}", configJson);
        HighRiskJudgeV2ServiceImpl.Config config = JSON.parseObject(configJson, HighRiskJudgeV2ServiceImpl.Config.class);
        ruleCodeResultMap = config.getRuleResultList().stream().collect(Collectors.toMap(RiskRuleResult::getRuleCode, Function.identity()));
        levelThresholdMap = config.getLevelThresholdMap();
    }

    @Value("${apollo.high-risk.year-income:0}")
    private int yearIncomeAuditInYuan;

    @Value("${apollo.risk.jiabao.category.id:0}")
    private long jiabaoCategoryId;

    public HighRiskJudgeResult exec(JudgeContext judgeContext) {
        final Set<String> hitCodes = promoteRule(judgeContext);
        log.info("HighRiskV2Strategy exec context: {}, hitCodes:{}", judgeContext, hitCodes);
        return promoteResult(hitCodes);
    }

    public HighRiskJudgeResult promoteResult(String hitCodesStr) {
        if (StringUtils.isEmpty(hitCodesStr)) {
            return promoteResult(Sets.newLinkedHashSet());
        }
        Set<String> hitCodes = Sets.newLinkedHashSet(Splitter.on(",").splitToList(hitCodesStr));
        return promoteResult(hitCodes);
    }

    public HighRiskJudgeResult promoteResult(Set<String> hitCodes) {
        HighRiskJudgeResult result = new HighRiskJudgeResult();
        result.setRiskLabels(Lists.newArrayList());
        result.setRiskTips(Lists.newArrayList());
        result.setHitCodes(hitCodes);

        // 无风险
        if (CollectionUtils.isEmpty(hitCodes)) {
            result.setHighRisk(false);
            result.getRiskTips().add("家庭经济情况初步系统预判较合理");
            return result;
        }

        // 有风险
        result.setHighRisk(true);
        int extAction = -1;
        for (String hitCode : hitCodes) {
            RiskRuleResult riskRuleResult = ruleCodeResultMap.get(hitCode);
            result.getRiskLabels().addAll(riskRuleResult.getRedFieldCodes());
            result.getRiskTips().add(riskRuleResult.getMsg());
            if (riskRuleResult.getExtAction() > extAction) {
                extAction = riskRuleResult.getExtAction();
            }
        }
        if (extAction == 10) {
            result.getRiskTips().add("请综合评估发起方案");
        }
        if (extAction == 20) {
            result.getRiskTips().add("请升级应急组处理");
        }
        // 提示文案去重
        result.setRiskTips(result.getRiskTips().stream().distinct().collect(Collectors.toList()));
        //提示信息的互斥逻辑
        mutexPrompt(result);
        return result;
    }

    private void mutexPrompt(HighRiskJudgeResult result) {
        List<String> riskTips = result.getRiskTips();
        if (CollectionUtils.isEmpty(riskTips) || CollectionUtils.size(riskTips) == 1) {
            return;
        }

        riskTips.remove("家庭经济情况初步系统预判较合理");

        Set<String> hitCodes = result.getHitCodes();
        // GFXYM001和GFXYM009同时命中时，走GFXYM009文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM001.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM009.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM001.getRuleCode()).getMsg());
        }
        // GFXYM002和GFXYM010同时命中时，走GFXYM010文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM002.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM010.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM002.getRuleCode()).getMsg());
        }
        // GFXYM003和GFXYM011同时命中时，走GFXYM011文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM003.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM011.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM003.getRuleCode()).getMsg());
        }
        // GFXYM004和GFXYM012同时命中时，走GFXYM012文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM004.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM012.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM004.getRuleCode()).getMsg());
        }
        // GFXYM005和GFXYM013同时命中时，走GFXYM013文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM005.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM013.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM005.getRuleCode()).getMsg());
        }
        // GFXYM006和GFXYM014同时命中时，走GFXYM014文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM006.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM014.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM006.getRuleCode()).getMsg());
        }
        // GFXYM007和GFXYM015同时命中时，走GFXYM015文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM007.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM015.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM007.getRuleCode()).getMsg());
        }
        // GFXYM008和GFXYM016同时命中时，走GFXYM016文案
        if(hitCodes.contains(HighRiskManualEnum.GFXYM008.getRuleCode()) && hitCodes.contains(HighRiskManualEnum.GFXYM016.getRuleCode())){
            riskTips.remove(ruleCodeResultMap.get(HighRiskManualEnum.GFXYM008.getRuleCode()).getMsg());
        }
        result.setRiskTips(riskTips);
    }

    public Set<String> promoteRule(JudgeContext judgeContext) {
        final HighRiskJudgeInfoModel infoModel = judgeContext.getRequestParam();

        final Pair<CityThreshold, String> pair = promoteCity(judgeContext.getCityInfoVO(), infoModel);
        String cityLevel = pair.getRight();
        CityThreshold threshold = pair.getLeft();
        judgeContext.setCityThreshold(threshold);
        // 填充城市等级
        infoModel.setCaseCityLevel(cityLevel);

        HashSet<String> hitCodes = Sets.newHashSet();
        boolean auto = infoModel.getSource() != HighRiskJudgeConst.Source.MANUAL;

        // 房产总价值 >= 房产上限阈值 (试点城市按照总净值算)
        Integer houseAmountInYuan = infoModel.getHouseAmountInYuan();
        Integer houseNetAssetsInYuan = infoModel.getHouseNetAssetsInYuan();
        if (checkIsMoreThanOrEquals(houseNetAssetsInYuan, threshold.getHouseAuditInYuan())) {
            hit(hitCodes, auto ? HighRiskAutoEnum.HRA001.getRuleCode() : HighRiskManualEnum.HRM001.getRuleCode());
        } else if (checkIsMoreThanOrEquals(houseAmountInYuan, threshold.getHouseMaxInYuan())) {
            hit(hitCodes, auto ? HighRiskAutoEnum.HRA001.getRuleCode() : HighRiskManualEnum.HRM001.getRuleCode());
        }

        // 房产总数 > 1
        //AND 房产总价值 >= 房产审核阈值 (试点城市按照总净值算)
        Integer houseCount = infoModel.getHouseCount();
        Integer houseOtherCount = infoModel.getHouseOtherCount();
        if(checkNumNotNullNotZero(houseNetAssetsInYuan)){
            if(checkIsMoreThan(houseCount ,1)) {
                boolean hitHRA002 = checkIsMoreThanOrEquals(houseNetAssetsInYuan, threshold.getHouseAuditInYuan()) ||
                        checkIsMoreThanOrEquals(houseOtherCount, 2);
                transformRiskRule(infoModel, hitCodes, auto, houseCount, hitHRA002);
            }
        } else if (checkIsMoreThanOrEquals(houseAmountInYuan, threshold.getHouseAuditInYuan())) {
            transformRiskRule(infoModel, hitCodes, auto, houseCount, true);
        }


        Integer houseSellCount = infoModel.getHouseSellCount();
        Integer houseSellAmountInYuan = infoModel.getHouseSellAmountInYuan();
        if (checkNumNotNullNotZero(houseSellCount) && checkNumNotNullNotZero(houseSellAmountInYuan)) {
            // 变卖房产数 > 0 AND 变卖价格 >= 50万
            if (houseSellCount > 0 && houseSellAmountInYuan >= 50_0000 && auto) {
                hit(hitCodes, HighRiskAutoEnum.HRA003.getRuleCode());
            }
        }
        // 变卖房产数 > 0
        if (checkNumNotNullNotZero(houseSellCount)) {
            if (houseSellCount > 0 && !auto) {
                hit(hitCodes, HighRiskManualEnum.HRM005.getRuleCode());
            }
        }

        Integer targetAmountInYuan = judgeContext.getTargetAmountInYuan();
        Integer carAmountInYuan = infoModel.getCarAmountInYuan();
        if (checkNumNotNullNotZero(carAmountInYuan)) {
            // 车产价值 >= 目标金额 AND 车产价值 > 10万
            if (carAmountInYuan >= targetAmountInYuan && carAmountInYuan > 10_0000) {
                hit(hitCodes, auto ? HighRiskAutoEnum.HRA004.getRuleCode() : HighRiskManualEnum.HRM006.getRuleCode());
            }

            // 车产价值 >= 车产上限阈值
            if (carAmountInYuan >= threshold.getCarMaxInYuan()) {
                hit(hitCodes, auto ? HighRiskAutoEnum.HRA005.getRuleCode() : HighRiskManualEnum.HRM007.getRuleCode());
            }

            // 车产价值 >= 车产审核阈值
            if (carAmountInYuan >= threshold.getCarAuditInYuan()) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA006.getRuleCode());
                } else {
                    boolean atLeastOne = false;
                    // AND 车产总数 >= 3
                    Integer carCount = infoModel.getCarCount();
                    if (carCount != null && carCount >= 3) {
                        hit(hitCodes, HighRiskManualEnum.HRM008.getRuleCode());
                        atLeastOne = true;
                    }
                    // AND 是否有闲置 = 有
                    Boolean carUnused = infoModel.getCarUnused();
                    if (carUnused != null && carUnused) {
                        hit(hitCodes, HighRiskManualEnum.HRM009.getRuleCode());
                        atLeastOne = true;
                    }
                    // AND 是否有名车 = 是
                    Boolean carIsFamous = infoModel.getCarIsFamous();
                    if (carIsFamous != null && carIsFamous) {
                        hit(hitCodes, HighRiskManualEnum.HRM010.getRuleCode());
                        atLeastOne = true;
                    }

                    // ELSE
                    if (!atLeastOne) {
                        hit(hitCodes, HighRiskManualEnum.HRM011.getRuleCode());
                    }

                }
            }
        }

        // 变卖车产数 > 0 AND 变卖价格 >= 10万
        Integer carSellCount = infoModel.getCarSellCount();
        Integer carSellAmountInYuan = infoModel.getCarSellAmountInYuan();
        if (checkNumNotNullNotZero(carSellCount) && checkNumNotNullNotZero(carSellAmountInYuan)) {
            if (carSellCount > 0 && carSellAmountInYuan >= 10_0000) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA007.getRuleCode());
                }
            }
        }

        // 车产有闲置
        if (!auto) {
            Boolean carUnused = infoModel.getCarUnused();
            if (carUnused != null && carUnused) {
                hit(hitCodes, HighRiskManualEnum.HRM012.getRuleCode());
            }
        }

        Integer yearIncome = infoModel.getYearIncome();
        if (checkNumNotNullNotZero(yearIncome)) {
            // 年收入 >= 年收入审核阈值
            if (yearIncome >= threshold.getYearIncomeAuditInYuan()) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA008.getRuleCode());
                } else {
                    // 年收入/2 >= 目标金额
                    if (yearIncome / 2 >= targetAmountInYuan) {
                        hit(hitCodes, HighRiskManualEnum.HRM013.getRuleCode());
                    } else {
                        hit(hitCodes, HighRiskManualEnum.HRM014.getRuleCode());
                    }
                }
            }

            if(checkNumNotNullNotZero(targetAmountInYuan)){
                // 年收入 >= 目标金额（案例发起的时候的目标金额）*2
                if(yearIncome >= yearIncomeAuditInYuan && yearIncome >= targetAmountInYuan * 2){
                    if (auto) {
                        hit(hitCodes, HighRiskAutoEnum.NRS001.getRuleCode());
                    }
                }
            }
        }


        // 金融资产 >= 5万 AND 金融资产/目标金额 > 0.2
        Integer monetaryAssertInYuan = infoModel.getMonetaryAssertInYuan();
        if (checkNumNotNullNotZero(monetaryAssertInYuan)) {
            if (monetaryAssertInYuan >= 5_0000 && (1.0D * monetaryAssertInYuan / targetAmountInYuan) > 0.2D) {
                hit(hitCodes, auto ? HighRiskAutoEnum.HRA009.getRuleCode() : HighRiskManualEnum.HRM015.getRuleCode());
            }
        }

        // 负债金额 >= 200万
        Integer debtInYuan = infoModel.getDebtInYuan();
        if (checkNumNotNullNotZero(debtInYuan)) {
            if (debtInYuan >= 200_0000) {
                hit(hitCodes, auto ? HighRiskAutoEnum.HRA010.getRuleCode() : HighRiskManualEnum.HRM016.getRuleCode());
            }
        }

        // 剩余款项 >= 5万
        Integer remainAmountInYuan = infoModel.getRemainAmountInYuan();
        if (checkNumNotNullNotZero(remainAmountInYuan)) {
            if (remainAmountInYuan >= 5_0000) {
                hit(hitCodes, auto ? HighRiskAutoEnum.HRA011.getRuleCode() : HighRiskManualEnum.HRM017.getRuleCode());
            }
        }

        // 【有人身险】   and  目标金额大于20w
        Boolean personalInsurance = infoModel.getPersonalInsurance();
        if (personalInsurance != null) {
            if (personalInsurance && targetAmountInYuan > 20_0000) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA012.getRuleCode());
                } else {
                    // 0:不清楚/1:已赔偿/2:可后续赔偿/3:不可赔偿
                    Integer insurancePayoutStatus = infoModel.getInsurancePayoutStatus();
                    if (insurancePayoutStatus == 3) {
                        hit(hitCodes, HighRiskManualEnum.HRM018.getRuleCode());
                    }
                    if (insurancePayoutStatus == 2) {
                        hit(hitCodes, HighRiskManualEnum.HRM019.getRuleCode());
                    }
                    if (insurancePayoutStatus == 0) {
                        hit(hitCodes, HighRiskManualEnum.HRM021.getRuleCode());
                    }
                    // 人身险赔付理赔金额是否可以覆盖治疗花费 1:可覆盖/2:不可覆盖
                    if (insurancePayoutStatus == 1 && infoModel.getInsurancePayoutCTC() == 2) {
                        hit(hitCodes, HighRiskManualEnum.HRM020.getRuleCode());
                    }
                    if (insurancePayoutStatus == 1 && infoModel.getInsurancePayoutCTC() == 1) {
                        hit(hitCodes, HighRiskManualEnum.HRM027.getRuleCode());
                    }
                }
            }
        }

        // 是否普通案例 = 否
        Boolean normalCase = infoModel.getNormalCase();
        if (normalCase != null) {
            if (!normalCase) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA013.getRuleCode());
                } else {
                    // 事故赔付状态 0:不清楚/1:已赔偿/2:可后续赔偿/3:不可赔偿 手动
                    Integer accidentPayoutStatus = infoModel.getAccidentPayoutStatus();
                    // 事故赔付理赔金额是否可以覆盖治疗花费 1:可覆盖/2:不可覆盖
                    Integer accidentPayoutCTC = infoModel.getAccidentPayoutCTC();
                    if (accidentPayoutStatus == 3) {
                        hit(hitCodes, HighRiskManualEnum.HRM022.getRuleCode());
                    }
                    if (accidentPayoutStatus == 0) {
                        hit(hitCodes, HighRiskManualEnum.HRM023.getRuleCode());
                    }
                    if (accidentPayoutStatus == 2) {
                        hit(hitCodes, HighRiskManualEnum.HRM024.getRuleCode());
                    }
                    if (accidentPayoutStatus == 1 && accidentPayoutCTC == 2) {
                        hit(hitCodes, HighRiskManualEnum.HRM025.getRuleCode());
                    }
                    if (accidentPayoutStatus == 1 && accidentPayoutCTC == 1) {
                        hit(hitCodes, HighRiskManualEnum.HRM028.getRuleCode());
                    }
                }
            }
        }

        // 案例城市等级 = 1 AND 其他房屋总价值 <= 10万
        Integer otherHouseAmountInYuan = infoModel.getOtherHouseAmountInYuan();
        boolean oneLevelCity = otherHouseAmountInYuan != null &&
                StringUtils.equals(infoModel.getCaseCityLevelRank(), HighRiskCityLevel.ONE);
        if (oneLevelCity && otherHouseAmountInYuan <= 10_0000) {
            hit(hitCodes, auto ? HighRiskAutoEnum.HRA014.getRuleCode() : HighRiskManualEnum.HRM026.getRuleCode());
        }

        //身份敏感
        //患者身份为【现役军人(包括直系亲属)】AND 文章内容不含‘退伍’AND 文章内容不含‘退役’
        if (Objects.nonNull(infoModel.getPatientIdentity()) &&
                infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.ACTIVE_DUTY_SOLDIER.getCode() &&
                !judgeContext.getContent().contains("退伍") &&
                !judgeContext.getContent().contains("退役")) {
            if (auto) {
                infoModel.setPatientIdentityHit(true);
                hit(hitCodes, HighRiskAutoEnum.HRA015.getRuleCode());
            }
        }

        //患者身份为【县级及以上政府官员】、【媒体圈相关人员】、【涉及媒体或政府关注】
        if (Objects.nonNull(infoModel.getPatientIdentity()) &&
                (infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.GOVERNMENT_OFFICER.getCode() ||
                        infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.MEDIA_ABOUT.getCode() ||
                        infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.MEDIA_GOVERNMENT_NOTICE.getCode()
                )) {
            if (auto) {
                infoModel.setPatientIdentityHit(true);
                hit(hitCodes, HighRiskAutoEnum.HRA016.getRuleCode());
            }
        }

        final Integer yearAmount = Objects.requireNonNullElse(infoModel.getYearIncome(), 0);
        PatientToVolunteerParam toVolunteerParam = PatientToVolunteerParam.builder()
                .caseId(infoModel.getCaseId())
                .useType(PatientToVolunteerConst.Source.INITIAL_AUDIT)
                .yearAmount(yearAmount)
                .build();
        OperationResult<Integer> patientToVolunteerResult = initialAuditWorkOrderFeignClient.patientToVolunteer(toVolunteerParam);
        Integer integer = Optional.ofNullable(patientToVolunteerResult)
                .map(OperationResult::getData)
                .orElse(0);
        if (Objects.equals(integer, VolunteerLevelEnum.PARTNER_COMMON.getLevel()) || Objects.equals(integer, VolunteerLevelEnum.PARTNER_AID.getLevel())) {
            hit(hitCodes, HighRiskAutoEnum.HRA017.getRuleCode());
        }
        if ((Objects.equals(integer, VolunteerLevelEnum.COMMON_LEADER.getLevel()) ||
                Objects.equals(integer, VolunteerLevelEnum.PROVINCE_LEADER.getLevel()) ||
                Objects.equals(integer, VolunteerLevelEnum.PARTNER_MANAGER.getLevel())) && yearAmount > 30000) {
            hit(hitCodes, HighRiskAutoEnum.HRA018.getRuleCode());
        }
        if ((Objects.equals(integer, VolunteerLevelEnum.COMMON_LEADER.getLevel()) ||
                Objects.equals(integer, VolunteerLevelEnum.PROVINCE_LEADER.getLevel()) ||
                Objects.equals(integer, VolunteerLevelEnum.PARTNER_MANAGER.getLevel())) && yearAmount <= 30000) {
            hit(hitCodes, HighRiskAutoEnum.HRA019.getRuleCode());
        }

        // 案例 求助文章中 命中家暴/虐待高风险分类下的任意一个关键词后自动生成为高风险工单（高风险策略码：HRA020）
        // 需求文档：https://wdh.feishu.cn/wiki/E8IMwQuEZisB0gk4ZDCc57PSnyd
        if(StringUtils.isNotBlank(judgeContext.getContentForHighRisk())){
            log.info("judgeContext.getContentForHighRisk:{}", judgeContext.getContentForHighRisk());
            // 获取文章命中的关键词
            RiskWordCheckParamV2 ctx = RiskWordCheckParamV2.builder()
                    .content(judgeContext.getContentForHighRisk())
                    .useScene(RiskControlWordCategoryDO.RiskWordUseScene.TITLE_CONTENT.getCode())
                    .needsReturnContent(false)
                    .needsSaveRecord(true)
                    .action(0)
                    .location(HitLocationEnum.COMMENT.getCode())
                    .bizId(tracerService.getSpanId())
                    .isCheckAll(true)
                    .recordInfoJson(JSON.toJSONString(infoModel))
                    .build();
            Response<RiskWordResultV2> resp1 = checkService.checkV2(ctx);
            List<String> hitWords = Lists.newArrayList();
            if (resp1.ok() && CollectionUtils.isNotEmpty(resp1.getData().getHitWords())){
                hitWords.addAll(resp1.getData().getHitWords());
            }

            // 获取所有家暴类型关键词
            Response<List<RiskControlWordDO>> resp2 = riskControlWordManageClient
                    .searchByTypeAll(RiskControlWordTypeConsts.KEYWORD,
                            jiabaoCategoryId,
                            "",
                            RiskControlWordCategoryDO.RiskWordUseScene.TITLE_CONTENT.getCode());
            List<String> needCheckWords = Lists.newArrayList();
            if(resp2.ok() && CollectionUtils.isNotEmpty(resp2.getData())){
                needCheckWords.addAll(resp2.getData().stream().map(RiskControlWordDO::getContent).collect(Collectors.toList()));
            }

            log.info("judgeContext hitWords:{}, needCheckWords:{}", hitWords, needCheckWords);
            if(CollectionUtils.isNotEmpty(hitWords) && CollectionUtils.isNotEmpty(needCheckWords)){
                for (String hitWord : hitWords) {
                    if(needCheckWords.contains(hitWord)){
                        hit(hitCodes, HighRiskAutoEnum.HRA020.getRuleCode());
                        break;
                    }
                }
            }
        }


        List<Integer> marriedChildrenStatus = infoModel.getMarriedChildrenStatus();
        // 案例发起时勾选“患者的任一子女拥有净值超过M万房产”
        if(CollectionUtils.isNotEmpty(marriedChildrenStatus) &&
                marriedChildrenStatus.contains(EconomicSituationEnum.HOUSE_WITH_NET_WORTH.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY001.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM001.getRuleCode());
            }
        }

        // 案例发起时勾选“已婚子女-拥有两套或两套以上房产”
        if(CollectionUtils.isNotEmpty(marriedChildrenStatus) &&
                marriedChildrenStatus.contains(EconomicSituationEnum.HOUSE_WITH_COUNT.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY002.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM002.getRuleCode());
            }
        }

        // 案例发起时勾选“已婚子女拥有净值超过20万非营生车”
        if(CollectionUtils.isNotEmpty(marriedChildrenStatus) &&
                marriedChildrenStatus.contains(EconomicSituationEnum.NON_COMMERCIAL_WITH_NET_WORTH.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY003.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM005.getRuleCode());
            }
        }

        // 案例发起时勾选“已婚子女-拥有两辆或者两辆以上非营生车”
        if(CollectionUtils.isNotEmpty(marriedChildrenStatus) &&
                marriedChildrenStatus.contains(EconomicSituationEnum.NON_COMMERCIAL_WITH_COUNT.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY004.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM006.getRuleCode());
            }
        }

        List<Integer> patientParentStatus = infoModel.getPatientParentStatus();
        // 案例发起时勾选“患者的父母拥有净值超过M万房产”
        if(CollectionUtils.isNotEmpty(patientParentStatus) &&
                patientParentStatus.contains(EconomicSituationEnum.HOUSE_WITH_NET_WORTH.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY005.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM003.getRuleCode());
            }
        }

        // 案例发起时勾选“患者的父母-拥有两套或两套以上房产”
        if(CollectionUtils.isNotEmpty(patientParentStatus) &&
                patientParentStatus.contains(EconomicSituationEnum.HOUSE_WITH_COUNT.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY006.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM004.getRuleCode());
            }
        }

        // 案例发起时勾选“患者的父母拥有超过20W的营生车”
        if(CollectionUtils.isNotEmpty(patientParentStatus) &&
                patientParentStatus.contains(EconomicSituationEnum.NON_COMMERCIAL_WITH_NET_WORTH.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY007.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM007.getRuleCode());
            }
        }

        // 案例发起时勾选“患者的父母拥有两辆及两辆以上的非营生车”
        if(CollectionUtils.isNotEmpty(patientParentStatus) &&
                patientParentStatus.contains(EconomicSituationEnum.NON_COMMERCIAL_WITH_COUNT.getCode())) {
            if(auto) {
                hit(hitCodes, HighRiskAutoEnum.GFXY008.getRuleCode());
            } else {
                hit(hitCodes, HighRiskManualEnum.GFXYM008.getRuleCode());
            }
        }


        // --------------------------------
        // 小工具特有规则

        List<EconomyModel.RelativeDetailInfo> marriedChildrenInfoList = infoModel.getMarriedChildrenInfoList();
        handleRelativeDetail(hitCodes, targetAmountInYuan, marriedChildrenInfoList, "marriedChildren");
        List<EconomyModel.RelativeDetailInfo> patientParentInfoList = infoModel.getPatientParentInfoList();
        handleRelativeDetail(hitCodes, targetAmountInYuan, patientParentInfoList, "patientParent");


        return hitCodes;
    }

    private void handleRelativeDetail(HashSet<String> hitCodes, Integer targetAmountInYuan,
                                      List<EconomyModel.RelativeDetailInfo> detailInfoList,
                                      String handleType) {
        if(CollectionUtils.isEmpty(detailInfoList)) {
            return;
        }
        List<EconomyModel.HouseDetailInfo> houseDetailInfoList = Lists.newArrayList();
        List<EconomyModel.CarDetailInfo> carDetailInfoList = Lists.newArrayList();
        for (EconomyModel.RelativeDetailInfo relativeDetailInfo : detailInfoList) {

            // 房产判断
            if(CollectionUtils.isNotEmpty(relativeDetailInfo.getHouseDetailInfoList())) {
                houseDetailInfoList.addAll(relativeDetailInfo.getHouseDetailInfoList());
                for (EconomyModel.HouseDetailInfo houseDetailInfo : relativeDetailInfo.getHouseDetailInfoList()) {
                    // 已婚子女/患者父母-任一房产净值>房产阈值1倍
                    // 根据房产位置计算房产阈值
                    String cityCode = houseDetailInfo.getCityCode();
                    CityThreshold cityThreshold = getCityThresholdByCityLevel(getCityLevelByCityCode(cityCode));
                    int houseNetAssets = cityThreshold.getHouseAuditInYuan();
                    if(houseDetailInfo.getHouseNetValue() > houseNetAssets) {
                        if("marriedChildren".equals(handleType)) {
                            hit(hitCodes, HighRiskManualEnum.GFXYM009.getRuleCode());
                        } else if("patientParent".equals(handleType)) {
                            hit(hitCodes, HighRiskManualEnum.GFXYM011.getRuleCode());
                        }
                        break;
                    }
                }
            }

            // 车产判断
            if(CollectionUtils.isNotEmpty(relativeDetailInfo.getCarDetailInfoList())) {
                carDetailInfoList.addAll(relativeDetailInfo.getCarDetailInfoList());
                for (EconomyModel.CarDetailInfo carDetailInfo : relativeDetailInfo.getCarDetailInfoList()) {
                    if(carDetailInfo == null) {
                        continue;
                    }
                    if(carDetailInfo.getCarUseStatus() != RiskCarUseType.NON_BUSINESS_CAR.getCode()){
                        continue;
                    }
                    // 已婚子女/患者父母-任一非营生车车产净值>=20万
                    if(carDetailInfo.getCarAmount() >= 200000) {
                        if("marriedChildren".equals(handleType)) {
                            hit(hitCodes, HighRiskManualEnum.GFXYM013.getRuleCode());
                        } else if("patientParent".equals(handleType)) {
                            hit(hitCodes, HighRiskManualEnum.GFXYM015.getRuleCode());
                        }
                        break;
                    }
                }
            }
        }

        // 房产数量>=2，且闲置房产净值总和大于等于疾病花费
        // 计算规则：房产净值总和-最高那套房产净值-疾病花费（目标筹款金额）>=0
        if(CollectionUtils.size(houseDetailInfoList) >= 2) {
            int totalHouseNetAssets = houseDetailInfoList.stream().mapToInt(EconomyModel.HouseDetailInfo::getHouseNetValue).sum();
            int maxHouseNetAssets = houseDetailInfoList.stream().mapToInt(EconomyModel.HouseDetailInfo::getHouseNetValue).max().orElse(0);
            if(totalHouseNetAssets - maxHouseNetAssets - targetAmountInYuan >= 0) {
                if("marriedChildren".equals(handleType)) {
                    hit(hitCodes, HighRiskManualEnum.GFXYM010.getRuleCode());
                } else if("patientParent".equals(handleType)) {
                    hit(hitCodes, HighRiskManualEnum.GFXYM012.getRuleCode());
                }
            }
        }

        // 车产数量≥2，且闲置非营生车车产价值总和大于等于疾病花费
        // 计算规则：非营生车车产价值总和-最高那辆非营生车车产价值-疾病花费（目标筹款金额）>=0
        if(CollectionUtils.size(carDetailInfoList) >= 2) {
            int totalCarAmount = carDetailInfoList.stream().filter(v -> v.getCarUseStatus() == RiskCarUseType.NON_BUSINESS_CAR.getCode())
                    .mapToInt(EconomyModel.CarDetailInfo::getCarAmount).sum();
            int maxCarAmount = carDetailInfoList.stream().filter(v -> v.getCarUseStatus() == RiskCarUseType.NON_BUSINESS_CAR.getCode())
                    .mapToInt(EconomyModel.CarDetailInfo::getCarAmount).max().orElse(0);
            if(totalCarAmount - maxCarAmount - targetAmountInYuan >= 0) {
                if("marriedChildren".equals(handleType)) {
                    hit(hitCodes, HighRiskManualEnum.GFXYM014.getRuleCode());
                } else if("patientParent".equals(handleType)) {
                    hit(hitCodes, HighRiskManualEnum.GFXYM016.getRuleCode());
                }
            }
        }
    }

    private void transformRiskRule(HighRiskJudgeInfoModel infoModel, HashSet<String> hitCodes,
                                   boolean auto, Integer houseCount, boolean hitHRA002) {
        if (auto) {
            if(hitHRA002) {
                hit(hitCodes, HighRiskAutoEnum.HRA002.getRuleCode());
            }
        } else {
            if (infoModel.getHouseUnused() == null) {
                hit(hitCodes, HighRiskManualEnum.HRM004.getRuleCode());
            } else {
                // AND 是否有闲置 = 有
                if (infoModel.getHouseUnused()) {
                    hit(hitCodes, HighRiskManualEnum.HRM003.getRuleCode());
                } else if (!infoModel.getHouseUnused() && houseCount >= 3) {
                    // AND 是否有闲置 = 无 AND 房产总数 >= 3
                    hit(hitCodes, HighRiskManualEnum.HRM002.getRuleCode());
                } else {
                    hit(hitCodes, HighRiskManualEnum.HRM004.getRuleCode());
                }
            }
        }
    }

    public Response<String> getCityLevelByCaseId(int caseId) {
        IOperationResult<CaseCityInfoVO> caseCityInfoResp = caseCityFeignClient.getCityInfoByCaseId(caseId);
        if (caseCityInfoResp.isFail()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        CaseCityInfoVO cityInfoVO = caseCityInfoResp.getData();
        final Pair<String, String> levelCodePair = getCityLevelCodePairByCityInfo(cityInfoVO);
        return NewResponseUtil.makeSuccess(levelCodePair.getLeft());
    }

    public CityThreshold promoteCity(CaseCityInfoVO cityInfoVO) {
        if(cityInfoVO == null){
            return getCityThresholdByCityLevel(HighRiskCityLevel.FIVE);
        }
        String level;
        final Pair<String, String> levelCodePair = getCityLevelCodePairByCityInfo(cityInfoVO);
        level = levelCodePair.getLeft();
        CityThreshold threshold = getCityThresholdByCityLevel(level);
        return threshold;
    }


    public Pair<CityThreshold, String> promoteCity(CaseCityInfoVO cityInfoVO, HighRiskJudgeInfoModel infoModel) {
        if(cityInfoVO == null && infoModel == null){
            return Pair.of(getCityThresholdByCityLevel(HighRiskCityLevel.FIVE), null);
        }
        String level;
        String hitCityCode;
        final Pair<String, String> levelCodePair = getCityLevelCodePairByCityInfo(cityInfoVO);
        level = levelCodePair.getLeft();
        hitCityCode = levelCodePair.getRight();
        if(infoModel != null) {
            infoModel.setCaseCityLevelRank(level);
        }
        CityThreshold threshold = getCityThresholdByCityLevel(level);
        return Pair.of(threshold, hitCityCode);
    }

    private Pair<String, String> getCityLevelCodePairByCityInfo(CaseCityInfoVO cityInfoVO) {
        String bdCityCode = cityInfoVO.getBdCityCode();
        String patientCityCode = cityInfoVO.getPatientCityCode();
        String raiserCityCode = cityInfoVO.getRaiserCityCode();
        List<String> allCityCodeList = Lists.newArrayList(bdCityCode, patientCityCode, raiserCityCode);
        Response<List<CrowdfundingCity>> listCityByCodes = null;
        List<String> queryCityCodes = allCityCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryCityCodes)) {
            listCityByCodes = crowdfundingCityFeignClient.getListByCodes(queryCityCodes);
        }
        if (listCityByCodes != null && listCityByCodes.ok()) {
            List<CrowdfundingCity> data = listCityByCodes.getData();
            List<String> newCityCodes = data.stream()
                    .flatMap(city -> city.getNewCityVersionInfoList().stream())
                    .map(CrowdfundingCity.NewCityVersionInfo::getNewCode)
                    .collect(Collectors.toList());
            allCityCodeList.addAll(newCityCodes);
        }
        String level = null;
        String hitCityCode = null;
        for (String code : allCityCodeList) {
            level = getCityLevelByCityCode(code);
            hitCityCode = code;
            if (StringUtils.isNotBlank(level)) {
                break;
            }
        }
        if (StringUtils.isBlank(level)) {
            level = HighRiskCityLevel.FIVE;
        }
        return Pair.of(level, hitCityCode);
    }

    private String getCityLevelByCityCode(String cityCode) {
        if (StringUtils.isBlank(cityCode)) {
            return HighRiskCityLevel.FIVE;
        }
        cityCode = StringUtils.substring(cityCode, 0, 4);
        String level = highRiskCityLevelDao.getLevelByCityCode(cityCode);
        if (StringUtils.isEmpty(level)) {
            return HighRiskCityLevel.FIVE;
        }
        return level;
    }

    private CityThreshold getCityThresholdByCityLevel(String cityLevel) {
        return levelThresholdMap.get(cityLevel);
    }

    private boolean checkNumNotNullNotZero(Integer houseAmountInYuan) {
        return houseAmountInYuan != null && houseAmountInYuan != 0;
    }


    private boolean checkIsMoreThan(Integer source, int target) {
        if(source == null) {
            return false;
        }
        return source > target;
    }

    private boolean checkIsMoreThanOrEquals(Integer source, int target) {
        if(source == null) {
            return false;
        }
        return source >= target;
    }

    private void hit(HashSet<String> hitCodes, String code) {
        hitCodes.add(code);
    }
}
