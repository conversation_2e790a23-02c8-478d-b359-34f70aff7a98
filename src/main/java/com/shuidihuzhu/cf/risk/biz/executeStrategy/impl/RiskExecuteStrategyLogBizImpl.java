package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl;

import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyLogBiz;
import com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyLogDao;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Service
public class RiskExecuteStrategyLogBizImpl implements RiskExecuteStrategyLogBiz {
    @Autowired
    private RiskExecuteStrategyLogDao riskExecuteStrategyLogDao;

    @Override
    public int save(List<RiskExecuteStrategyLog> riskExecuteStrategyLogs) {
        riskExecuteStrategyLogs =  riskExecuteStrategyLogs.stream().filter(v -> StringUtils.isNotBlank(v.getStrategyResultDesc())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(riskExecuteStrategyLogs)) {
            return 0;
        }
        return riskExecuteStrategyLogDao.saveList(riskExecuteStrategyLogs);
    }

    @Override
    public List<RiskExecuteStrategyLog> getByCaseId(int caseId) {
        return riskExecuteStrategyLogDao.getByCaseId(caseId);
    }
}
