package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/10
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialHeartHandler implements DiseaseSpecialHandler {

    //private static Pattern PATTER_HEART;

    private List<String> heartList;

    @Value("${disease.heartPatter:二尖瓣疾病,三尖瓣疾病,主动脉瓣疾病,肺动脉瓣疾病,机械瓣功能障碍}")
    public void setHeartList(String heartPatter) {
        log.info("heartPatter:{}", heartPatter);
        if (StringUtils.isBlank(heartPatter)) {
            return;
        }
        heartList = Splitter.on(",").splitToList(heartPatter);
    }

    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        if (CollectionUtils.isEmpty(diseaseNameList)) {
            return Lists.newArrayList();
        }
        List<String> heartDiseaseList = Lists.newArrayList();
        for (int i = 0; i < diseaseNameList.size(); i++) {
            String diseaseName = diseaseNameList.get(i);
            if (StringUtils.isBlank(diseaseName) || !heartList.contains(diseaseName)) {
                continue;
            }
            heartDiseaseList.add(diseaseName);
            diseaseNameList.set(i, "");
        }
        if (CollectionUtils.isEmpty(heartDiseaseList)) {
            return Lists.newArrayList();
        }
        addResultDiseaseName(heartDiseaseList, resultVo, true);
        double heartAmount = getHeartAmount(heartDiseaseList);
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + heartAmount);
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + heartAmount);
        return Lists.newArrayList();
    }

    private double getHeartAmount(List<String> heartDiseaseList) {
        if (CollectionUtils.isEmpty(heartDiseaseList)) {
            return 0;
        }
        if (heartDiseaseList.size() > 5) {
            return 20;
        }
        return 8 + (heartDiseaseList.size() - 1) * 3;
    }
}
