package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/9
 *
 * 肿瘤特殊规则处理
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialMergeHandler implements DiseaseSpecialHandler {


    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;

    private static final String MERGE_KEY = "disease_special_merge_rule";

    private List<String> mergeClassNameList;

    @Value("${disease.special.merge-class-name:早产儿Merge,先天性心脏病Merge,脑梗死Merge,先天性心脏病Merge3}")
    public void setMergeClassNameList(String tumourClassName) {
        log.info("tumourClassName:{}", tumourClassName);
        if (StringUtils.isBlank(tumourClassName)){
            return;
        }
        this.mergeClassNameList = Splitter.on(",").splitToList(tumourClassName);
    }

    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //获取归一规则对应的所有案例 并且按照疾病个数  ariseCount从大到小偶爱徐
        List<SpecialDiseaseRule> specialDiseaseRules =
                specialDiseaseRuleBiz.findCacheByClassNameList(MERGE_KEY, mergeClassNameList).stream()
                        .sorted(Comparator.comparing(SpecialDiseaseRule::getAriseCount).reversed()).collect(Collectors.toList());
        //处理特殊规则的疾病
        List<String> needCalculate = Lists.newArrayList();
        for (SpecialDiseaseRule specialDiseaseRule : specialDiseaseRules) {
            needCalculate.addAll(filterMergeRule(specialDiseaseRule, resultVo, diseaseNameList, specialChoiceInfoVos));
        }
        return needCalculate;
    }

    private List<String> filterMergeRule(SpecialDiseaseRule specialDiseaseRule,
                                         InfoReasonableAmountResultVo resultVo,
                                         List<String> diseaseNameList,
                                         List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //过滤肿瘤的案例 去除前三的规则
        List<String> filterDiseaseName = filterSpecialDisease(diseaseNameList,
                specialDiseaseRule.getDiseaseContainClassNameList());
        if (log.isDebugEnabled()){
            log.debug("diseaseNameList:{} filterDiseaseName:{}", JSON.toJSONString(diseaseNameList),
                    JSON.toJSONString(filterDiseaseName));
        }
        //如果小于需要合并个数则进行下一条
        if (filterDiseaseName.size() < specialDiseaseRule.getAriseCount()) {
            return Lists.newArrayList();
        }
        if (conformHeart(specialChoiceInfoVos, specialDiseaseRule, diseaseNameList, filterDiseaseName)) {
            return Lists.newArrayList();
        }
        //增加匹配结果
        addResultDiseaseName(filterDiseaseName, resultVo, true);
        resultVo.setMatchMinAmount(resultVo.getMatchMinAmount() + specialDiseaseRule.getMergeAmount());
        resultVo.setMatchMaxAmount(resultVo.getMatchMaxAmount() + specialDiseaseRule.getMergeAmount());
        log.info("filterDiseaseName:{}", JSON.toJSONString(filterDiseaseName));
        //将特殊疾病的置为空
        replaceDiseaseName(diseaseNameList, filterDiseaseName, true,
                DiseaseCommonConfig.HEART_MERGE_3.equals(specialDiseaseRule.getDiseaseClassName()));
        if (specialDiseaseRule.getMergeAmount() == 0){
            return filterDiseaseName;
        }
        return Lists.newArrayList();
    }


    /**
     * 处理先天性心脏病Merge2的规则
     * @param specialChoiceInfoVos
     * @param specialDiseaseRule
     * @param diseaseNameList
     * @param filterDiseaseName
     * @return
     */
    private boolean conformHeart(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos,
                                 SpecialDiseaseRule specialDiseaseRule,
                                 List<String> diseaseNameList,
                                 List<String> filterDiseaseName) {
        if (!DiseaseCommonConfig.HEART_MERGE_A.equals(specialDiseaseRule.getDiseaseClassName())){
            return false;
        }
        //验证是否存在A组的
        SpecialDiseaseRule heartRuleB = specialDiseaseRuleBiz.getByClassNameCache(DiseaseCommonConfig.HEART_MERGE_B);
        List<String> filterList = diseaseNameList.stream()
                .filter(v -> heartRuleB.getDiseaseContainClassNameList().contains(v)).collect(Collectors.toList());
        if (filterList.size() < filterDiseaseName.size()) {
            return false;
        }
        if (CollectionUtils.isEmpty(specialChoiceInfoVos)) {
            return true;
        }
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> choiceNameList = specialChoiceInfoVos.stream()
                .filter(v -> v.getSpecialDiseaseName().equals(DiseaseCommonConfig.HEART_NAME)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(choiceNameList)) {
            return true;
        }
        SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo choiceInfo = choiceNameList.get(0);
        //判断对应的选项是否为大于 7岁
        if (CollectionUtils.isEmpty(choiceInfo.getChoiceInfoList()) ||
                DiseaseCommonConfig.AGE_MORE_THAN_RANGE_CHOICE_NAME.equals(choiceInfo.getChoiceInfoList().get(0).getChoiceName())) {
            return true;
        }
        return false;
    }

}
