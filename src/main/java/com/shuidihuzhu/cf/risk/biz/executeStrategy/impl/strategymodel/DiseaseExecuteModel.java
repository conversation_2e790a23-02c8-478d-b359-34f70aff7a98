package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
public class DiseaseExecuteModel extends ExecuteStrategyModelTemplate {

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList())) {
            //写入识别结果
            diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                    resultInfo(getResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum())));
            transit.setHaNext(false);
        }
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        switch (diseaseStrategyEnum) {
            case USER_WRITE:
                return DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.WRITE_DISEASE_NULL;
            case MANUAL_WRITE:
            default:
                return null;
        }
    }


    @Override
    public String getCode() {
        return "disease_judge_size";
    }

    @Override
    public boolean preDecide() {
        return true;
    }
}
