package com.shuidihuzhu.cf.risk.biz;

import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.cf.risk.model.risk.HouseThresholdParam;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoPrePoseModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HighRiskJudgeV2Service {
    Response<HighRiskJudgeResult> judge(HighRiskJudgeInfoModel infoModel);

    Response<List<HighRiskRecordDto>> getLastByCaseId(int caseId);

    Response<HighRiskJudgeResult> judgePrePose(HighRiskJudgeInfoPrePoseModel infoModel);

    Response<Integer> getMResult(String patientIdCard, String raiserIdCard);

    Response<Integer> getMResultByParam(HouseThresholdParam houseThresholdParam);

    Response<Integer> getHouseThreshold(String patientIdCard, String raiserIdCard);

}
