package com.shuidihuzhu.cf.risk.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.*;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.AuthenticityCarInfo;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.CommercialCar;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.NonCommercialCar;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.*;
import com.shuidihuzhu.cf.client.material.model.authenticity.house.*;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskAutoEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDO;
import com.shuidihuzhu.cf.risk.model.highrisk.RiskRuleResult;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.risk.service.RiskStrategyService;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CardMsgVO;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class HighRiskV3Strategy {

    private Map<String, RiskRuleResult> ruleCodeResultMap;

    @Resource
    private InitialAuditWorkOrderFeignClient initialAuditWorkOrderFeignClient;

    @Resource
    private RiskStrategyService riskStrategyService;

    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    @Value("${apollo.high-risk.config-json-v2:{}}")
    public void setConfigJson(String configJson) {
        log.info("HighRiskV3Strategy configJson v2 :{}", configJson);
        HighRiskJudgeV2ServiceImpl.Config config = JSON.parseObject(configJson, HighRiskJudgeV2ServiceImpl.Config.class);
        ruleCodeResultMap = config.getRuleResultList().stream().collect(Collectors.toMap(RiskRuleResult::getRuleCode, Function.identity()));
    }

    public HighRiskJudgeResult exec(JudgeContext judgeContext) {
        final Set<String> hitCodes = promoteRule(judgeContext);
        log.info("HighRiskV3Strategy exec context: {}, hitCodes:{}", judgeContext, hitCodes);
        return promoteResult(hitCodes, judgeContext.getRequestParam());
    }

    public HighRiskJudgeResult promoteResult(String hitCodesStr, HighRiskJudgeInfoModel reqInfo) {
        if (StringUtils.isEmpty(hitCodesStr)) {
            return promoteResult(Sets.newLinkedHashSet(), reqInfo);
        }
        Set<String> hitCodes = Sets.newLinkedHashSet(Splitter.on(",").splitToList(hitCodesStr));
        return promoteResult(hitCodes, reqInfo);
    }


    public HighRiskJudgeResult promoteResult(HighRiskRecordDO record) {
        final String reqParam = record.getReqParam();
        final HighRiskJudgeInfoModel reqInfo = JSON.parseObject(reqParam, HighRiskJudgeInfoModel.class);
        return promoteResult(record.getHitCodes(), reqInfo);
    }

    public HighRiskJudgeResult promoteResult(Set<String> hitCodes, HighRiskJudgeInfoModel reqInfo) {
        HighRiskJudgeResult result = new HighRiskJudgeResult();
        result.setRiskLabels(Lists.newArrayList());
        final ArrayList<String> riskTips = Lists.newArrayList();
        result.setHitCodes(hitCodes);
        result.setRiskTips(riskTips);

        Set<String> validHitCodes = Sets.newHashSet();

        int extAction = -1;
        Set<String> daiLuRuSet = Sets.newHashSet();
        List<RiskRuleResult> resultList = Lists.newArrayList();
        Map<String, Set<String>> processedTipRuleCodeMap = Maps.newHashMap();
        for (String hitCode : hitCodes) {
            RiskRuleResult riskRuleResult = ruleCodeResultMap.get(hitCode);
            if (riskRuleResult == null) {
                log.info("此策略已删除 code {}", hitCode);
                continue;
            }
            resultList.add(riskRuleResult);
            validHitCodes.add(hitCode);
            result.getRiskLabels().addAll(riskRuleResult.getRedFieldCodes());
            final String e = "\"" + riskRuleResult.getRiskPoint() + "\"，请按照\"" + riskRuleResult.getAuditMethod()
                    + "\"进行判断；";
            final Set<String> ruleCodeList = processedTipRuleCodeMap.computeIfAbsent(e, v -> Sets.newHashSet());
            ruleCodeList.add(riskRuleResult.getRuleCode());

            daiLuRuSet.add(riskRuleResult.getDaiLuRu());
            if (riskRuleResult.getExtAction() > extAction) {
                extAction = riskRuleResult.getExtAction();
            }
        }

        // eg 1、{“审核提示信息-风险点”1}，请按照{“审核提示信息-审核方式”1}进行判断（策略编码1、策略编码2）；
        final List<String> tipList = processedTipRuleCodeMap.entrySet()
                .stream()
                .map(v -> {
                    final String e = v.getKey();
                    final Set<String> ruleCodeList = v.getValue();
                    return e + "(" + StringUtils.join(ruleCodeList, ",") + ")";
                })
                .collect(Collectors.toList());
        result.setRiskTips(tipList);

        // 无风险
        if (CollectionUtils.isEmpty(validHitCodes)) {
            result.setHighRisk(false);
            result.getRiskTips().add("家庭经济情况初步系统预判较合理");
            return result;
        }

        // 有风险
        result.setHighRisk(true);

        // 提示文案去重
        result.setRiskTips(result.getRiskTips().stream().distinct().collect(Collectors.toList()));
        result.setRiskLabels(result.getRiskLabels().stream().distinct().collect(Collectors.toList()));

        // 患者【房产】、【车产】、【金融资产】等需进一步核实情况
        if (CollectionUtils.isNotEmpty(daiLuRuSet)) {
            final String daiLuRuMsg = daiLuRuSet.stream()
                    .limit(5)
                    .collect(Collectors.joining("、"));
            result.setTotalRiskTip("患者" + daiLuRuMsg + "等需进一步核实情况");
        }
        final String houseTipInfo = reqInfo.getHouseTipInfo();
        if (StringUtils.isNotEmpty(houseTipInfo)) {
            result.getRiskTips().add(houseTipInfo);
        }

        return result;
    }

    public Set<String> promoteRule(JudgeContext judgeContext) {
        final HighRiskJudgeInfoModel infoModel = judgeContext.getRequestParam();
        HashSet<String> hitCodes = Sets.newHashSet();
        boolean auto = infoModel.getSource() != HighRiskJudgeConst.Source.MANUAL;

        // 计算自建房阈值
        processSelfCityThreshold(judgeContext);

        Integer targetAmountInYuan = judgeContext.getTargetAmountInYuan();

        // 真实性数据
        final AuthenticityIndicator authenticityIndicator = infoModel.getAuthenticityIndicator();

        // 是否有真实性信息
        final boolean isAuthentic = authenticityIndicator != null;
        if (!isAuthentic) {
            log.error("没有真实性信息");
            return hitCodes;
        }

        // 上下文计算
        processContext(judgeContext, authenticityIndicator, infoModel);

        // 【有人身险】   and  目标金额大于20w
        final InsuranceSituation insuranceSituation = authenticityIndicator.getInsuranceSituation();
        Boolean personalInsurance = null;
        if (insuranceSituation != null) {
            if (insuranceSituation.getPersonalInsurance() != null) {
                personalInsurance = insuranceSituation.getPersonalInsurance() == 1;
                if (personalInsurance && targetAmountInYuan > 20_0000) {
                    if (auto) {
                        hit(hitCodes, HighRiskAutoEnum.HRA012.getRuleCode());
                    }
                }
            }
        }

        // 是否普通案例 = 否
        Boolean normalCase = infoModel.getNormalCase();
        if (normalCase != null) {
            if (!normalCase) {
                if (auto) {
                    hit(hitCodes, HighRiskAutoEnum.HRA013.getRuleCode());
                }
            }
        }

        //身份敏感
        //患者身份为【现役军人(包括直系亲属)】AND 文章内容不含‘退伍’AND 文章内容不含‘退役’
        if (Objects.nonNull(infoModel.getPatientIdentity()) &&
                infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.ACTIVE_DUTY_SOLDIER.getCode() &&
                !judgeContext.getContent().contains("退伍") &&
                !judgeContext.getContent().contains("退役")) {
            if (auto) {
                infoModel.setPatientIdentityHit(true);
                hit(hitCodes, "HRA015");
            }
        }

        //患者身份为【县级及以上政府官员】、【媒体圈相关人员】、【涉及媒体或政府关注】
        if (Objects.nonNull(infoModel.getPatientIdentity()) &&
                (infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.GOVERNMENT_OFFICER.getCode() ||
                        infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.MEDIA_ABOUT.getCode() ||
                        infoModel.getPatientIdentity() == PreposeMaterialModel.PatientIdentity.MEDIA_GOVERNMENT_NOTICE.getCode()
                )) {
            if (auto) {
                infoModel.setPatientIdentityHit(true);
                hit(hitCodes, "HRA016");
            }
        }

        final int yearIncome = intDef(authenticityIndicator.getPatientFinancialCondition().getFamilyIncome());
//        final Integer yearIncome = infoModel.getYearIncome();
        final Integer caseId = infoModel.getCaseId();
        if (caseId != null && caseId > 0) {
            PatientToVolunteerParam toVolunteerParam = PatientToVolunteerParam.builder()
                    .caseId(caseId)
                    .useType(PatientToVolunteerConst.Source.INITIAL_AUDIT)
                    .yearAmount(Objects.requireNonNullElse(yearIncome, 0))
                    .build();
            OperationResult<Integer> patientToVolunteerResult = initialAuditWorkOrderFeignClient.patientToVolunteer(toVolunteerParam);
            Integer integer = Optional.ofNullable(patientToVolunteerResult)
                    .map(OperationResult::getData)
                    .orElse(0);
            if (Objects.equals(integer, VolunteerLevelEnum.PARTNER_COMMON.getLevel())
                    || Objects.equals(integer, VolunteerLevelEnum.PARTNER_AID.getLevel())) {
                hit(hitCodes, "HRA017");
            }
            if ((Objects.equals(integer, VolunteerLevelEnum.COMMON_LEADER.getLevel()) ||
                    Objects.equals(integer, VolunteerLevelEnum.PROVINCE_LEADER.getLevel()) ||
                    Objects.equals(integer, VolunteerLevelEnum.PARTNER_MANAGER.getLevel())) && yearIncome > 30000) {
                hit(hitCodes, "HRA018");
            }
            if ((Objects.equals(integer, VolunteerLevelEnum.COMMON_LEADER.getLevel()) ||
                    Objects.equals(integer, VolunteerLevelEnum.PROVINCE_LEADER.getLevel()) ||
                    Objects.equals(integer, VolunteerLevelEnum.PARTNER_MANAGER.getLevel())) && yearIncome <= 30000) {
                hit(hitCodes, "HRA019");
            }
        }

        // 策略计算 -----------------------------------
        /*
        "【核心家庭房产】房产总量" =2
And
"【核心家庭房产】【多套】是否有任意一套房产净值大于等于房产阈值
And
疾病花费大于等于(该房产净值-该房产阈值*30%）"==true
         */
        final boolean isCoreHouseTotalEquals2 = judgeContext.getCoreHouseTotal() == 2;
        final boolean isJiBingHuaFeiOverThreshold = judgeContext.isCoreHouse_hasAnyOneOverThresholdX();
        if (isCoreHouseTotalEquals2 && isJiBingHuaFeiOverThreshold) {
            hit(hitCodes, "HRA020");
        }

        /*
        "【核心家庭房产】房产总量" >=3
         */
        if (judgeContext.getCoreHouseTotal() >= 3) {
            hit(hitCodes, "HRA021");
        }
//
//        /*
//        "【核心家庭车产】营生车数量 ">= 2
//         */
//        if (judgeContext.getCoreCar_business_totalCount() >= 2) {
//            hit(hitCodes, "HRA022");
//        }

        /*
        "【核心家庭车产】【多辆】是否有任一非营生车品牌为名牌车"==true
AND
"【核心家庭车产】非营生车产总价值" >= 25
         */
        if (judgeContext.isCoreCar_nonBusiness_hasAnyOneBrandIsFamous() && judgeContext.getCoreCar_nonBusiness_totalValue() >= 25_0000) {
            hit(hitCodes, "HRA023");
        }

//        /*
//        HRA024
//"【核心家庭车产】非营生车总数量">= 3
//         */
//        if (judgeContext.getCoreCar_nonBusiness_totalCount() >= 3) {
//            hit(hitCodes, "HRA024");
//        }

        /*
        HRA025
"【核心家庭负债】负债" >= 100
         */
        if (judgeContext.getCoreDebt_total() >= 100_0000) {
            hit(hitCodes, "HRA025");
        }

        /*
        HRA026
"【核心家庭房产】房产总量" =1
And
"【通用】疾病花费" <=任意 "【核心家庭房产】【单套】房产净值"
And
 任意"【核心家庭房产】【单套】房产净值">= "【通用】房产阈值"
         */
        if (judgeContext.getCoreHouseTotal() == 1
                && judgeContext.isCoreHouse_hasAnyOneOverTargetAmount()
                && judgeContext.isCoreHouse_hasAnyOneOverThreshold()){
            hit(hitCodes, "HRA026");
        }

        /*
        HRA027
"【核心家庭房产】房产总量" =1
And
任意 "【核心家庭房产】【单套】房产净值"> "【通用】房产阈值"
And
"【通用】疾病花费"  >=（任意"【核心家庭房产】【单套】房产净值"- "【通用】房产阈值"*30%）
         */
        if (judgeContext.getCoreHouseTotal() == 1
                && judgeContext.isCoreHouse_hasAnyOneOverThreshold()
                && judgeContext.isCoreHouse_hasAnyOneOverThresholdX()) {
            hit(hitCodes, "HRA027");
        }

        /*
        HRA028
"【核心家庭房产】房产总量" =1
And
核心家庭房产净值略低于城市房产阈值
         */
        if (judgeContext.getCoreHouseTotal() == 1
                && judgeContext.isCoreHouse_hasAnyOneLowerThresholdSlightly()) {
            hit(hitCodes, "HRA028");
        }

        /*
        HRA029

"【核心家庭房产】房产总量" =2
And
"【核心家庭房产】【多套】是否有任意一套房产净值大于等于房产阈值"
==false
         */
        if (judgeContext.getCoreHouseTotal() == 2
                && !judgeContext.isCoreHouse_hasAnyOneOverThreshold()) {
            hit(hitCodes, "HRA029");
        }

        /*
        HRA030
"【核心家庭车产】非营生车总数量">=1
And
"【核心家庭车产】非营生车总数量"<=2
AND
"【核心家庭车产】非营生车总净值">="【通用】疾病花费"
         */
        if (judgeContext.getCoreCar_nonBusiness_totalCount() >= 1
                && judgeContext.getCoreCar_nonBusiness_totalCount() <= 2
                && judgeContext.getCoreCar_nonBusiness_totalValue() >= targetAmountInYuan) {
            hit(hitCodes, "HRA030");
        }

        /*
        HRA031
"【核心家庭车产】非营生车总数量">=1
And
"【核心家庭车产】非营生车总数量"<=2
AND
"【核心家庭车产】非营生车总净值">=15
         */
        if (judgeContext.getCoreCar_nonBusiness_totalCount() >= 1
                && judgeContext.getCoreCar_nonBusiness_totalCount() <= 2
                && judgeContext.getCoreCar_nonBusiness_totalValue() >= 15_0000) {
            hit(hitCodes, "HRA031");
        }

        /*
        HRA032
"【核心家庭车产】非营生车总数量">=1
And
"【核心家庭车产】非营生车总数量"<=2
AND
"【核心家庭车产】【多辆】是否有任意非营生车品牌为名牌车"==true
AND
"【核心家庭车产】非营生车产总价值"<25
         */
        if (judgeContext.getCoreCar_nonBusiness_totalCount() >= 1
                && judgeContext.getCoreCar_nonBusiness_totalCount() <= 2
                && judgeContext.isCoreCar_nonBusiness_hasAnyOneBrandIsFamous()
                && judgeContext.getCoreCar_nonBusiness_totalValue() < 25_0000) {
            hit(hitCodes, "HRA032");
        }

        /*
        HRA033
"【核心家庭年收入】年净收入 ">= "【通用】疾病花费"
         */
        if (judgeContext.getCoreIncomeYearNet() >= targetAmountInYuan) {
            hit(hitCodes, "HRA033");
        }

        /*
        HRA034
"【核心家庭年收入】年收入 ">= 15
         */
        if (judgeContext.getCoreIncomeYear() >= 15_0000) {
            hit(hitCodes, "HRA034");
        }

        /*
        HRA035
"【核心家庭人身险】患者本人是否有人身险"==true
         */
//        if (personalInsurance != null && personalInsurance){
//            hit(hitCodes, "HRA035");
//        }

        /*
        HRA036
"【核心家庭案例类型】案例类型"<>'普通'
         */

        /*
        HRA037
（"【核心家庭现金流】金融资产金额"+"【核心家庭现金流】其他渠道救助金额"+"【核心家庭现金流】事故已赔付金额"）>="【通用】疾病花费"
         */
        if (judgeContext.getFinancialAssetAmount()
                + judgeContext.getOtherChannelReliefAmount()
                + judgeContext.getAccidentCompensationAmountPaid() >= targetAmountInYuan) {
            hit(hitCodes, "HRA037");
        }

        /*
        HRA038
（"【核心家庭车产】非营生车总净值"+
"【核心家庭现金流】金融资产金额"+
"【核心家庭现金流】其他渠道救助金额"+
"【核心家庭现金流】事故已赔付金额"+
"【核心家庭房产】非刚需房产总净值"）
>= "【通用】疾病花费"
         */
        final int coreHouse_minHousingNetAsset = judgeContext.getCoreHouse_minHousingNetAsset();
        final int coreHouse_housingNetAssetsTotal = judgeContext.getCoreHouse_housingNetAssetsTotal();
        final int nonTotalNetValue = coreHouse_housingNetAssetsTotal - coreHouse_minHousingNetAsset;
        if (judgeContext.getCoreCar_nonBusiness_totalValue()
                + judgeContext.getFinancialAssetAmount()
                + judgeContext.getOtherChannelReliefAmount()
                + judgeContext.getAccidentCompensationAmountPaid()
                + nonTotalNetValue >= targetAmountInYuan) {
            hit(hitCodes, "HRA038");
        }

        /*
        HRA039
"【已婚子女房产】【多套】是否有任意一套房产价值大于等于房产阈值*1.5"==true
         */
        if (judgeContext.isMarriedChildren_hasAnyOneOverThresholdX1_5()) {
            hit(hitCodes, "HRA039");
        }

        /*
        HRA040
"【已婚子女房产】拥有2套及以上的已婚子女数量">0
         */
        if (judgeContext.getMarriedChildren_owningTwoOrMoreHousesCount() > 0) {
            hit(hitCodes, "HRA040");
        }

        /*
        HRA041
"【已婚子女房产】价值超过15万的非营生车产数量">0
         */
        if (judgeContext.getMarriedChildren_nonBusinessVehiclesOver150kValueCount() > 0) {
            hit(hitCodes, "HRA041");
        }

        /*
        HRA042
"【已婚子女房产】拥有2辆及以上车产的已婚子女数量">0
         */
        if (judgeContext.getMarriedChildren_owningTwoOrMoreVehiclesCount() > 0) {
            hit(hitCodes, "HRA042");
        }

        /*
        HRA043
"【有钱父母房产】【多套】是否有任意一套房产价值大于等于房产阈值*1.5"==true
         */
        if (judgeContext.isParent_hasAnyOneOverThresholdX1_5()) {
            hit(hitCodes, "HRA043");
        }

        /*
        HRA044
"【有钱父母房产】父母家庭房产数量">=2
         */
        if (judgeContext.getParent_familyHouseCount() >= 2) {
            hit(hitCodes, "HRA044");
        }

        /*
        HRA045
"【有钱父母房产】非营生车产价值">=15
---
“存在非营生车产价值 >= 15万元”
         */
        if (judgeContext.getParent_nonBusinessVehicleValue() >= 15_0000) {
            hit(hitCodes, "HRA045");
        }

        /*
        HRA046
"【有钱父母车产】非营生车数量">=2
         */
        if (judgeContext.getParent_nonBusinessVehicleCount() >= 2) {
            hit(hitCodes, "HRA046");
        }

        /*
        HRA047
        （"【核心家庭现金流】金融资产金额"+"【核心家庭现金流】其他渠道救助金额"+"【核心家庭现金流】事故已赔付金额"）>=15万元
         */
        if (judgeContext.getFinancialAssetAmount()
                + judgeContext.getOtherChannelReliefAmount()
                + judgeContext.getAccidentCompensationAmountPaid() >= (15_0000)) {
            hit(hitCodes, "HRA047");
        }

        /*
        HRA048
        【核心家庭车产】车产数量>=3
         */
        if (judgeContext.getCoreCar_totalCount() >= 3){
            hit(hitCodes, "HRA048");
        }

        return hitCodes;
    }

    private void processSelfCityThreshold(JudgeContext judgeContext) {
        final CaseCityInfoVO cityInfoVO = judgeContext.getCityInfoVO();
        String cityCode = getCodeByCityInfo(cityInfoVO);
        if (StringUtils.isBlank(cityCode)) {
            return;
        }
        final Response<CardMsgVO> cityResp = crowdfundingCityFeignClient.getCityByIdCard(cityCode);
        log.info("getCityByIdCard cityCode:{}, cityResp:{}", cityCode, cityResp);
        if (NewResponseUtil.isNotOk(cityResp)) {
            return;
        }
        final CardMsgVO location = cityResp.getData();
        final CityParam cityParam = new CityParam();
        cityParam.setProvince(location.getProvinceName());
        cityParam.setCity(location.getCity());
        cityParam.setCounty(location.getCounty());
        final Integer cityAmountThreshold = riskStrategyService.getCityAmonutThreshold(cityParam);
        judgeContext.setSelfHouseThreshold(cityAmountThreshold * 10000);
    }

    private String getCodeByCityInfo(CaseCityInfoVO cityInfoVO) {
        final String patientCityCode = cityInfoVO.getPatientCityCode();
        final String raiserCityCode = cityInfoVO.getRaiserCityCode();
        if (StringUtils.isBlank(patientCityCode)) {
            return raiserCityCode;
        } else {
            return patientCityCode;
        }
    }

    private void processContext(JudgeContext judgeContext, AuthenticityIndicator authenticityIndicator, HighRiskJudgeInfoModel infoModel) {

        // 核心家庭经济情况
        final PatientFinancialCondition coreHomeFinancial = authenticityIndicator.getPatientFinancialCondition();
        // 患者保险保障状况
        final InsuranceSituation insuranceSituation = authenticityIndicator.getInsuranceSituation();
        // 1. 患者家庭经济状况
        // 房产情况
        processPatientFinancialCondition(judgeContext, coreHomeFinancial);

        // 新版车产情况
        processCar(judgeContext, coreHomeFinancial);

        // 新版收入情况
        processIncome(judgeContext, coreHomeFinancial);

        judgeContext.setFinancialAssetAmount(intDef(coreHomeFinancial.getFinancialAssetsSum()));

        // 其他渠道救助金额
        judgeContext.setOtherChannelReliefAmount(str2Double(insuranceSituation.getOtherAssistanceAmount()));
        // 事故赔付金额
        judgeContext.setAccidentCompensationAmountPaid(intDef(infoModel.getPaidAmount()));

        // 子女经济情况
        final ChildFinancialCondition marriedChildFinancialCondition = authenticityIndicator.getMarriedChildFinancialCondition();
        judgeContext.setMarriedChildren_owningTwoOrMoreHousesCount(intDef(marriedChildFinancialCondition.getRichHouseOwnerCount()));
        final List<ChildFinancialCondition.RichHouseInfo> richHouseInfos = marriedChildFinancialCondition.getRichHouseInfos();
        int index = 0;
        for (ChildFinancialCondition.RichHouseInfo richHouseInfo : richHouseInfos) {
            final HouseLocation houseLocation = richHouseInfo.getHouseLocation();
            if (houseLocation == null) {
                log.warn("子女房产信息中房产位置为空，忽略该房产信息");
                continue;
            }
            final Integer cityAmountThreshold = getHouseThreshold(houseLocation);
            final int housePrice = intDef(richHouseInfo.getHousePrice());
            recordHouseInfo(judgeContext, "子女房产" + ++index, housePrice, cityAmountThreshold);
            // 是否有任意一套房产价值大于等于房产阈值*1.5
            if (housePrice >= cityAmountThreshold * 1.5) {
                judgeContext.setMarriedChildren_hasAnyOneOverThresholdX1_5(true);
            }
        }
        final List<ChildFinancialCondition.RichCarInfo> richCarInfos = marriedChildFinancialCondition.getRichCarInfos();
        // 价值超过15万的非营生车产数量
        if (CollectionUtils.isEmpty(richCarInfos)) {
            judgeContext.setMarriedChildren_nonBusinessVehiclesOver150kValueCount(intDef(marriedChildFinancialCondition.getRichCarCount()));
        } else {
            final long count = richCarInfos.stream()
                    .map(a -> Optional.ofNullable(a)
                            .map(ChildFinancialCondition.RichCarInfo::getCommercialCarTag)
                            .orElse(null)
                    )
                    .filter(Objects::nonNull)
                    .filter(tag -> tag == 0)
                    .count();
            judgeContext.setMarriedChildren_nonBusinessVehiclesOver150kValueCount(Math.toIntExact(count));
        }
        // 已婚子女-拥有2辆及以上车产的已婚子女数量
        judgeContext.setMarriedChildren_owningTwoOrMoreVehiclesCount(intDef(marriedChildFinancialCondition.getRichCarOwnerCount()));

        // 有钱父母
        // 房产
        final ParentFinancialCondition richParentFinancialCondition = authenticityIndicator.getRichParentFinancialCondition();
        final List<ParentFinancialCondition.ParentHouseDetail> parentHouseDetails = richParentFinancialCondition.getParentHouseDetails();
        judgeContext.setParent_familyHouseCount(intDef(richParentFinancialCondition.getParentHouseCount()));
        for (ParentFinancialCondition.ParentHouseDetail parentHouseDetail : parentHouseDetails) {
            HouseLocation houseLocation = parentHouseDetail.getHouseLocation();
            int cityAmountThreshold;
            if(houseLocation == null){
                // HRA043策略调整，当房产位置为空时，房产阈值取50万
                cityAmountThreshold = 500000;
            }else{
                cityAmountThreshold = getHouseThreshold(houseLocation);
            }
            // 是否有任意一套房产价值大于等于房产阈值*1.5
            if (intDef(parentHouseDetail.getHousePrice()) >= cityAmountThreshold * 1.5) {
                judgeContext.setParent_hasAnyOneOverThresholdX1_5(true);
            }
        }

        // 车产
        final List<ParentFinancialCondition.ParentCarDetail> parentCarDetails = richParentFinancialCondition.getParentCarDetails();
        final int parentCarCount = intDef(richParentFinancialCondition.getParentCarCount());
        if (CollectionUtils.isEmpty(parentCarDetails)){
            // 无明细
            if (parentCarCount > 0) {
                judgeContext.setParent_nonBusinessVehicleValue(
                        intDef(richParentFinancialCondition.getParentCarPriceSum())
                                / parentCarCount
                );
            }
        } else {
            // 有明细
            final List<ParentFinancialCondition.ParentCarDetail> nonBusinessCars = parentCarDetails
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(v -> v.getCommercialCarTag() != null && v.getCommercialCarTag() == 0)
                    .collect(Collectors.toList());
            judgeContext.setParent_nonBusinessVehicleCount(CollectionUtils.size(nonBusinessCars));
            // 非营生车产总价值
            for(ParentFinancialCondition.ParentCarDetail nonBusinessCar : nonBusinessCars){
                if(nonBusinessCar.getCommercialCarTag() == null || nonBusinessCar.getCommercialCarTag() != 0) {
                    continue;
                }
                int carPriceRange = nonBusinessCar.getCarPriceRange();
                if(carPriceRange == CarRangeEnum.AMOUNT_AREA_0_5.getCode()){
                    nonBusinessCar.setCarPrice(CarRangeEnum.AMOUNT_AREA_0_5.getTo());
                }
            }
            final int parent_nonBusinessVehicleValue = nonBusinessCars.stream()
                    .mapToInt(v -> Optional.ofNullable(v).map(ParentFinancialCondition.ParentCarDetail::getCarPrice)
                            .orElse(0)
                    )
                    .sum();
            judgeContext.setParent_nonBusinessVehicleValue(parent_nonBusinessVehicleValue);
        }
    }

    /**
     * 新版核心家庭收入情况
     */
    public void processIncome(JudgeContext judgeContext, PatientFinancialCondition coreHomeFinancial){
        int familyIncome = intDef(coreHomeFinancial.getFamilyIncome());
        int familyCost = intDef(coreHomeFinancial.getFamilyCost());
        // 家庭收入区间
        Integer familyIncomeRange = coreHomeFinancial.getFamilyIncomeRange();
        // 家庭支出区间
        Integer familyCostRange = coreHomeFinancial.getFamilyCostRange();
        // - 收入、开销均为“5万元以内”区间值时，都取区间上限进行计算
        if(familyIncomeRange == HouseIncomeEnum.AMOUNT_AREA_0_5.getCode()){
            familyIncome = HouseIncomeEnum.AMOUNT_AREA_0_5.getTo();
        }
        if(familyCostRange == HouseIncomeEnum.AMOUNT_AREA_0_5.getCode()){
            familyCost = HouseIncomeEnum.AMOUNT_AREA_0_5.getTo();
        }
        judgeContext.setCoreIncomeYear(familyIncome);
        judgeContext.setFamilyCost(familyCost);
        judgeContext.setCoreIncomeYearNet(familyIncome - familyCost);

        // 家庭欠款
        int familyDebt = intDef(coreHomeFinancial.getDebtSum());
        Integer familyDebtRange = coreHomeFinancial.getDebtSumRange();
        if(familyDebtRange == null){
            return;
        }
        if(familyDebtRange == HouseDebtEnum.AMOUNT_AREA_0_5.getCode()){
            familyDebt = HouseDebtEnum.AMOUNT_AREA_0_5.getTo();
        }
        if(familyDebtRange == HouseDebtEnum.AMOUNT_AREA_5_10.getCode()){
            familyDebt = HouseDebtEnum.AMOUNT_AREA_5_10.getTo();
        }
        judgeContext.setCoreDebt_total(familyDebt);
    }

    /**
     * 新版核心家庭车产情况
     */
    private void processCar(JudgeContext judgeContext, PatientFinancialCondition coreHomeFinancial) {
        AuthenticityCarInfo authenticityCarInfo = coreHomeFinancial.getAuthenticityCarInfo();
        if(authenticityCarInfo == null || authenticityCarInfo.getCarCount() == null || authenticityCarInfo.getCarCount() <= 0){
            return;
        }
        // 新版增信存在只有数量没有明细的情况
        judgeContext.setCoreCar_totalCount(authenticityCarInfo.getCarCount());

        List<AuthenticityCarInfo.CarDetail> carDetails = authenticityCarInfo.getCarDetails();
        if(CollectionUtils.isEmpty(carDetails)){
            return;
        }
        for(AuthenticityCarInfo.CarDetail carDetail : carDetails){
            if(carDetail == null){
                continue;
            }
            if(carDetail.getIsCommercialVehicle() == null){
                continue;
            }
            // 营生车
            if(carDetail.getIsCommercialVehicle() == 1){
                judgeContext.setCoreCar_business_totalCount(judgeContext.getCoreCar_business_totalCount() + 1);
                continue;
            }
            // 非营生车
            //     - 15万以内非营生车车产品牌为空，HRA023和HRA032策略计算时，视为非名牌（兜底逻辑）
            //    - 15万以上非营生车产品牌字段为必填，不会为空，策略无影响
            judgeContext.setCoreCar_nonBusiness_hasAnyOneBrandIsFamous(isFamousCar(carDetail.getCarBrand()));

            judgeContext.setCoreCar_nonBusiness_totalCount(judgeContext.getCoreCar_nonBusiness_totalCount() + 1);
            if(carDetail.getCarMarketValueRange() == null){
                continue;
            }
            // 价值小于等于5万元的非营生车产不计算净值，价值按5万元计算
            if(carDetail.getCarMarketValueRange() == CarRangeEnum.AMOUNT_AREA_0_5.getCode()){
                judgeContext.setCoreCar_nonBusiness_totalValue(judgeContext.getCoreCar_nonBusiness_totalValue() + 50000);
            }

            int carMarketValue = intDef(carDetail.getCarMarketValue());
            judgeContext.setCoreCar_nonBusiness_totalValue(judgeContext.getCoreCar_nonBusiness_totalValue() + carMarketValue);
            // 车产净值计算
            //  - 价值小于等于5万元的非营生车产不计算车产净值
            //  - 车产贷款可能为空。为空时，非营生车产净值按照无贷款0来进行计算
            if(carMarketValue <= 50000){
                continue;
            }
            int carLoan = intDef(carDetail.getLoanAmount());
            judgeContext.setCoreCar_nonBusiness_totalNetValue(judgeContext.getCoreCar_nonBusiness_totalNetValue() + (carMarketValue - carLoan));
        }
    }

    private boolean isFamousCar(String carBrand) {
        if (StringUtils.isBlank(carBrand)) {
            return false;
        }
        final Boolean famousCar = riskStrategyService.isFamousCar(carBrand);
        if (famousCar == null) {
            return false;
        }
        return famousCar;
    }

    private int intDef(Integer target) {
        return intDef(target, 0);
    }

    private int intDef(Integer target, int def) {
        return target == null ? def : target;
    }

    private void processPatientFinancialCondition(JudgeContext judgeContext,
                                                  PatientFinancialCondition patientFinancialCondition) {
        if (patientFinancialCondition == null) {
            return;
        }

        // 自建房信息
        final SelfBuiltHouseInfo selfBuiltHouseInfo = patientFinancialCondition.getSelfBuiltHouseInfo();
        processSelfBuiltHouseInfo(judgeContext, selfBuiltHouseInfo);

        // 商品房信息
        final CommodityHouseInfo commodityHouseInfo = patientFinancialCondition.getCommodityHouseInfo();
        processCommodityHouseInfo(judgeContext, commodityHouseInfo);

        // 其他房产信息
        final OtherHouseInfo otherHouseInfo = patientFinancialCondition.getOtherHouseInfo();
        processOtherHouseInfo(judgeContext, otherHouseInfo);

    }

    /**
     * 其他房产
     */
    private void processOtherHouseInfo(JudgeContext judgeContext, OtherHouseInfo otherHouseInfo) {
        if (otherHouseInfo == null) {
            return;
        }
        // 累加其他房产数量
        final int houseCount = intDef(otherHouseInfo.getHouseCount());
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);
        final List<OtherHouseInfo.OtherHouseDetail> otherHouseDetails = otherHouseInfo.getOtherHouseDetails();
        if (CollectionUtils.isEmpty(otherHouseDetails)) {
            if (houseCount > 0) {
                final int houseNetValue = intDef(otherHouseInfo.getHouseValueSum()) / houseCount;
                IntStream.range(1, houseCount).forEach(index -> {
                    processCoreHouse(judgeContext, houseNetValue, null, "其他房产", index);
                });
            }
            return;
        }
        // 有明细
        int index = 0;
        for (OtherHouseInfo.OtherHouseDetail otherHouseDetail : otherHouseDetails) {
            if (otherHouseDetail == null) {
                continue;
            }
            final int ownershipRatioType = intDef(otherHouseDetail.getOwnershipRatioType());
            double ownershipRatio = str2Double(otherHouseDetail.getOwnershipRatio());
            /*
                    - 其他房产产权比例
          - 全部：按100%算
          - 部分：按输入值算
          - 不清楚占比：按100%算
             */
            if (ownershipRatioType == OwnershipEnum.ALL.getCode() ||
                    ownershipRatioType == OwnershipEnum.UNCLEAR.getCode()) {
                ownershipRatio = 100D;
            }
            final String tag = "其他房产";
            processCoreHouse(judgeContext, otherHouseDetail, ownershipRatio, tag, ++index);
        }
    }

    /**
     * 商品房
     */
    private void processCommodityHouseInfo(JudgeContext judgeContext, CommodityHouseInfo commodityHouseInfo) {
        if (commodityHouseInfo == null) {
            return;
        }
        // 累加商品房数量
        final int houseCount = intDef(commodityHouseInfo.getHouseCount());
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);

        final List<CommodityHouseInfo.CommodityHouseDetail> commodityHouseDetails =
                commodityHouseInfo.getCommodityHouseInfos();
        if (CollectionUtils.isEmpty(commodityHouseDetails)) {
            if (houseCount > 0) {
                final int houseNetValue = intDef(commodityHouseInfo.getHouseValueSum()) / houseCount;
                IntStream.rangeClosed(1, houseCount).forEach(index -> {
                    processCoreHouse(judgeContext, houseNetValue, null, "商品房", index);
                });
            }
            return;
        }
        int index = 0;
        for (CommodityHouseInfo.CommodityHouseDetail commodityHouseDetail : commodityHouseDetails) {
            if (commodityHouseDetail == null) {
                continue;
            }
            processCoreHouse(judgeContext, commodityHouseDetail, 100D,
                    "商品房", ++index);
        }
    }

    /**
     * 自建房
     */
    private void processSelfBuiltHouseInfo(JudgeContext judgeContext, SelfBuiltHouseInfo selfBuiltHouseInfo) {
        final Integer houseCount = intDef(selfBuiltHouseInfo.getHouseCount());
        // 累加自建房数量
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);
        final List<SelfBuiltHouseInfo.SelfBuiltHouseDetail> selfBuiltHouseDetails =
                selfBuiltHouseInfo.getSelfBuiltHouseDetails();
        if (CollectionUtils.isEmpty(selfBuiltHouseDetails)) {
            if (houseCount > 0) {
                final int houseNetValue = intDef(selfBuiltHouseInfo.getHouseValueSum()) / houseCount;
                final Integer cityAmountThreshold = judgeContext.getSelfHouseThreshold();
                IntStream.rangeClosed(1, houseCount)
                        .forEach(index -> {
                            processCoreHouse(judgeContext, houseNetValue, cityAmountThreshold, "自建房", index);
                        });
            }
            return;
        }

        int index = 0;
        for (SelfBuiltHouseInfo.SelfBuiltHouseDetail selfBuiltHouseDetail : selfBuiltHouseDetails) {

            if (selfBuiltHouseDetail == null) {
                continue;
            }
            int houseNetValue = getHouseValue(selfBuiltHouseDetail);
            if (!checkNumNotNullNotZero(houseNetValue)) {
                continue;
            }
            final Integer cityAmountThreshold = judgeContext.getSelfHouseThreshold();

            processCoreHouse(judgeContext, houseNetValue, cityAmountThreshold, "自建房", ++index);
        }
    }
    // 获取自建房房产价值
    private int getHouseValue(SelfBuiltHouseInfo.SelfBuiltHouseDetail selfBuiltHouseDetail) {
        Integer value = selfBuiltHouseDetail.getHouseMarketValueRange();
        if(value == null || value == SelfHouseRangeEnum.AMOUNT_AREA_15.getCode()){
            return selfBuiltHouseDetail.getHouseMarketValue();
        }
        if (value == SelfHouseRangeEnum.AMOUNT_AREA_0_10.getCode()) {
            return SelfHouseRangeEnum.AMOUNT_AREA_0_10.getTo();
        }
        if (value == SelfHouseRangeEnum.AMOUNT_AREA_10_15.getCode()) {
            return SelfHouseRangeEnum.AMOUNT_AREA_10_15.getTo();
        }
        return selfBuiltHouseDetail.getHouseMarketValue();
    }

    private void processCoreHouse(JudgeContext judgeContext,
                                  GeneralHouseDetail otherHouseDetail,
                                  double ownershipRatio,
                                  String tag,
                                  int index) {
        // - 当“市场价值”为空时，不参与计算，该套房产的房产净值为空；
        if(otherHouseDetail.getHouseValue() == null){
            return;
        }

        final int houseValue = intDef(otherHouseDetail.getHouseValue());
        final HouseLoanDetail houseLoanDetail = otherHouseDetail.getHouseLoanDetail();

        // 兜底
        if (ownershipRatio == 0) {
            ownershipRatio = 100D;
        }

        // 当“是否有房贷”为“无”，首付金额、贷款年限、已还贷年限、每月还款金额为空时，房贷计为0
        if(houseLoanDetail == null || houseLoanDetail.getHouseLoanTag() == null || houseLoanDetail.getHouseLoanTag() == 0){
            houseLoanDetail.setLoanYear("0");
            houseLoanDetail.setLoanPaidYear("0");
            houseLoanDetail.setLoanMonthRepayment("0");
            houseLoanDetail.setDownPaymentAmount(0);
        }

        // 根据贷款年限获取贷款年限系数
        final double loanYear = str2Double(houseLoanDetail.getLoanYear());
        // 计算房产净值 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
        final double loanPaidYear = str2Double(houseLoanDetail.getLoanPaidYear());

        final double loanMonthRepayment = str2Double(houseLoanDetail.getLoanMonthRepayment());
        final int houseNetValue = countHouseNetValue(houseValue, loanMonthRepayment,
                ownershipRatio, loanYear, loanPaidYear);

        final Integer cityAmountThreshold = getHouseThreshold(otherHouseDetail.getHouseLocation());

        processCoreHouse(judgeContext, houseNetValue, cityAmountThreshold, tag, index);
    }

    private void processCoreHouse(JudgeContext judgeContext,
                                  int houseNetValue,
                                  Integer cityAmountThreshold,
                                  String tag,
                                  int index) {
        recordHouseInfo(judgeContext, tag + index, houseNetValue, cityAmountThreshold);

        // 房产净值最低值
        int minHouseNetValue = judgeContext.getCoreHouse_minHousingNetAsset();
        if (minHouseNetValue == 0 || houseNetValue < minHouseNetValue) {
            minHouseNetValue = houseNetValue;
            judgeContext.setCoreHouse_minHousingNetAsset(minHouseNetValue);
        }
        // 房产总净值
        judgeContext.setCoreHouse_housingNetAssetsTotal(judgeContext.getCoreHouse_housingNetAssetsTotal() + houseNetValue);

        if (houseNetValue >= judgeContext.getTargetAmountInYuan()) {
            // 是否有房产净值覆盖目标金额
            judgeContext.setCoreHouse_hasAnyOneOverTargetAmount(true);
        }
        if (cityAmountThreshold != null && isHouseNetValueLowerThresholdSlightly(houseNetValue, cityAmountThreshold)) {
            judgeContext.setCoreHouse_hasAnyOneLowerThresholdSlightly(true);
        }
        if (cityAmountThreshold != null && houseNetValue >= cityAmountThreshold) {
            judgeContext.setCoreHouse_hasAnyOneOverThreshold(true);
            // 是否有任意一套房产净值大于等于房产阈值 且 疾病花费大于等于(该房产净值-该房产阈值*30%）
            if (judgeContext.getTargetAmountInYuan() >= (houseNetValue - cityAmountThreshold * 0.3)) {
                judgeContext.setCoreHouse_hasAnyOneOverThresholdX(true);
            }
        }
    }

    private double str2Double(String doubleStr) {
        if (StringUtils.isBlank(doubleStr)) {
            return 0;
        }
        return Double.parseDouble(doubleStr);
    }

    private boolean isHouseNetValueLowerThresholdSlightly(int houseNetValue, Integer cityAmountThreshold) {
        // 房产阈值"*90% 和 房产阈值"-20万元 中取小值
        int lowerSlightlyCityAmountThreshold = Math.min((int) (cityAmountThreshold * 0.9), cityAmountThreshold - 20_0000);
        // 核心家庭房产净值略大于等于城市房产阈值
        return (houseNetValue >= lowerSlightlyCityAmountThreshold) && (houseNetValue < cityAmountThreshold);
    }

    private void recordHouseInfo(JudgeContext judgeContext, String tag, int houseNetValue, Integer cityAmountThreshold) {
//        if (!(checkNumNotNullNotZero(houseNetValue) && checkNumNotNullNotZero(cityAmountThreshold))) {
//            return;
//        }
        final HighRiskJudgeInfoModel requestParam = judgeContext.getRequestParam();
        if (requestParam.getHouseTipInfo() == null) {
            requestParam.setHouseTipInfo("其中，");
        }
        final String source = requestParam.getSource() == HighRiskJudgeConst.Source.CREDIT ? "发起信息" : "代录入信息";
        //         自建房1房产阈值为X，房产净值为X，自建房2房产阈值为X，房产净值为X；
        String houseTipInfo = requestParam.getHouseTipInfo();
        houseTipInfo += "【" + source + "】 \n";
        houseTipInfo += ("\n" + tag + "房产阈值为" + (cityAmountThreshold == null ? "无" : formatWanYuan(cityAmountThreshold)));
        houseTipInfo +=  "，房产净值为" + formatWanYuan(houseNetValue) + "；";
        requestParam.setHouseTipInfo(houseTipInfo);
    }

    /**
     * 保留2位小数 并去除小数点后尾部的0
     * @param yuan
     * @return
     */
    private String formatWanYuan(Integer yuan) {
        if (yuan == null) {
            return "";
        }
        return new DecimalFormat("#.##万元").format(yuan / 10000D);
    }

    private Integer getHouseThreshold(HouseLocation houseLocation) {
        final CityParam cityParam = new CityParam();
        cityParam.setProvince(houseLocation.getProvince());
        cityParam.setCity(houseLocation.getCity());
        cityParam.setCounty(houseLocation.getDistrict());
        return riskStrategyService.getCityAmonutThreshold(cityParam) * 10000;
    }

    private boolean checkNumNotNullNotZero(Integer houseAmountInYuan) {
        return houseAmountInYuan != null && houseAmountInYuan != 0;
    }

    private void hit(HashSet<String> hitCodes, String code) {
        hitCodes.add(code);
    }

    /**
     * 计算房产净值 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
     * @param houseValue 市场价值
     * @param loanMonthRepayment 月供
     * @param ownershipRatio 产权比例
     * @param loanYear 贷款年限
     * @param loanPaidYear 已还款年限
     * @return 房产净值 单位元
     */
    private int countHouseNetValue(int houseValue,
                                   double loanMonthRepayment,
                                   double ownershipRatio,
                                   double loanYear,
                                   double loanPaidYear) {
        double loanRatio = getLoanRatio(loanYear);
        return (int) (houseValue * ownershipRatio / 100 -
                (loanMonthRepayment * 12 * (loanYear - loanPaidYear) * loanRatio));
    }

    /**
     * 贷款年限系数
     * @param loanYear
     * @return
     */
    private double getLoanRatio(Double loanYear) {
        if (loanYear == null) {
            return 0;
        }
        if (loanYear > 0 && loanYear <= 6) {
            return 0.9;
        }
        if (loanYear > 6 && loanYear <= 14) {
            return 0.8;
        }
        if (loanYear > 14 && loanYear <= 21) {
            return 0.7;
        }
        if (loanYear > 21 && loanYear <= 30) {
            return 0.6;
        }
        return 0;
    }
}

