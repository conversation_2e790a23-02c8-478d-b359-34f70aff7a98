package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.dao.SpecialDiseaseRuleDao;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseRuleTypeEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.PfLocalMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
@Service
@Slf4j
public class SpecialDiseaseRuleBizImpl implements SpecialDiseaseRuleBiz {

    @Autowired
    private SpecialDiseaseRuleDao specialDiseaseRuleDao;

    private static final String DISEASE_CLASS_NAME_PREFIX = "special-disease-rule-";

    @Override
    public SpecialDiseaseRule getByClassName(String diseaseClassName) {
        if (StringUtils.isBlank(diseaseClassName)){
            return null;
        }
        return specialDiseaseRuleDao.getByClassName(StringUtils.trim(diseaseClassName));
    }

    @Override
    public SpecialDiseaseRule getByClassNameCache(String diseaseClassName) {
        SpecialDiseaseRule specialDiseaseRule = PfLocalMapUtil.get(getKey(diseaseClassName));
        if (specialDiseaseRule != null) {
            log.info("diseaseClassName from local cache:{}", diseaseClassName);
            return specialDiseaseRule;
        }
        specialDiseaseRule = getByClassName(diseaseClassName);
        if (specialDiseaseRule != null) {
            PfLocalMapUtil.put(getKey(diseaseClassName), specialDiseaseRule, RedissonHandler.ONE_HOUR);
        }
        return specialDiseaseRule;
    }

    private String getKey(String diseaseClassName) {
        return DISEASE_CLASS_NAME_PREFIX + diseaseClassName;
    }

    @Override
    public List<SpecialDiseaseRule> findByClassNameList(List<String> diseaseClassNameList) {
        if (CollectionUtils.isEmpty(diseaseClassNameList)){
            return Lists.newArrayList();
        }
        return specialDiseaseRuleDao.findByClassNameList(diseaseClassNameList);
    }

    @Override
    public List<String> findAllSpecialClassNameCache() {
        String key = "disease-All-Special-Class-Name";
        List<String> specialAllClassName = PfLocalMapUtil.get(key);
        if(CollectionUtils.isNotEmpty(specialAllClassName)) {
            return specialAllClassName;
        }
        specialAllClassName =specialDiseaseRuleDao.findAllSpecialClassName();
        PfLocalMapUtil.put(key, specialAllClassName, RedissonHandler.ONE_HOUR);
        return specialAllClassName;
    }


    /**
     * 获取某个疾病对应的病种
     * @return 对应的特殊疾病规则
     */
    @Override
    public List<SpecialDiseaseRule> findCacheByClassNameList(String key, List<String> diseaseClassNameList) {

        List<SpecialDiseaseRule> diseaseRules = PfLocalMapUtil.get(getKey(key));
        if (CollectionUtils.isNotEmpty(diseaseRules)) {
            return diseaseRules;
        }
        List<SpecialDiseaseRule> ruleList = findByClassNameList(diseaseClassNameList);
        if (CollectionUtils.isNotEmpty(diseaseRules)) {
            PfLocalMapUtil.put(getKey(key), diseaseRules, RedissonHandler.ONE_HOUR);
        }
        return ruleList;
    }

    @Override
    public List<SpecialDiseaseRule> findCacheByClassNameList(String key,
                                                             List<String> diseaseClassNameList,
                                                             SpecialDiseaseRuleTypeEnum typeEnum) {
        String typeKey = getKey(key + "_" + typeEnum.getCode());
        List<SpecialDiseaseRule> diseaseRules = PfLocalMapUtil.get(typeKey);
        if (CollectionUtils.isNotEmpty(diseaseRules)) {
            return diseaseRules;
        }
        List<SpecialDiseaseRule> ruleList = findByClassNameListAndType(diseaseClassNameList, typeEnum);
        if (CollectionUtils.isNotEmpty(diseaseRules)) {
            PfLocalMapUtil.put(typeKey, diseaseRules, RedissonHandler.ONE_HOUR);
        }
        return ruleList;
    }

    @Override
    public List<SpecialDiseaseRule> findByClassNameListAndType(List<String> diseaseClassNameList, SpecialDiseaseRuleTypeEnum type) {
        if (CollectionUtils.isEmpty(diseaseClassNameList) || type.getCode() <= 0){
            return Lists.newArrayList();
        }
        return specialDiseaseRuleDao.findByClassNameListAndType(diseaseClassNameList, type.getCode());
    }
}
