package com.shuidihuzhu.cf.risk.biz.disease;

import com.github.pagehelper.Page;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseNameQuery;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 */
public interface RiskDiseaseDataRpcBiz {

    List<RiskDiseaseData> getByClassNameList(List<String> diseaseNameList);

    List<String> findAllDiseaseNameCache();

   Page<String> findByDiseaseName(DiseaseNameQuery diseaseNameQuery);

    List<String> findByDiseaseName(String diseaseNameKeyWord);

}
