package com.shuidihuzhu.cf.risk.biz.impl;

import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.risk.model.highrisk.CityThreshold;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("真实性特征上下文")
@Data
public class JudgeContext {

    @ApiModelProperty("初版：0 v3版本： 3")
    private int version;

    @ApiModelProperty("请求入参")
    private HighRiskJudgeInfoModel requestParam;

    @ApiModelProperty("v2版本使用")
    private CityThreshold cityThreshold;

    @ApiModelProperty("城市策略")
    private CaseCityInfoVO cityInfoVO;

    @ApiModelProperty("案例文章")
    private String content;

    @ApiModelProperty("案例文章(生成高风险工单用)")
    private String contentForHighRisk;


    @ApiModelProperty("目标金额 元 不需传")
    private Integer targetAmountInYuan;

    @ApiModelProperty("自建房阈值 元")
    private Integer selfHouseThreshold;

    @ApiModelProperty("核心家庭-房产总量")
    private int coreHouseTotal;

    @ApiModelProperty("核心家庭-是否有任意一套房产净值大于等于房产阈值且该房产净值大于等于房产阈值的x%")
    private boolean coreHouse_hasAnyOneOverThresholdX = false;

    @ApiModelProperty("核心家庭-是否有房产净值覆盖目标金额")
    private boolean coreHouse_hasAnyOneOverTargetAmount = false;

    @ApiModelProperty("核心家庭-是否有房产净值超过阈值")
    private boolean coreHouse_hasAnyOneOverThreshold = false;

    @ApiModelProperty("核心家庭-核心家庭房产净值略低于城市房产阈值")
    private boolean coreHouse_hasAnyOneLowerThresholdSlightly = false;

    @ApiModelProperty("核心家庭-房产总净值")
    private int coreHouse_housingNetAssetsTotal = 0;

    @ApiModelProperty("核心家庭-房产净值最低值")
    private int coreHouse_minHousingNetAsset = 0;

    @ApiModelProperty("核心家庭-车辆总量")
    private int coreCar_totalCount = 0;

    @ApiModelProperty("核心家庭-营生车总量")
    private int coreCar_business_totalCount = 0;

    @ApiModelProperty("核心家庭-是否有任意非营生车品牌为名牌车")
    private boolean coreCar_nonBusiness_hasAnyOneBrandIsFamous = false;

    @ApiModelProperty("核心家庭-非营生车总数量")
    private int coreCar_nonBusiness_totalCount = 0;

    @ApiModelProperty("核心家庭-非营生车总价值")
    private int coreCar_nonBusiness_totalValue = 0;

    @ApiModelProperty("核心家庭-非营生车总净值")
    private int coreCar_nonBusiness_totalNetValue = 0;

    @ApiModelProperty("核心家庭负债-负债 元")
    private int coreDebt_total = 0;

    @ApiModelProperty("核心家庭年收入")
    private int coreIncomeYear = 0;

    @ApiModelProperty("核心家庭年净收入")
    private int coreIncomeYearNet = 0;

    @ApiModelProperty("家庭年支出")
    private int familyCost = 0;

    @ApiModelProperty("【核心家庭现金流】金融资产金额")
    private int financialAssetAmount = 0;

    @ApiModelProperty("其他渠道救助金额")
    private double otherChannelReliefAmount = 0;

    @ApiModelProperty("事故已赔付金额")
    private int accidentCompensationAmountPaid = 0;

    @ApiModelProperty("已婚子女-是否有任意一套房产价值大于等于房产阈值*1.5")
    private boolean marriedChildren_hasAnyOneOverThresholdX1_5 = false;

    @ApiModelProperty("已婚子女-拥有2套及以上的已婚子女数量")
    private int marriedChildren_owningTwoOrMoreHousesCount = 0;

    @ApiModelProperty("已婚子女-价值超过15万的非营生车产数量")
    private int marriedChildren_nonBusinessVehiclesOver150kValueCount = 0;

    @ApiModelProperty("已婚子女-拥有2辆及以上车产的已婚子女数量")
    private int marriedChildren_owningTwoOrMoreVehiclesCount = 0;

    @ApiModelProperty("有钱父母-父母家庭房产数量")
    private int parent_familyHouseCount = 0;

    @ApiModelProperty("有钱父母-非营生车产价值")
    private int parent_nonBusinessVehicleValue = 0;

    @ApiModelProperty("有钱父母-非营生车数量")
    private int parent_nonBusinessVehicleCount = 0;

    @ApiModelProperty("有钱父母-是否有任意一套房产价值大于等于房产阈值*1.5")
    private boolean parent_hasAnyOneOverThresholdX1_5 = false;


}
