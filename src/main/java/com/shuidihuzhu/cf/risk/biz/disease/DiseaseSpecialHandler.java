package com.shuidihuzhu.cf.risk.biz.disease;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.SpecialDiseaseBurnEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseSpecialInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
public interface DiseaseSpecialHandler {



    /**
     * 利用责任链模式进行循环处理
     * 获取需要计算的金额
     */
    List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                    List<String> diseaseNameList, int diseaseJudgeLimit,
                                    List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos);

    /**
     * 获取本地缓存值
     * @return
     */
    default String getKey(String specialClassName) {
        return "Disease-special-deal" + specialClassName;
    }


    /**
     * 过滤特殊案例
     * @param diseaseNameList      入参疾病集合
     * @param specialClassNameList 特殊疾病类名集合
     * @return
     */
    default List<String> filterSpecialDisease(List<String> diseaseNameList,
                                              List<String> specialClassNameList) {
        return diseaseNameList.stream().filter(specialClassNameList::contains)
                .collect(Collectors.toList());
    }

    /**
     * 过滤特殊案例
     * @param diseaseNameList       入参疾病集合
     * @param diseaseJudgeLimit     疾病顺序限制个数
     * @return
     */
    default List<String> getTopLimit(List<String> diseaseNameList,
                                     int diseaseJudgeLimit) {
        if (diseaseNameList.size() > diseaseJudgeLimit) {
            return diseaseNameList.subList(0, diseaseJudgeLimit);
        }
        return diseaseNameList;
    }

    /**
     * 获取去除了特殊病例的集合
     * @param diseaseNameList
     * @param diseaseJudgeLimit
     * @return
     */
    default List<String> getFilterAfterLimit(List<String> diseaseNameList,
                                       int diseaseJudgeLimit) {
        if (diseaseJudgeLimit > diseaseNameList.size()) {
            return diseaseNameList.subList(0, diseaseJudgeLimit);
        }
        return diseaseNameList;
    }

    /**
     * 增加特殊病例匹配结果
     * @param filterDiseaseName
     * @param resultVo
     */
    default void addResultDiseaseName(List<String> filterDiseaseName,
                                      InfoReasonableAmountResultVo resultVo,
                                      boolean calculateSpecialStatus) {
        for (String specialDisease:filterDiseaseName) {
            DiseaseInfoVo diseaseInfoVo = new DiseaseInfoVo();
            diseaseInfoVo.setDiseaseName(specialDisease);
            diseaseInfoVo.setDiseaseType(RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode());
            diseaseInfoVo.setCalculateSpecialStatus(calculateSpecialStatus);
            resultVo.getDiseaseInfoList().add(diseaseInfoVo);
        }
    }

    /**
     * 特殊病例去除
     * @param diseaseNameList       疾病集合
     * @param filterDiseaseName     需要替换的疾病名称集合
     */
    default void replaceDiseaseName(List<String> diseaseNameList,
                                    List<String> filterDiseaseName,
                                    boolean isMerge,
                                    boolean isTop) {
        if (CollectionUtils.isEmpty(filterDiseaseName)) {
            return;
        }
        for (int i = 0; i< diseaseNameList.size(); i++) {
            if (!filterDiseaseName.contains(diseaseNameList.get(i))) {
                continue;
            }
            //判断是否需要置顶
            if (isTop){
                diseaseNameList.add(0, "");
            } else {
                diseaseNameList.set(i, "");
            }
            if (!isMerge) {
                continue;
            }
            //如果需要合并则跳出循环
            diseaseNameList.removeAll(filterDiseaseName);
            break;
        }
    }

    /**
     *
     * @param specialDiseaseChoiceInfos        用户选择的信息
     * @param filterDiseaseNameList         需要查询的疾病类型
     * @return 需要处理的特殊格式
     */
    default List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo>  filterSpecialChoice(List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos,
                                                                                           final List<String> filterDiseaseNameList) {
        if (CollectionUtils.isEmpty(specialDiseaseChoiceInfos)) {
            return Lists.newArrayList();
        }
        return specialDiseaseChoiceInfos.stream().filter(v -> filterDiseaseNameList.contains(v.getSpecialDiseaseName()))
                .collect(Collectors.toList());
    }
}
