package com.shuidihuzhu.cf.risk.biz.impl;

import com.shuidihuzhu.cf.risk.biz.UserIncomeBiz;
import com.shuidihuzhu.cf.risk.dao.UserIncomeThirdDao;
import com.shuidihuzhu.cf.risk.model.UserIncomeThird;
import com.shuidihuzhu.cf.risk.model.enums.UserInComeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@Slf4j
@Service
public class UserIncomeBizImpl implements UserIncomeBiz {

    @Autowired
    private UserIncomeThirdDao userIncomeThirdDao;

    @Override
    public int save(UserIncomeThird userIncomeThird) {
        return this.userIncomeThirdDao.save(userIncomeThird);
    }

    @Override
    public UserIncomeThird getBairongByIdCard(String idCard, String realName) {
        return this.userIncomeThirdDao.getByIdCard(idCard, realName, UserInComeEnum.ThirdType.BAIRONG.getCode(),
                UserInComeEnum.ThirdStatus.SUCCESS.getCode(), null);
    }

    @Override
    public UserIncomeThird getShangyongByIdCard(String idCard, String realName, String modelType) {
        return this.userIncomeThirdDao.getByIdCard(idCard, realName, UserInComeEnum.ThirdType.SHANGYONG.getCode(),
                UserInComeEnum.ThirdStatus.SUCCESS.getCode(), modelType);
    }

}
