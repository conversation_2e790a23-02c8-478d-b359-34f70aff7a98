package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/28
 * 判断案例的举报待录入
 */
@Service
@Slf4j
public class DiseaseMaterialTypeInModel extends ExecuteStrategyModelTemplate {

    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Override
    public String getCode() {
        return "material-type-in-judge";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        if (transit.isJudgeMaterialType()){
            return;
        }
        Response<List<CfCaseSpecialPrePoseDetail>> relationResponse = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(
                Lists.newArrayList(diseaseStrategyRequest.getCaseId()));
        transit.setJudgeMaterialType(true);
        if (relationResponse.notOk() ) {
            log.error("rpcResult fail:{}", JSON.toJSONString(relationResponse));
            return;
        }
        if (relationResponse.getData() == null ) {
            return;
        }
        List<CfCaseSpecialPrePoseDetail> cfCaseSpecialPrePoseDetailList = relationResponse
                .getData().stream().filter(v -> v.getPreposeId() != 0).collect(Collectors.toList());
        if (log.isDebugEnabled()){
            log.debug("cfCaseSpecialPrePoseDetailList:{}", JSON.toJSONString(cfCaseSpecialPrePoseDetailList));
        }
        if (CollectionUtils.isEmpty(cfCaseSpecialPrePoseDetailList)) {
            return;
        }
        transit.setHaNext(false);
        //写入识别结果
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(
                resultInfo(getResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum())));
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        switch (diseaseStrategyEnum) {
            case AMOUNT_REASONABLE:
                return DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.USED;
            default:
                return null;
        }
    }
}
