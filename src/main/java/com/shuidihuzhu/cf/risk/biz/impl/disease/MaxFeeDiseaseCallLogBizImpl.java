package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.shuidihuzhu.cf.risk.biz.disease.MaxFeeDiseaseCallLogBiz;
import com.shuidihuzhu.cf.risk.dao.MaxFeeDiseaseCallLogDao;
import com.shuidihuzhu.cf.risk.model.disease.MaxFeeDiseaseCallLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MaxFeeDiseaseCallLogBizImpl implements MaxFeeDiseaseCallLogBiz {

    @Autowired
    private MaxFeeDiseaseCallLogDao callLogDao;

    @Override
    public int add(MaxFeeDiseaseCallLog callLog) {
        return callLogDao.add(callLog);
    }
}
