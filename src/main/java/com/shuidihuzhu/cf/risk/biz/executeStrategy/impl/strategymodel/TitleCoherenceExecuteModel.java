package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.RiskExecuteStrategyLogBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.risk.service.SeaAccountService;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/24
 */
@Service
@Slf4j
public class TitleCoherenceExecuteModel extends ExecuteStrategyModelTemplate {

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskExecuteStrategyLogBiz strategyLogBiz;

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noResultMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_NOT_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_NOT_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_NOT_SAME);
        }
    };
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noResultRaiseMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_NOT_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> partResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_PART_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_PART_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_PART_SAME);
        }
    };
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> partResultRaiseMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_PART_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> allCanResultMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_SAME);
        }
    };
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> allCanResultRaiseMap = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_SAME);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME);
        }
    };


    @Override
    public String getCode() {
        return "title_coherence";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        transit.setHaNext(false);
        //获取文章和标题
        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse
                =  crowdfundingFeignClient.getCaseInfoById(diseaseStrategyRequest.getCaseId());
        if (crowdfundingInfoFeignResponse.isFailOrNullData()){
            log.error("crowdfundingInfoFeignResponse is error, :{}",
                    JSON.toJSONString(crowdfundingInfoFeignResponse));
            return;
        }
        String title = crowdfundingInfoFeignResponse.getData().getTitle();
        String content = crowdfundingInfoFeignResponse.getData().getContent();
        //获取需要匹配到的疾病
        List<String> matchDiseaseList = gteMatchByType(transit, diseaseStrategyRequest,
                diseaseStrategyResponse);


        List<String> matchNameList = Lists.newArrayList();
        //null 完全匹配到
        int titleCounter = 0 ;
        //匹配标题
        for (String s : matchDiseaseList) {
            if (title.contains(s)) {
                matchNameList.add(s);
                titleCounter ++;
            }
        }
        if (titleCounter == matchDiseaseList.size()) {
            transit.setMatchLocation("标题");
            diseaseStrategyResponse.setDiseaseContentCoherenceStrategyResult(resultInfo(
                    getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(),
                            isRasie(diseaseStrategyResponse.getDiseaseRaiseStrategyResult()) ? allCanResultRaiseMap : allCanResultMap), matchNameList));
            strategyLogBiz.save(Lists.newArrayList( buildBaseLog(diseaseStrategyRequest, matchDiseaseList, matchNameList, transit.getMatchLocation(),
                    diseaseStrategyResponse.getDiseaseContentCoherenceStrategyResult())));
            return;
        }
        int contentCounter = 0 ;
        //匹配文章
        for (String s : matchDiseaseList) {
            s = StringUtils.trim(s);
            if (content.contains(s)) {
                matchNameList.add(s);
                contentCounter ++;
            }
        }

        //匹配位置
        if (titleCounter > 0 && contentCounter > 0) {
            transit.setMatchLocation( "文章&标题");
        } else if (titleCounter > 0) {
            transit.setMatchLocation( "标题");
        }  else if (contentCounter > 0) {
            transit.setMatchLocation( "文章");
        }


        int allCounter = titleCounter + contentCounter;
        if (allCounter == matchDiseaseList.size()) {
            diseaseStrategyResponse.setDiseaseContentCoherenceStrategyResult(resultInfo(
                    getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(),
                            isRasie(diseaseStrategyResponse.getDiseaseRaiseStrategyResult()) ? allCanResultRaiseMap :  allCanResultMap), matchNameList));
            strategyLogBiz.save(Lists.newArrayList( buildBaseLog(diseaseStrategyRequest, matchDiseaseList, matchNameList, transit.getMatchLocation(),
                    diseaseStrategyResponse.getDiseaseContentCoherenceStrategyResult())));
        } else if (allCounter > 0){
            diseaseStrategyResponse.setDiseaseContentCoherenceStrategyResult(resultInfo(
                    getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(),
                            isRasie(diseaseStrategyResponse.getDiseaseRaiseStrategyResult()) ? partResultRaiseMap : partResultMap), matchNameList));
            strategyLogBiz.save(Lists.newArrayList( buildBaseLog(diseaseStrategyRequest, matchDiseaseList, matchNameList, transit.getMatchLocation(),
                    diseaseStrategyResponse.getDiseaseContentCoherenceStrategyResult())));
        } else {
            diseaseStrategyResponse.setDiseaseContentCoherenceStrategyResult(resultInfo(
                    getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(),
                            isRasie(diseaseStrategyResponse.getDiseaseRaiseStrategyResult()) ? noResultRaiseMap : noResultMap), matchNameList));
            strategyLogBiz.save(Lists.newArrayList( buildBaseLog(diseaseStrategyRequest, matchDiseaseList, matchNameList, transit.getMatchLocation(),
                    diseaseStrategyResponse.getDiseaseContentCoherenceStrategyResult())));
        }

    }

    private boolean isRasie(DiseaseStrategyResultInfo resultInfo) {
        return  resultInfo.getResult() == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode() ||
                resultInfo.getResult() == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode();
    }

    private List<String> gteMatchByType(RiskExecuteStrategyTransit transit,
                                        DiseaseStrategyRequest diseaseStrategyRequest,
                                        DiseaseStrategyResponse diseaseStrategyResponse) {
        DiseaseStrategyEnum diseaseStrategyEnum  = DiseaseStrategyEnum.findByCode(diseaseStrategyRequest.getExecuteStrategyEnum());
        switch (diseaseStrategyEnum) {
            case USER_WRITE:
                return transit.getMatchList();
            case MANUAL_WRITE:
                if (transit.getCaseRaiseType() != null &&
                        transit.getCaseRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE){
                    //可发和特殊可发的
                    return diseaseStrategyResponse.getDiseaseInfoList().stream()
                            .filter(v -> v.getDiseaseType() != RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode())
                            .map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList());
                }
                //全部录入疾病
                return diseaseStrategyRequest.getDiseaseNameList();
            default:
                //todo 区分场景
                return diseaseStrategyResponse.getDiseaseInfoList().stream()
                                .filter(v -> v.getDiseaseType() != RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode())
                                .map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList());

        }
    }

    private RiskExecuteStrategyLog buildBaseLog(DiseaseStrategyRequest request, List<String> contrastDiseaseName,
                                                List<String> matchDiseaseName, String location,
                                                DiseaseStrategyResultInfo resultInfo) {
        RiskExecuteStrategyLog riskExecuteStrategyLog = new RiskExecuteStrategyLog();
        riskExecuteStrategyLog.setCaseId(request.getCaseId());
        riskExecuteStrategyLog.setWorkOrderId(request.getWorkOrderId());
        riskExecuteStrategyLog.setExecuteTime(DateUtil.nowTime());
        if (request.getExecuteStrategyEnum() == DiseaseStrategyEnum.USER_WRITE.getCode()){
            riskExecuteStrategyLog.setStrategyName("医疗材料中疾病与图文是否一致");
        } else {
            riskExecuteStrategyLog.setStrategyName("手动录入的疾病与图文是否一致");
        }
        riskExecuteStrategyLog.setStrategyResult(String.valueOf(resultInfo.getResult()));
        riskExecuteStrategyLog.setStrategyResultDesc(resultInfo.getResultDesc());
        Map<String, Object> otherMap = Maps.newHashMap();
        otherMap.put("diseaseName", StringUtils.join(request.getDiseaseNameList(), "，"));
        otherMap.put("contrastDiseaseName", StringUtils.join(contrastDiseaseName, "，"));
        otherMap.put("matchDiseaseName", StringUtils.join(matchDiseaseName, "，"));
        otherMap.put("matchLocation", "" + location);
        otherMap.put("executeStrategyEnum", request.getExecuteStrategyEnum());
        riskExecuteStrategyLog.setOtherInfo(JSON.toJSONString(otherMap));
        riskExecuteStrategyLog.setOperator(seaAccountService.getCurrAdminUserNameWithOrg(request.getUserId()).getUserNameWithOrg());
        return riskExecuteStrategyLog;
    }



    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }
}
