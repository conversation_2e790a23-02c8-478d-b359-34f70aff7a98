package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 目标金额计算多疾病方案选择
 *
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
@Slf4j
public class DiseaseMultipleTreatmentModel extends ExecuteStrategyModelTemplate {


    @Autowired
    private DiseaseRpcService diseaseRpcService;


    @Override
    public String getCode() {
        return "disease_amount_multiple_choice";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList())) {
            return;
        }
        SpecialDiseaseChoiceInfoVo choiceInfoVoResponse =
                diseaseRpcService.specialChoiceInfo(transit.getMergeResult(),
                        diseaseStrategyRequest.getSpecialRaiseChoiceInfo()).getData();
        if (CollectionUtils.isEmpty(choiceInfoVoResponse.getSpecialDiseaseChoiceInfoList())) {
            return;
        }
        DiseaseStrategyResultInfo resultInfo = new DiseaseStrategyResultInfo();
        resultInfo.setMulitipleDiseaseChoiceInfoList(choiceInfoVoResponse.getSpecialDiseaseChoiceInfoList());
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(resultInfo);
        transit.setHaNext(false);
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }
}
