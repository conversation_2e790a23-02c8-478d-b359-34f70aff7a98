package com.shuidihuzhu.cf.risk.biz.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.PreposeMaterialRiskBiz;
import com.shuidihuzhu.cf.risk.model.risk.CfPreposeMaterialRiskVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.base.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020-05-14
 **/
@Slf4j
@Service
@RefreshScope
public class PreposeMaterialRiskBizImpl implements PreposeMaterialRiskBiz {

    @Value("${apollo.prepose-material-house-num-risk:3}")
    private int materialHouseNum;
    @Value("${apollo.prepose-material-house-value-risk:1000000}")
    private int materialHouseValue;
    @Value("${apollo.prepose-material-car-brand-risk:布加迪,宾利,兰博基尼,法拉利,保时捷,阿尔法,奔驰,宝马,奥迪,帕加尼,捷豹," +
            "凯迪拉克,玛莎拉蒂,克莱斯勒,阿斯顿马丁,劳斯莱斯,莲花,通用-克尔维特,通用-旁迪克,路虎,福特野马,雷克萨斯,英菲尼迪," +
            "雷诺,道奇,迈巴赫,斯巴鲁,沃尔沃,世爵,威兹曼,悍马,菲亚特,}")
    private String materialCarBrand;
    @Value("${apollo.prepose-material-car-value-risk:200000}")
    private int materialCarValue;
    @Value("${apollo.prepose-material-commercial-amount-risk:500000}")
    private int materialCommercialAmount;
    @Value("${apollo.prepose-material-annual-household-income-risk:150000}")
    private int annualHouseholdIncome;
    @Value("${apollo.prepose-material-household-financial-assets-risk:50000}")
    private int householdFinancialAssets;
    @Value("${apollo.prepose-material-home-debt-amount-risk:9999000}")
    private int homeDebtAmountRisk;
    @Value("${apollo.prepose-material-home-raise-remain-amount-risk:100000}")
    private int raiseRemainAmount;
    @Value("${apollo.prepose-material-self-house-amount-area:500000}")
    private int selfHouseAmountArea;
    @Value("${apollo.prepose-material-self-house-num:3}")
    private int selfHouseNumSum;
    @Value("${apollo.prepose-material-self-house-value-sum:1200000}")
    private int selfHouseValueSum;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Override
    public CfPreposeMaterialRiskVo markRisk(PreposeMaterialModel.MaterialInfoVo info) {
        if (info.getMaterialType() == PreposeMaterialModel.MaterialType.SNAPSHOT.getCode()) {
            log.info("当前报备是草稿。不需要更新 id：{}", info.getId());
            return new CfPreposeMaterialRiskVo();
        }
        if (info.getVersion() == PreposeMaterialModel.VersionEnum.VERSION_1.getVersionCode()){
            return this.fillMaterialRiskLevel(info);
        } else if (info.getVersion() >= PreposeMaterialModel.VersionEnum.VERSION_4_0.getVersionCode()) {
            return this.fillMaterialRiskLevelV3(info);
        } else {
            return this.fillMaterialRiskLevelV2(info);
        }
    }

    private CfPreposeMaterialRiskVo fillMaterialRiskLevelV3(PreposeMaterialModel.MaterialInfoVo info) {
        List<Integer> riskLabels = Lists.newArrayList();
        //汽车总价值>=20万 触发风控
        if (info.getCarValue() != null && info.getCarValue() >= materialCarValue) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.WELL_KNOWN_BRAND_VALUE_RISK.getCode());
        }
        //家庭年收入>=15万 触发风控
        if (info.getHomeIncome() != null && info.getHomeIncome() >= annualHouseholdIncome) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ANNUAL_HOUSEHOLD_INCOME_RISK.getCode());
        }
        //家庭金融资产>=5万 触发风控
        if ((info.getFinancialAssetsAmount() != null && info.getFinancialAssetsAmount() >= householdFinancialAssets)) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSEHOLD_FINANCIAL_ASSETS_RISK.getCode());
        }
        // 家庭金融资产>=目标金额
        if (info.getFinancialAssetsAmountArea() != null) {
            PreposeMaterialModel.FinancialAssetsAmountAreaEnum financialAssetsAmountAreaEnum = PreposeMaterialModel.FinancialAssetsAmountAreaEnum.valueOfCode(info.getFinancialAssetsAmountArea());
            int amount = financialAssetsAmountAreaEnum.getEnd() == null ? financialAssetsAmountAreaEnum.getStart() : financialAssetsAmountAreaEnum.getEnd();
            if (amount > Objects.firstNonNull(info.getTargetAmount(), 0)) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.TARGET_AMOUNT.getCode());
            }
        } else {
            if (info.getFinancialAssetsAmount() != null && info.getFinancialAssetsAmount() >= Objects.firstNonNull(info.getTargetAmount(), 0)) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.TARGET_AMOUNT.getCode());
            }
        }
        //案例类型命中“非默认”，触发风控
        if (info.getAccidentType() != null && info.getAccidentType() > 0) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.NON_COMMON_CASE.getCode());
        }
        //身份命中“非普通”，触发风控
        if (info.getPatientIdentity() != null && info.getPatientIdentity() > 0) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.NON_COMMON_IDENTITY.getCode());
        }
        this.setCommonRisk(info, riskLabels);

        //自建房总价值>=50万
        if (info.getSelfHouseValue() != null && info.getSelfHouseValue() > selfHouseAmountArea) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS.getCode());
        }
        if (info.getSelfHouseAmountArea() != null && info.getSelfHouseAmountArea() > 0) {
            var houseSellingAmountAreaEnum = PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(info.getSelfHouseAmountArea());
            long selfHouseAmount = houseSellingAmountAreaEnum.getEnd() == null ? houseSellingAmountAreaEnum.getStart() : houseSellingAmountAreaEnum.getEnd();
            if (null != houseSellingAmountAreaEnum && selfHouseAmount > selfHouseAmountArea) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS.getCode());
            }
        }
        //其他房屋总价值>=100万
        if ((info.getHouseValue() != null && info.getHouseValue() >= materialHouseValue)) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.OTHER_BUILT_HOUSING_ASSETS.getCode());
        }

        //（自建房数量+其他房屋数量）>=3
        int selfHouseNum = 0;
        int houseNum = 0;
        if (info.getSelfHouseNum() != null && info.getSelfHouseNum() > 0) {
            var houseNumEnum = PreposeMaterialModel.HouseNumEnum.valueOfCode(info.getSelfHouseNum());
            if (null != houseNumEnum) {
                selfHouseNum = houseNumEnum.getCode();
            }
        }
        if (info.getHouseNum() != null && info.getHouseNum() > 0) {
            var houseNumEnum = PreposeMaterialModel.HouseNumEnum.valueOfCode(info.getHouseNum());
            if (null != houseNumEnum) {
                houseNum = houseNumEnum.getCode();
            }
        }
        if (houseNum + selfHouseNum >= selfHouseNumSum) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_NUMBER.getCode());
        }
        //（自建房总价值+其他房屋总价值）>=120万
        int houseValue = info.getHouseValue() == null ? 0 : info.getHouseValue();
        int selfHouseValue = 0;
        if (info.getSelfHouseValue() != null) {
            selfHouseValue = info.getSelfHouseValue();
            var houseSellingAmountAreaEnum = PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(info.getSelfHouseAmountArea());
            if (java.util.Objects.nonNull(houseSellingAmountAreaEnum) && selfHouseValue == 0) {
                selfHouseValue = houseSellingAmountAreaEnum.getEnd() == null ? houseSellingAmountAreaEnum.getStart() : houseSellingAmountAreaEnum.getEnd();
            }
        }
        if (houseValue + selfHouseValue >= selfHouseValueSum) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS_SUM.getCode());
        }

        if (CollectionUtils.isNotEmpty(riskLabels)) {
            return new CfPreposeMaterialRiskVo(PreposeMaterialModel.RiskLevel.RED, riskLabels);
        } else {
            return new CfPreposeMaterialRiskVo();
        }
    }

    private CfPreposeMaterialRiskVo fillMaterialRiskLevel(PreposeMaterialModel.MaterialInfoVo info) {
        List<Integer> riskLabels = Lists.newArrayList();

        if (info.getHouseNum() >= materialHouseNum || info.getHouseValue() >= materialHouseValue) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSE_NUM_OR_VALUE_RISK.getCode());
        }

        if ((info.getCarBrand() != null &&
                StringUtils.isNotBlank(info.getCarBrand().trim())
                && materialCarBrand.contains(info.getCarBrand().trim() + ",")) || info.getCarValue() >= materialCarValue) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.WELL_KNOWN_BRAND_VALUE_RISK.getCode());
        }

        if (info.getAccidentType() == PreposeMaterialModel.AccidentType.CRIMINAL_DUTY.getCode()
                || info.getPayForState() == PreposeMaterialModel.PayForState.ALL_PAID.getCode()
                || info.getPayForState() == PreposeMaterialModel.PayForState.NOT_YET_DECIDED.getCode()) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.CRIMINAL_DUTY.getCode());
        }

//        if (info.getHomeDeposit() >= info.getTargetAmount()) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.DEPOSIT_EXCEED_TARGET_AMOUNT.getCode());
//        }

        if (info.getCommercialInsurance() == 1 && info.getCommercialAmount() >= materialCommercialAmount) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.COMMERCIAL_AMOUNT_EXCEED_FIFTY.getCode());

        }

        if (PreposeMaterialModel.PatientIdentity.isRisk(info.getPatientIdentity())) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ACTIVE_DUTY_SOLDIER.getCode());
        }

        // 新增
//        if (info.getAccidentDuty() == PreposeMaterialModel.AccidentDuty.NO_DECIDE_DUTY.getCode()
//            || info.getAccidentDuty() ==  PreposeMaterialModel.AccidentDuty.BOTH_DUTY.getCode()) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ACCIDENT_DUTY_RISK.getCode());
//        }
//        if (info.getCarHasSell() == 1 || info.getHouseHasSell() == 1) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.CAR_HOUSE_SELL.getCode());
//        }
//        if (info.getHasSupply() == 1) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HAS_SUPPLY_RISK.getCode());
//        }
//        if (info.getMedicalInsuranceRate() >= 80) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.MEDICAL_INSURANCE_RATE_LARGE.getCode());
//        }
        if (info.getMediaGovernmentNotice() == 1) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.MEDIA_GOVERNMENT_NOTICE.getCode());
        }
        setCommonRisk(info, riskLabels);

        if (CollectionUtils.isNotEmpty(riskLabels)) {
//            info.setRiskLevel(PreposeMaterialModel.RiskLevel.RED.getCode());
//            info.setRiskLabels(Joiner.on(PreposeMaterialModel.ENGLISH_COMMA).join(riskLabels));
            return new CfPreposeMaterialRiskVo(PreposeMaterialModel.RiskLevel.RED, riskLabels);
        } else {
            return new CfPreposeMaterialRiskVo();
        }
    }

    /**
     * 触发风控条件判断V2
     * @param info
     */
    private CfPreposeMaterialRiskVo fillMaterialRiskLevelV2(PreposeMaterialModel.MaterialInfoVo info) {
        List<Integer> riskLabels = Lists.newArrayList();
        //房产总价值>=100万  或者   房产数量>=3套  触发风控
        if ((info.getHouseNum() != null && info.getHouseNum() >= materialHouseNum)
                || (info.getHouseValue() !=null && info.getHouseValue() >= materialHouseValue)) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSE_NUM_OR_VALUE_RISK.getCode());
        }
        //汽车总价值>=20万 触发风控
        if (info.getCarValue() != null && info.getCarValue() >= materialCarValue) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.WELL_KNOWN_BRAND_VALUE_RISK.getCode());
        }
        //家庭年收入>=15万 触发风控
        if (info.getHomeIncome() != null && info.getHomeIncome() >= annualHouseholdIncome) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ANNUAL_HOUSEHOLD_INCOME_RISK.getCode());
        }
        //家庭金融资产>=5万 触发风控
        if (info.getFinancialAssetsAmount() != null && info.getFinancialAssetsAmount() >= householdFinancialAssets) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSEHOLD_FINANCIAL_ASSETS_RISK.getCode());
        }
        //医保报销比率 命中80%及以上，触发风控
//        if (PreposeMaterialModel.MedicalInsuranceRateAreaEnum.RATE_AREA_80.getCode() == info.getMedicalInsuranceRateArea()) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.MEDICAL_INSURANCE_RATE_LARGE.getCode());
//        }
        /**
         * 20200821 去除这两项：wiki https://wiki.shuiditech.com/pages/viewpage.action?pageId=589496375
         */
        /*//有人身险，触发风控
        if (info.getHasPersonalInsurance() != null && info.getHasPersonalInsurance() == 1) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.LIFE_INSURANCE.getCode());
        }
        //财产险，触发风控
        if (info.getHasCarInsurance() != null && info.getHasCarInsurance() == 1) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.PROPERTY_INSURANCE.getCode());
        }*/
        //政府救助，触发风控
//        if (info.getMediaGovernmentNotice() == 1){
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.GOVERNMENT_BAILOUT.getCode());
//        }
        //有其他渠道救助，触发风控
//        if (info.getHasSupply() == 1){
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HELP_FROM_OTHER_CHANNELS.getCode());
//        }
        //案例类型命中“非默认”，触发风控
        if (info.getAccidentType() != null && info.getAccidentType() > 0){
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.NON_COMMON_CASE.getCode());
        }
        //身份命中“非普通”，触发风控
        if (info.getPatientIdentity() != null && info.getPatientIdentity() > 0){
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.NON_COMMON_IDENTITY.getCode());
        }
        setCommonRisk(info, riskLabels);

        if (CollectionUtils.isNotEmpty(riskLabels)) {
//            info.setRiskLevel(PreposeMaterialModel.RiskLevel.RED.getCode());
//            info.setRiskLabels(Joiner.on(PreposeMaterialModel.ENGLISH_COMMA).join(riskLabels));
            return new CfPreposeMaterialRiskVo(PreposeMaterialModel.RiskLevel.RED, riskLabels);
        } else {
            return new CfPreposeMaterialRiskVo();
        }
    }

    private void setCommonRisk(PreposeMaterialModel.MaterialInfoVo info, List<Integer> riskLabels) {
        // 车产范围判断
        if (info.getCarAmountArea() != null && info.getCarAmountArea() > 0) {
            PreposeMaterialModel.CarSellingAmountAreaEnum carSellingAmountAreaEnum =
                    PreposeMaterialModel.CarSellingAmountAreaEnum.valueOfCode(info.getCarAmountArea());
            if (carSellingAmountAreaEnum != null) {
                if (carSellingAmountAreaEnum.getEnd() == null || carSellingAmountAreaEnum.getEnd() >= materialCarValue) {
                    riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.WELL_KNOWN_BRAND_VALUE_RISK.getCode());
                }
            }
        }
        //家庭年收入范围>=15万 触发风控
        if (info.getHomeIncomeArea() != null && info.getHomeIncomeArea() > 0) {
            PreposeMaterialModel.HomeIncomeAreaEnum homeIncomeAreaEnum =
                    PreposeMaterialModel.HomeIncomeAreaEnum.valueOfCode(info.getHomeIncomeArea());
            if (homeIncomeAreaEnum != null) {
                if (homeIncomeAreaEnum.getEnd() == null || homeIncomeAreaEnum.getEnd() >= annualHouseholdIncome) {
                    riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ANNUAL_HOUSEHOLD_INCOME_RISK.getCode());
                }
            }
        }
        //家庭金融资产>=5万 触发风控
        if (info.getFinancialAssetsAmountArea() != null && info.getFinancialAssetsAmountArea() > 0) {
            PreposeMaterialModel.FinancialAssetsAmountAreaEnum financialAssetsAmountAreaEnum =
                    PreposeMaterialModel.FinancialAssetsAmountAreaEnum.valueOfCode(info.getFinancialAssetsAmountArea());
            if (financialAssetsAmountAreaEnum != null) {
                if (financialAssetsAmountAreaEnum.getEnd() == null || financialAssetsAmountAreaEnum.getEnd() >= householdFinancialAssets) {
                    riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSEHOLD_FINANCIAL_ASSETS_RISK.getCode());
                }
            }
        }
        // 家庭债务情况选择”有“且家庭欠款金额≥999.9万元
        if (info.getHomeOwningAmount() != null && info.getHomeOwningAmount() >= homeDebtAmountRisk) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOME_DEBT_OVERTOP.getCode());
        }
        // 是否在其他平台发起筹款：是 且 剩余款项>=10万
        if (info.getHasRaise() != null && info.getHasRaise() == 1
                && info.getRemainAmount() != null && info.getRemainAmount() >= raiseRemainAmount) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.OTHER_PLATFORM_OVERTOP.getCode());
        }
    }

    @Override
    public CfPreposeMaterialRiskVo markPropertyRisk(CfPropertyInsuranceInfoModel insuranceInfo) {

        List<Integer> riskLabels = Lists.newArrayList();

        // 车的价值大于20w
        if (insuranceInfo.getCarProperty() != null) {
            CfPropertyInsuranceInfoModel.CarPropertyInfo carProperty = insuranceInfo.getCarProperty();
            if (Objects.firstNonNull(carProperty.getTotalValueUserDefined(), 0) >= materialCarValue
                    || (carProperty.getTotalValueRangeType() != null
                    && (CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(carProperty.getTotalValueRangeType()).getTo() == null || CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(carProperty.getTotalValueRangeType()).getTo() >= materialCarValue))) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.WELL_KNOWN_BRAND_VALUE_RISK.getCode());
            }
        }

        // 家庭金融资产>=目标金额
        var caseId = insuranceInfo.getCaseId();
        long targetAmount = 0;
        FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getCaseInfoById(caseId);
        if (response.ok() && response.getData() != null) {
            targetAmount = response.getData().getTargetAmount() / 100;
        }
        if (Objects.firstNonNull(insuranceInfo.getHomeStockUserDefined(), 0) >= targetAmount
                || (insuranceInfo.getHomeStockRangeType() != null &&
                (CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(insuranceInfo.getHomeStockRangeType()).getTo() == null || CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(insuranceInfo.getHomeStockRangeType()).getTo() >= targetAmount))) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.TARGET_AMOUNT.getCode());
        }

        if (insuranceInfo.getSelfBuiltHouse() != null) {
            this.setSelfHouseVersionRiskLabel(insuranceInfo, riskLabels);
        } else {
            if (insuranceInfo.getHouseProperty() != null) {
                CfPropertyInsuranceInfoModel.HousePropertyInfo houseProperty = insuranceInfo.getHouseProperty();
                if (Objects.firstNonNull(houseProperty.getTotalCount(), 0) >= 3
                        || Objects.firstNonNull(houseProperty.getTotalValueUserDefined(), 0) >= materialHouseValue) {
                    riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSE_NUM_OR_VALUE_RISK.getCode());
                }
            }
        }

        if (Objects.firstNonNull(insuranceInfo.getHomeIncomeUserDefined(), 0) >= annualHouseholdIncome
                || (insuranceInfo.getHomeIncomeRangeType() != null &&
                (CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(insuranceInfo.getHomeIncomeRangeType()).getTo() == null || CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(insuranceInfo.getHomeIncomeRangeType()).getTo() >= annualHouseholdIncome))) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.ANNUAL_HOUSEHOLD_INCOME_RISK.getCode());
        }

        if (Objects.firstNonNull(insuranceInfo.getHomeStockUserDefined(), 0) >= householdFinancialAssets
                || (insuranceInfo.getHomeStockRangeType() != null &&
                (CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(insuranceInfo.getHomeStockRangeType()).getTo() == null || CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(insuranceInfo.getHomeStockRangeType()).getTo() >= householdFinancialAssets))) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOUSEHOLD_FINANCIAL_ASSETS_RISK.getCode());
        }

        //20200812优化需求修改-https://wiki.shuiditech.com/pages/viewpage.action?pageId=589496375
        /*if (insuranceInfo.getLifeInsurance() != null && insuranceInfo.getLifeInsurance() == 1) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.LIFE_INSURANCE.getCode());
        }*/

        /*if (insuranceInfo.getPropertyInsurance() != null && insuranceInfo.getPropertyInsurance() == 1) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.PROPERTY_INSURANCE.getCode());
        }*/

//        if (insuranceInfo.getGovRelief() != null && insuranceInfo.getGovRelief() == 1) {
//            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.GOVERNMENT_BAILOUT.getCode());
//        }

        // 家庭债务情况选择”有“且家庭欠款金额≥999.9万元
        if (insuranceInfo.getHomeDebt() != null && insuranceInfo.getHomeDebt() > 0) {
            if(insuranceInfo.getHomeDebtDecimalAmount() !=null
                    && insuranceInfo.getHomeDebtDecimalAmount().intValue() >= homeDebtAmountRisk) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.HOME_DEBT_OVERTOP.getCode());
            }
        }
        // 是否在其他平台发起筹款：是 且 剩余款项>=10万
        if (insuranceInfo.getOtherPlatform() != null
                && insuranceInfo.getOtherPlatform().getHasRaise() != null && insuranceInfo.getOtherPlatform().getHasRaise() == 1
                && insuranceInfo.getOtherPlatform().getRemainAmount() != null
                && insuranceInfo.getOtherPlatform().getRemainAmount() >= raiseRemainAmount) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.OTHER_PLATFORM_OVERTOP.getCode());
        }

        if (CollectionUtils.isNotEmpty(riskLabels)) {
            insuranceInfo.setRisk(1);
            insuranceInfo.setRiskLabels(riskLabels);
            return new CfPreposeMaterialRiskVo(PreposeMaterialModel.RiskLevel.RED, riskLabels);
        } else {
            return new CfPreposeMaterialRiskVo();
        }
    }

    private void setSelfHouseVersionRiskLabel(CfPropertyInsuranceInfoModel insuranceInfo, List<Integer> riskLabels) {
        // 自建房总价值>=50万 或
        if (insuranceInfo.getSelfBuiltHouse() != null) {
            if (insuranceInfo.getSelfBuiltHouse().getTotalValueUserDefined() != null
                    && insuranceInfo.getSelfBuiltHouse().getTotalValueUserDefined() > selfHouseAmountArea) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS.getCode());
            } else if (insuranceInfo.getSelfBuiltHouse().getTotalValueRangeType() != null
                    && CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(insuranceInfo.getSelfBuiltHouse()
                    .getTotalValueRangeType()).getTo() > selfHouseAmountArea) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS.getCode());
            }
        }
        // 其他房屋总价值>=100万
        if (insuranceInfo.getHouseProperty() != null) {
            if (insuranceInfo.getHouseProperty().getTotalValueUserDefined() != null
                    && insuranceInfo.getHouseProperty().getTotalValueUserDefined() >= materialHouseValue) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.OTHER_BUILT_HOUSING_ASSETS.getCode());
            } else if (insuranceInfo.getHouseProperty().getTotalValueRangeType() != null
                    && Optional.ofNullable(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(insuranceInfo.getHouseProperty().getTotalValueRangeType()).getTo())
                    .orElse(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(insuranceInfo.getHouseProperty().getTotalValueRangeType()).getFrom()) >= materialHouseValue) {
                riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.OTHER_BUILT_HOUSING_ASSETS.getCode());
            }
        }

        // （自建房数量+其他房屋数量）>=3
        int totalCount = 0;
        int selfTotalCount = 0;
        if (insuranceInfo.getHouseProperty() != null && insuranceInfo.getHouseProperty().getTotalCount() != null) {
            totalCount = insuranceInfo.getHouseProperty().getTotalCount();
        }
        if (insuranceInfo.getSelfBuiltHouse() != null && insuranceInfo.getSelfBuiltHouse().getTotalCount() != null) {
            selfTotalCount = insuranceInfo.getSelfBuiltHouse().getTotalCount();
        }
        if (totalCount + selfTotalCount >= selfHouseNumSum) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_NUMBER.getCode());
        }

        //（自建房总价值+其他房屋总价值）>=120万
        int totalValue = 0;
        if (insuranceInfo.getHouseProperty() != null) {
            totalValue = Objects.firstNonNull(insuranceInfo.getHouseProperty().getTotalValueUserDefined(), 0);
            if (totalValue == 0 && insuranceInfo.getHouseProperty().getTotalValueRangeType() != null) {
                CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(
                        insuranceInfo.getHouseProperty().getTotalValueRangeType());
                totalValue = houseValueRange.getTo() == null ? houseValueRange.getFrom() : houseValueRange.getTo();
            }
        }
        int selfTotalValue = 0;
        if (insuranceInfo.getSelfBuiltHouse() != null) {
            selfTotalValue = Objects.firstNonNull(insuranceInfo.getSelfBuiltHouse().getTotalValueUserDefined(), 0);
            if (selfTotalValue == 0 && insuranceInfo.getSelfBuiltHouse().getTotalValueRangeType() != null) {
                CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(
                        insuranceInfo.getSelfBuiltHouse().getTotalValueRangeType());
                selfTotalValue = houseValueRange.getTo() == null ? houseValueRange.getFrom() : houseValueRange.getTo();
            }
        }
        if (totalValue + selfTotalValue >= selfHouseValueSum) {
            riskLabels.add(PreposeMaterialModel.MaterialRiskLabel.SELF_BUILT_HOUSING_ASSETS_SUM.getCode());
        }
    }
}
