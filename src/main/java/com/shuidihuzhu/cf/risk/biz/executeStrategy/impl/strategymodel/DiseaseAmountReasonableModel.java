package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
@Slf4j
public class DiseaseAmountReasonableModel extends ExecuteStrategyModelTemplate {




    /**
     * 治疗方案疾病的结果
     */
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noMultipleResult = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.AMOUNT_REASONABLE);
        }
    };

    /**
     * 多治疗方案疾病的结果
     */
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> multipleResult = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.AMOUNT_REASONABLE_2);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> REASONABLE_RESULT = new HashMap<>() {
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.AMOUNT_REASONABLE_3);
        }
    };

    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> NO_REASONABLE_RESULT = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.AMOUNT_REASONABLE, DiseaseStrategyResultEnum.DiseaseAmountStrategyEnum.AMOUNT_NOT_REASONABLE);
        }
    };


    @Autowired
    private DiseaseRpcService diseaseRpcService;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz riskDiseaseTreatmentProjectRpcBiz;

    @Override
    public String getCode() {
        return "disease_judge_amount";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList())) {
            return;
        }
        FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getCaseInfoById(diseaseStrategyRequest.getCaseId());
        if (response.notOk() || response.getData() == null) {
            log.info("response:{}", JSON.toJSONString(response));
            transit.setHaNext(false);
            return;
        }
        if (CollectionUtils.isEmpty(transit.getDiseaseClassifyVOV2List())){
            List<DiseaseClassifyVOV2> classifyVoV2List = diseaseRpcService.diseaseNorm(diseaseStrategyRequest.getDiseaseNameList());
            transit.setDiseaseClassifyVOV2List(classifyVoV2List);
        }
        transit.setTargetAmount(response.getData().getTargetAmount());
        InfoReasonableAmountResultVo reasonableAmountResultVo =
                diseaseRpcService.decideInfoAmountReasonable(response.getData().getTargetAmount(),
                        buildStrategyInfo(diseaseStrategyRequest, transit)).getData();
        transit.setResultVo(reasonableAmountResultVo);


        //无多治疗方案 或者 多治疗方案为选择的情况
        if (StringUtils.isBlank(diseaseStrategyRequest.getSpecialDiseaseInfo()) && !transit.isJudgeRaise()) {
            if (!reasonableAmountResultVo.isReasonableStatus()) {
                transit.setJudgeRaise(true);
                return;
            }
            fullResponseDiseaseInfoList(diseaseStrategyRequest, diseaseStrategyResponse, transit,
                    reasonableAmountResultVo.getDiseaseInfoList());
            //判断是否需要多治疗方案
            SpecialDiseaseChoiceInfoVo choiceInfoVoResponse = diseaseRpcService.specialChoiceInfo(transit.getMergeResult(),
                            diseaseStrategyRequest.getSpecialRaiseChoiceInfo()).getData();
            transit.setHaNext(false);
            //有多治疗方案
            if (choiceInfoVoResponse != null &&
                    CollectionUtils.isNotEmpty(choiceInfoVoResponse.getSpecialDiseaseChoiceInfoList())) {
                DiseaseStrategyResultEnum resultEnum = getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), multipleResult);
                diseaseStrategyResponse.setDiseaseAmountStrategyResult(resultInfo(resultEnum, Lists.newArrayList()));
                return;
            }
            //无多治疗方案
            DiseaseStrategyResultEnum resultEnum =  getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), noMultipleResult);
            diseaseStrategyResponse.setDiseaseAmountStrategyResult(resultInfo(resultEnum,
                    resultEnum.getHint() + buildAmountInfo(reasonableAmountResultVo), Lists.newArrayList()));
            return;
        }
        fullResponseDiseaseInfoList(diseaseStrategyRequest, diseaseStrategyResponse, transit,
                reasonableAmountResultVo.getDiseaseInfoList());
        //选择多治疗方案的情况
        transit.setHaNext(false);
        if (reasonableAmountResultVo.isReasonableStatus()) {
            DiseaseStrategyResultEnum resultEnum = getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), REASONABLE_RESULT);
            diseaseStrategyResponse.setDiseaseAmountStrategyResult(resultInfo(resultEnum,
                    resultEnum.getHint() + buildAmountInfo(reasonableAmountResultVo), Lists.newArrayList()));

            return;
        }
        DiseaseStrategyResultEnum resultEnum = getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), NO_REASONABLE_RESULT);
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(
                resultInfo(resultEnum, resultEnum.getHint() + buildAmountInfo(reasonableAmountResultVo), Lists.newArrayList()));
    }

    private void fullResponseDiseaseInfoList(DiseaseStrategyRequest request,
                                             DiseaseStrategyResponse response,
                                             RiskExecuteStrategyTransit transit,
                                             List<DiseaseInfoVo> diseaseInfoList) {
        if (StringUtils.isBlank(request.getSpecialDiseaseInfo()) && StringUtils.isBlank(request.getSpecialRaiseChoiceInfo())){
            return;
        }
        //1.对特殊方案进行判断
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialRaiseDiseaseChoiceInfos =
                JSONObject.parseArray(request.getSpecialRaiseChoiceInfo(), SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfos =
                JSONObject.parseArray(request.getSpecialDiseaseInfo(), SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class);
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> allDiseaseChoiceInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(specialDiseaseChoiceInfos)){
            allDiseaseChoiceInfos.addAll(specialDiseaseChoiceInfos);
        }
        if (CollectionUtils.isNotEmpty(specialRaiseDiseaseChoiceInfos)){
            allDiseaseChoiceInfos.addAll(specialRaiseDiseaseChoiceInfos);
        }
        Map<String, List<Long>> specialRaiseChoiceMap = diseaseRpcService.getChoiceMap(allDiseaseChoiceInfos);
        List<RiskDiseaseTreatmentProject> treatmentProjects = riskDiseaseTreatmentProjectRpcBiz.findById(specialRaiseChoiceMap.values().stream()
                .reduce(Lists.newArrayList(), (all, one) -> {
                    all.addAll(one);
                    return all;
                }));
        List<DiseaseInfoVo> diseaseInfoVos = buildList(transit.getDiseaseClassifyVOV2List(),
                diseaseInfoList, getTreatmentMap(specialRaiseChoiceMap, treatmentProjects));
        if (CollectionUtils.isEmpty(diseaseInfoVos)){
            return;
        }
        diseaseInfoVos = diseaseInfoVos.stream().filter(diseaseInfoVo ->
                request.getDiseaseNameList().contains(diseaseInfoVo.getDiseaseName()) &&
                        diseaseInfoVo.getDiseaseType() != RiskDiseaseData.RaiseTypeEnum.DEFAULT.getCode())
                .collect(Collectors.toList());
        response.setDiseaseInfoList(diseaseInfoVos);
    }

    private DecideReasonableInfo buildStrategyInfo(DiseaseStrategyRequest diseaseStrategyRequest, RiskExecuteStrategyTransit transit) {
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo();
        decideReasonableInfo.setCaseId(diseaseStrategyRequest.getCaseId());
        decideReasonableInfo.setDiseaseNameList(transit.getMergeResult());
        decideReasonableInfo.setSpecialRaiseChoiceInfo(diseaseStrategyRequest.getSpecialRaiseChoiceInfo());
        decideReasonableInfo.setSpecialDiseaseInfo(diseaseStrategyRequest.getSpecialDiseaseInfo());
        decideReasonableInfo.setRequestChannel("system-strategy");
        return decideReasonableInfo;
    }

    private String buildAmountInfo(InfoReasonableAmountResultVo reasonableAmountResultVo) {
        return "（"+ "未来花费建议为"+reasonableAmountResultVo.getAdviseMinAmount() + "-"
                +  reasonableAmountResultVo.getAdviseMaxAmount()+ "w）";
    }


    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }
}
