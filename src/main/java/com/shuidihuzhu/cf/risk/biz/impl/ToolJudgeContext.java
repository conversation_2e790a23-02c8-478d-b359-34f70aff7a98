package com.shuidihuzhu.cf.risk.biz.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.highrisk.CityThreshold;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.cf.risk.model.risk.DiscountValueInfo;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("真实性特征上下文")
@Data
public class ToolJudgeContext {

    @ApiModelProperty("请求入参")
    private FamilyFinancialSituation requestParam;

    @ApiModelProperty("入参目标金额【只用于计算 不修改】")
    private Integer targetAmount;

    /**
     * 房产相关
     */
    @ApiModelProperty("核心家庭-房产总量")
    private int coreHouseTotal;

    @ApiModelProperty("核心家庭-是否有任意一套刚需房产净值大于等于房产阈值且该房产净值大于等于房产阈值的x%")
    private boolean coreHouse_hasAnyOneOverThresholdX = false;

    @ApiModelProperty("核心家庭-闲置房产总净值")
    private int coreHouse_idleHousingNetAssetsTotal = 0;

    @ApiModelProperty("核心家庭-刚需房产-高价值房产扣减")
    private int coreHouse_highValueHousingDeduction = 0;

    /**
     * 车产相关
     */
    @ApiModelProperty("核心家庭-非营生车总净值")
    private int coreCar_nonBusiness_totalNetValue = 0;

    /**
     * 家庭资产
     */
    @ApiModelProperty("核心家庭年净收入")
    private int coreIncomeYearNet = 0;

    @ApiModelProperty("【核心家庭现金流】金融资产金额")
    private int financialAssetAmount = 0;

    @ApiModelProperty("其他渠道救助金额")
    private double otherChannelReliefAmount = 0;

    @ApiModelProperty("事故已赔付金额")
    private int accidentCompensationAmountPaid = 0;

    @ApiModelProperty("人身险已赔付金额")
    private int personalInsuranceAmountPaid = 0;

    @ApiModelProperty("核心家庭综合资产(高价值房产扣减+闲置房产总净值+非营生车产总净值+金融资产+事故已赔付金额+人身险已赔付金额+其他渠道救助)")
    private int coreFamilyTotalAssets = 0;

    /**
     * 结果相关
     */
    @ApiModelProperty("高价值房产(刚需)扣减")
    private List<DiscountValueInfo> highValueHouse = Lists.newArrayList();
    @ApiModelProperty("闲置房产总净值")
    private List<DiscountValueInfo> idleHouse = Lists.newArrayList();
    @ApiModelProperty("非营生车总净值")
    private List<DiscountValueInfo> nonBusinessCar = Lists.newArrayList();

}
