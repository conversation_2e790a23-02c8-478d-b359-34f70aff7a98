package com.shuidihuzhu.cf.risk.biz.disease;

import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/28
 */
public interface DiseaseAmountCallLogBiz {

    void saveLog(InfoReasonableAmountResultVo reasonableAmountResultVo,
                 DecideReasonableInfo decideReasonableInfo,
                 int targetAmount);

    void insertAmountRecord(DiseaseStrategyRequest diseaseStrategyRequest,
                            RiskExecuteStrategyTransit transit,
                            InfoReasonableAmountResultVo reasonableAmountResultVo,
                            DecideReasonableInfo decideReasonableInfo,
                            DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum amountReasonableEnum,
                            int targetAmount);

    void insertAmountRecord(DiseaseStrategyRequest diseaseStrategyRequest,
                            RiskExecuteStrategyTransit transit,
                            InfoReasonableAmountResultVo reasonableAmountResultVo,
                            DecideReasonableInfo decideReasonableInfo,
                            DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum amountReasonableEnum,
                            int targetAmount,
                            int version);

    List<DiseaseAmountResultRecord> getRecordWithoutToolByCaseId(int caseId);

    List<DiseaseAmountResultRecord> getToolRecordByCaseId(int caseId);

    void saveToolRecord(DiseaseAmountResultRecord diseaseAmountResultRecord);
}
