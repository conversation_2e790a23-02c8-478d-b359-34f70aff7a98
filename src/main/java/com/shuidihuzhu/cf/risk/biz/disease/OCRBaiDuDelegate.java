package com.shuidihuzhu.cf.risk.biz.disease;

import com.alibaba.fastjson.JSON;
import com.baidu.aip.ocr.AipOcr;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.*;
import com.qcloud.cos.utils.IOUtils;
import com.shuidihuzhu.cf.risk.model.ocr.OCRBaiDuResponse;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OCRBaiDuDelegate {

    //设置APPID/AK/SK免费账号
//    private static final String APP_ID_OCR = "10948171";
//    private static final String API_KEY_OCR = "EQY2swgAqqQrk0kzV1QYPcre";
//    private static final String SECRET_KEY_OCR = "GKXEUP3r8uxBCfVAhs4a7ZfIyu7IU4vS";

    //设置APPID/AK/SK付费账号
    public static final String APP_ID_OCR = "44282476";
    public static final String API_KEY_OCR = "npXG6BTKSdAyitbe4PHVB6WE";
    public static final String SECRET_KEY_OCR = "pL2lGmchuMPsteBWBGKzO30udpNPPrgg";

    protected static ListeningExecutorService service = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(10));

    /**
     * 获取百度ocr client
     *
     * @return
     */
    public AipOcr getOCRClient() {
        AipOcr client =  new AipOcr(APP_ID_OCR, API_KEY_OCR, SECRET_KEY_OCR);
        client.setConnectionTimeoutInMillis(5000);
        client.setSocketTimeoutInMillis(60000);

        return client;
    }

    public OCRBaiDuResponse accurateGeneral(String file) {
        AipOcr client = getOCRClient();
        // 传入可选参数调用接口
        HashMap<String, String> options = Maps.newHashMap();
        options.put("language_type", "CHN_ENG");
//        options.put("detect_direction", "true");
//        options.put("detect_language", "true");
//        options.put("probability", "true");
        try {
            log.info("accurateGeneralFile:{}", file);
            ListenableFuture<JSONObject> booleanTask = service.submit(new Callable<JSONObject>() {
                @Override
                public JSONObject call() throws Exception {
                    long start = System.currentTimeMillis();
                    JSONObject res = client.accurateGeneral(getStreamByte(file), options);
                    long end = System.currentTimeMillis();
                    log.info("ocr time consuming time:{}", (end - start));
                    return res;
                }
            });
            Futures.addCallback(booleanTask, new FutureCallback<JSONObject>() {
                @SneakyThrows
                @Override
                public void onSuccess(JSONObject result) {
                    log.info("ocr distinguish Success result:{}", result);
                }

                @Override
                public void onFailure(@NotNull Throwable throwable) {
                    log.error("ocr throwable error throwable:{}", throwable);
                }
            }, service);
            String jsonString = booleanTask.get().toString();
            log.info("ocr baidu response:{}", jsonString);
            return JSON.parseObject(jsonString, OCRBaiDuResponse.class);
        } catch (Exception e) {
            log.debug(e.getMessage());
            log.error("ocr baidu error", e);
            return null;
        }
    }

    private static byte[] getStreamByte(String fileURL) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        if (fileURL.startsWith("http") || fileURL.startsWith("https")) {
            URL url = new URL(fileURL);
            inputStream = url.openStream();
        } else {
            inputStream = new FileInputStream(fileURL);
        }
        IOUtils.copy(inputStream, output);
        return output.toByteArray();
    }

    public List<String> accurateGeneralList(String file) {
        OCRBaiDuResponse ocrBaiDuResponse = accurateGeneral(file);
        return Optional.ofNullable(ocrBaiDuResponse)
                .map(OCRBaiDuResponse::getWords_result)
                .orElse(Lists.newArrayList())
                .stream()
                .map(OCRBaiDuResponse.Result::getWords)
                .collect(Collectors.toList());
    }

    public List<String> ocrMulipitleFile(List<String> urls) {
        // ocr识别结果list
        List<String> diseaseList = Lists.newArrayList();
        for (String url : urls) {
            List<String> words = accurateGeneralList(url);
            CollectionUtils.addAll(diseaseList, words);
        }
       return diseaseList;
    }
}
