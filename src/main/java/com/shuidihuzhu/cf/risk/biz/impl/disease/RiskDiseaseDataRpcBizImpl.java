package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseNameQuery;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.PfLocalMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/16
 */
@Service
@Slf4j
public class RiskDiseaseDataRpcBizImpl implements RiskDiseaseDataRpcBiz {

    @Autowired
    private RiskDiseaseDataRpcDao riskDiseaseDataRpcDao;

    private static final String DISEASE_CLASS_NAME_ALL_PREFIX = "DISEASE_ALL_NAME_PREFIX";

    @Override
    public List<RiskDiseaseData> getByClassNameList(List<String> diseaseNameList) {
        diseaseNameList = diseaseNameList.stream()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(diseaseNameList)) {
            return Lists.newArrayList();
        }
        return riskDiseaseDataRpcDao.getByClassNameList(diseaseNameList);
    }

    @Override
    public List<String> findAllDiseaseNameCache() {
        List<String> nameList = PfLocalMapUtil.get(DISEASE_CLASS_NAME_ALL_PREFIX);
        if (nameList != null) {
            log.info("diseaseClassName from local cache:{}", DISEASE_CLASS_NAME_ALL_PREFIX);
            return nameList;
        }
        nameList = riskDiseaseDataRpcDao.findAllDiseaseName();
        if (CollectionUtils.isNotEmpty(nameList)) {
            PfLocalMapUtil.put(DISEASE_CLASS_NAME_ALL_PREFIX, nameList, RedissonHandler.TEN_MINUTES);
        }
        return nameList;
    }

    @Override
    public Page<String> findByDiseaseName(DiseaseNameQuery diseaseNameQuery) {
        return PageHelper.startPage(diseaseNameQuery.getPageNo(), diseaseNameQuery.getPageSize())
                .doSelectPage(() ->  riskDiseaseDataRpcDao.findByDiseaseName(diseaseNameQuery.getDiseasNameKeyWord()));
    }

    @Override
    public List<String> findByDiseaseName(String diseaseNameKeyWord) {
        return riskDiseaseDataRpcDao.findByDiseaseName(diseaseNameKeyWord);
    }
}
