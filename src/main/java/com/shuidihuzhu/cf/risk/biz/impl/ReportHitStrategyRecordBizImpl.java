package com.shuidihuzhu.cf.risk.biz.impl;

import com.shuidihuzhu.cf.risk.biz.ReportHitStrategyRecordBiz;
import com.shuidihuzhu.cf.risk.dao.ReportHitStrategyRecordDao;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-11-05 20:35
 **/
@Service
public class ReportHitStrategyRecordBizImpl implements ReportHitStrategyRecordBiz {
    @Autowired
    private ReportHitStrategyRecordDao reportHitStrategyRecordDao;

    @Override
    public int batchInsert(List<ReportHitStrategyRecord> reportHitStrategyRecordList) {
        return reportHitStrategyRecordDao.batchInsert(reportHitStrategyRecordList);
    }

    @Override
    public List<ReportHitStrategyRecord> getByReportIds(List<Integer> reportIds) {
        return reportHitStrategyRecordDao.getByReportIds(reportIds);
    }

    @Override
    public List<ReportHitStrategyRecord> listByCursor(long lastId, int limit){
        return reportHitStrategyRecordDao.listByCursor(lastId, limit);
    }

    @Override
    public int updateEncryptById(long id, String encryptMobile, String encryptReportMobile, String encryptIdCard) {
        return reportHitStrategyRecordDao.updateEncryptById(id, encryptMobile, encryptReportMobile, encryptIdCard);
    }
    @Override
    public int updateMobileById(long id, String mobile, String reportMobile, String idCard) {
        return reportHitStrategyRecordDao.updateMobileById(id, mobile, reportMobile, idCard);
    }
}
