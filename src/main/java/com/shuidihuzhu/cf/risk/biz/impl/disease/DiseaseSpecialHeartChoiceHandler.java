package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Service
@Slf4j
public class DiseaseSpecialHeartChoiceHandler implements DiseaseSpecialHandler {


    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;

    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //处理特殊规则的疾病
        SpecialDiseaseRule mainRule = specialDiseaseRuleBiz.getByClassNameCache(DiseaseCommonConfig.HEART_MERGE_A);
        if (mainRule == null) {
            log.warn("specialDiseaseRule is null");
            return Lists.newArrayList();
        }
        //获取肿瘤剩余的疾病
        List<String> filterDiseaseName = filterSpecialDisease(diseaseNameList,
                mainRule.getDiseaseContainClassNameList());
        if (filterDiseaseName.size() < mainRule.getAriseCount()){
            return Lists.newArrayList();
        }
        SpecialDiseaseRule secondRule =specialDiseaseRuleBiz.getByClassNameCache(DiseaseCommonConfig.HEART_MERGE_B);
        //是否包含
        List<String> secondFilterDiseaseName = filterSpecialDisease(diseaseNameList,
                secondRule.getDiseaseContainClassNameList());
        if (secondFilterDiseaseName.size() < filterDiseaseName.size()) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(DiseaseCommonConfig.HEART_MERGE_B);
    }
}
