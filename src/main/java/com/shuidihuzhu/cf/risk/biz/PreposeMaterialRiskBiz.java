package com.shuidihuzhu.cf.risk.biz;

import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.model.risk.CfPreposeMaterialRiskVo;

/**
 * <AUTHOR>
 * @date 2020-05-14
 **/
public interface PreposeMaterialRiskBiz {

    /**
     * 前置报备高风险策略验证
     * @param info
     * @return
     */
    CfPreposeMaterialRiskVo markRisk(PreposeMaterialModel.MaterialInfoVo info);

    /**
     * 增信高风险策略验证
     * @param insuranceInfo
     * @return
     */
    CfPreposeMaterialRiskVo markPropertyRisk(CfPropertyInsuranceInfoModel insuranceInfo);

}
