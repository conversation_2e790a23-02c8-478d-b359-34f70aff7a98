package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
@Slf4j
public class DiseaseMergeExecuteModel  extends ExecuteStrategyModelTemplate {

    @Autowired
    private DiseaseRpcService diseaseRpcService;

    @Override
    public String getCode() {
        return "disease_merge";
    }

    @Override
    public boolean preDecide() {
        return true;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                                                DiseaseStrategyResponse diseaseStrategyResponse,
                                                RiskExecuteStrategyTransit transit) {
        List<DiseaseClassifyVOV2> classifyVoV2List = diseaseRpcService.diseaseNorm(diseaseStrategyRequest.getDiseaseNameList());
        transit.setDiseaseClassifyVOV2List(classifyVoV2List);
        boolean isMergeNull = CollectionUtils.isEmpty(classifyVoV2List) || CollectionUtils.isEmpty(transit.getMergeResult());
        //如果归一结果为空
        int strategyEnum =  diseaseStrategyRequest.getExecuteStrategyEnum();
        if (isMergeNull) {
            transit.setCaseRaiseType(null);
            diseaseStrategyResponse.setDiseaseRaiseStrategyResult(resultInfo(getResultEnum(strategyEnum)));
            transit.setHaNext(false);
        }
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        switch (diseaseStrategyEnum) {
            case USER_WRITE:
                return DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.WRITE_DISEASE_NOT_MERGE_RULE;
            case MANUAL_WRITE:
            case OCR:
                return DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.WRITE_DISEASE_NOT_MERGE_RULE;
            default:
                return null;
        }
    }


}
