package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialTumourChoiceHandler implements DiseaseSpecialHandler {


    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;
    @Autowired
    private RiskDiseaseDataRpcBiz diseaseDataRpcBiz;

    private static final String TUMOUR_KEY = "disease_special_heart_rule";

    private List<String> tumourClassNameList;

    @Value("${disease.special.tumour-class-name:肿瘤}")
    public void setTumourClassNameList(String tumourClassName) {
        log.info("tumourClassName:{}", tumourClassName);
        if (StringUtils.isBlank(tumourClassName)){
            return;
        }
        this.tumourClassNameList = Splitter.on(",").splitToList(tumourClassName);
    }


    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit,
                                           List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //处理特殊规则的疾病
        List<SpecialDiseaseRule> specialDiseaseRules = specialDiseaseRuleBiz.findCacheByClassNameList(TUMOUR_KEY, tumourClassNameList);
        if (CollectionUtils.isEmpty(specialDiseaseRules)) {
            return Lists.newArrayList();
        }
        List<String> needChoice = Lists.newArrayList();
        for (SpecialDiseaseRule specialDiseaseRule : specialDiseaseRules) {
            needChoice.addAll(filterTumourRule(specialDiseaseRule, diseaseNameList));
        }
        return needChoice;
    }


    private List<String> filterTumourRule(SpecialDiseaseRule specialDiseaseRule,
                                          List<String> diseaseNameList) {
        //获取肿瘤剩余的疾病
        List<String> filterDiseaseName = filterSpecialDisease(diseaseNameList,
                specialDiseaseRule.getDiseaseContainClassNameList());
        if (log.isDebugEnabled()){
            log.debug("DiseaseSpecialTumourHandler diseaseNameList:{} filterDiseaseName:{}", JSON.toJSONString(diseaseNameList), JSON.toJSONString(filterDiseaseName));
        }
        //如果肿瘤为空直接进行下一个
        if (filterDiseaseName.size() < specialDiseaseRule.getAriseCount()) {
            return Lists.newArrayList();
        }
        log.info("filterDiseaseName:{}", JSON.toJSONString(filterDiseaseName));
        //判断是否是需要分期的病
        List<RiskDiseaseData> riskDiseaseData = diseaseDataRpcBiz.getByClassNameList(filterDiseaseName);
        //将需要选择的病返回
        return riskDiseaseData.stream().filter(v -> v.getChoiceType() > 0)
                .map(RiskDiseaseData::getDiseaseClassName).collect(Collectors.toList());
    }


}
