package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseSpecialHandler;
import com.shuidihuzhu.cf.risk.biz.disease.SpecialDiseaseRuleBiz;
import com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.PfLocalMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/10
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseSpecialFractureHandler implements DiseaseSpecialHandler {

    @Value("${disease.special.fracture-class-name:骨折}")
    private String fractureClassName;
    @Autowired
    private SpecialDiseaseRuleBiz specialDiseaseRuleBiz;

    @Override
    public List<String> dealSpecialDisease(InfoReasonableAmountResultVo resultVo,
                                           List<String> diseaseNameList,
                                           int diseaseJudgeLimit, List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialChoiceInfoVos) {
        //获取骨折对应的病种
        List<String> fractureClassNameList = getFractureClassNameList();
        //获取骨折对应的病种数量
        List<String> filterClassNameList = filterSpecialDisease(Lists.newArrayList(diseaseNameList),
                fractureClassNameList);
        if (CollectionUtils.isEmpty(filterClassNameList)){
            return Lists.newArrayList();
        }
        return filterClassNameList;
    }


    /**
     * @return 获取骨折对应的全部病种
     */
    private List<String> getFractureClassNameList() {
        List<String> classNameList = PfLocalMapUtil.get(getKey(fractureClassName));
        if (CollectionUtils.isNotEmpty(classNameList)) {
            return classNameList;
        }
        SpecialDiseaseRule specialDiseaseRule =
                specialDiseaseRuleBiz.getByClassName(fractureClassName);
        classNameList = specialDiseaseRule.getDiseaseContainClassNameList();
        if (CollectionUtils.isNotEmpty(classNameList)) {
            PfLocalMapUtil.put(getKey(fractureClassName), classNameList, RedissonHandler.ONE_HOUR);
        }
        return classNameList;
    }


    public static void main(String[] args) {
        System.out.println("呼吸衰竭、多器官功能衰竭、心衰、肾功能衰竭".replace("、",","));
    }


}
