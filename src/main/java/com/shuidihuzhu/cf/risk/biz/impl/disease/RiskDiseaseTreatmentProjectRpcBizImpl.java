package com.shuidihuzhu.cf.risk.biz.impl.disease;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 */
@Service
public class RiskDiseaseTreatmentProjectRpcBizImpl implements RiskDiseaseTreatmentProjectRpcBiz {

    @Autowired
    private RiskDiseaseTreatmentProjectRpcDao treatmentProjectRpcDao;

    @Override
    public List<RiskDiseaseTreatmentProject> findByDiseaseId(List<Long> diseaseIds) {
        if (CollectionUtils.isEmpty(diseaseIds)) {
            return Lists.newArrayList();
        }
        return treatmentProjectRpcDao.findByDiseaseIdList(diseaseIds);
    }

    @Override
    public List<RiskDiseaseTreatmentProject> findById(List<Long> treatmentIds) {
        if (CollectionUtils.isEmpty(treatmentIds)) {
            return Lists.newArrayList();
        }
        return treatmentProjectRpcDao.findById(treatmentIds);
    }
}
