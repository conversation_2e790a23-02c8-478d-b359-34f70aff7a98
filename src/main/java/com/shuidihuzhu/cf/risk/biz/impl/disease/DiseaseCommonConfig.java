package com.shuidihuzhu.cf.risk.biz.impl.disease;

/**
 * 用于保存疾病库 一些常规的不会变化的字段 做统一收口
 * <AUTHOR>
 * @date 2020/6/7
 */
public class DiseaseCommonConfig {

    //选择项相关的一些变量

    /**
     * 选项名称
     */
    public static final String KNOW_CHOICE_NAME = "知道";

    /**
     * 选项名称
     */
    public static final String NO_KNOW_CHOICE_NAME = "不知道";

    /**
     * 先心病选项名称
     */
    public static final String HEART_AGE_CHOICE_NAME = "患者年龄";
    public static final String AGE_LESS_THAN_RANGE_CHOICE_NAME = "小于等于7岁";
    public static final String AGE_MORE_THAN_RANGE_CHOICE_NAME = "大于7岁";


    /**
     * 特殊疾病名称
     */
    public static final String BURN_NAME = "烧伤";

    public static final String HEART_NAME = "先天性心脏病";

    public static final String HEART_MERGE_A = "早产儿Merge";
    public static final String HEART_MERGE_B = "先天性心脏病Merge2";
    public static final String HEART_MERGE_3 = "先天性心脏病Merge3";

}
