package com.shuidihuzhu.cf.risk.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseCityInfoVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.*;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.AuthenticityCarInfo;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.CommercialCar;
import com.shuidihuzhu.cf.client.material.model.authenticity.car.NonCommercialCar;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.OwnershipEnum;
import com.shuidihuzhu.cf.client.material.model.authenticity.house.*;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.enums.ToolIconCodeEnum;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskAutoEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.CityThreshold;
import com.shuidihuzhu.cf.risk.model.highrisk.ComputingToolRule;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDO;
import com.shuidihuzhu.cf.risk.model.highrisk.RiskRuleResult;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.cf.risk.model.risk.CalculateProcess;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.cf.risk.model.risk.DeductionDetail;
import com.shuidihuzhu.cf.risk.model.risk.DiscountValueInfo;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.risk.service.RiskStrategyService;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CardMsgVO;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class DiseaseComputingToolStrategy {

    private Map<String, ComputingToolRule> ruleCodeResultMap;

    @Resource
    private RiskStrategyService riskStrategyService;

    @Data
    public static class Config {

        private List<ComputingToolRule> ruleResultList;
    }

    @Value("${apollo.high-risk-tool.config-json:{}}")
    public void setConfigJson(String configJson) {
        log.info("DiseaseComputingToolStrategy configJson v2 :{}", configJson);
        Config config = JSON.parseObject(configJson, Config.class);
        ruleCodeResultMap = config.getRuleResultList().stream().collect(Collectors.toMap(ComputingToolRule::getRuleCode, Function.identity()));
    }


    public ComputingToolResult exec(ToolJudgeContext judgeContext) {
        final Set<String> hitCodes = promoteRule(judgeContext);
        log.info("DiseaseComputingToolStrategy exec context: {}, hitCodes:{}", judgeContext, hitCodes);
        return promoteResult(hitCodes, judgeContext);
    }

    public ComputingToolResult promoteResult(Set<String> hitCodes, ToolJudgeContext judgeContext) {
        ComputingToolResult result = new ComputingToolResult();
        List<String> hitStrategy = Lists.newArrayList();

        // 扣减项明细
        result.setHitStrategy(hitStrategy);
        DeductionDetail deductionDetail = new DeductionDetail();
        deductionDetail.setHighValueHouse(judgeContext.getHighValueHouse());
        deductionDetail.setIdleHouse(judgeContext.getIdleHouse());
        deductionDetail.setNonBusinessCar(judgeContext.getNonBusinessCar());
        result.setDeductionDetail(deductionDetail);

        int maxSystemDiseaseCost = long2Int(judgeContext.getRequestParam().getMaxSystemDiseaseCost());

        // 目标金额计算过程
        CalculateProcess calculateProcess = new CalculateProcess();
        calculateProcess.setMaxSystemDiseaseCost(maxSystemDiseaseCost);
        calculateProcess.setMinSystemDiseaseCost(long2Int(judgeContext.getRequestParam().getMinSystemDiseaseCost()));
        calculateProcess.setHighValueHouse(judgeContext.getCoreHouse_highValueHousingDeduction());
        calculateProcess.setIdleHouse(judgeContext.getCoreHouse_idleHousingNetAssetsTotal());
        calculateProcess.setNonBusinessCar(judgeContext.getCoreCar_nonBusiness_totalNetValue());
        calculateProcess.setFinancialAssets(judgeContext.getFinancialAssetAmount());
        calculateProcess.setAccidentCompensation(judgeContext.getAccidentCompensationAmountPaid());
        calculateProcess.setInsuranceCompensation(judgeContext.getPersonalInsuranceAmountPaid());
        calculateProcess.setOtherChannelRelief((int) (judgeContext.getOtherChannelReliefAmount()));


        // 可发目标金额计算结果
        int calculateResult = maxSystemDiseaseCost - judgeContext.getCoreFamilyTotalAssets();

        calculateProcess.setTargetAmount(calculateResult);

        if(CollectionUtils.isEmpty(hitCodes)) {
            result.setIconCode(ToolIconCodeEnum.CAN_RAISE.getCode());
            result.setResultTitle(String.format("计算结果：可发，目标金额最高%s万", formatDouble((double) calculateProcess.getTargetAmount() / 10000)));
        }

        for (String hitCode : hitCodes) {
            ComputingToolRule riskRuleResult = ruleCodeResultMap.get(hitCode);
            if (riskRuleResult == null) {
                log.info("此策略已删除 code {}", hitCode);
                continue;
            }
            if(riskRuleResult.getRaiseType() == ToolIconCodeEnum.CAN_NOT_RAISE.getCode()){
                result.setIconCode(ToolIconCodeEnum.CAN_NOT_RAISE.getCode());
                result.setResultTitle("计算结果：不可发");
            }else if(result.getIconCode() != ToolIconCodeEnum.CAN_NOT_RAISE.getCode()
                    && riskRuleResult.getRaiseType() == ToolIconCodeEnum.NEED_ADJUST.getCode()){
                result.setIconCode(ToolIconCodeEnum.NEED_ADJUST.getCode());
                result.setResultTitle(String.format("计算结果：可发，目标金额需调整至最高%s万", formatDouble((double) calculateProcess.getTargetAmount() / 10000)));
            }else if(result.getIconCode() != ToolIconCodeEnum.CAN_NOT_RAISE.getCode()
                    && result.getIconCode() != ToolIconCodeEnum.NEED_ADJUST.getCode()){
                result.setIconCode(ToolIconCodeEnum.CAN_RAISE.getCode());
                result.setResultTitle(String.format("计算结果：可发，目标金额最高%s万", formatDouble((double) calculateProcess.getTargetAmount() / 10000)));
            }

            // 命中策略
            if(StringUtils.isNotBlank(riskRuleResult.getMsg())){
                hitStrategy.add(handleMsg(riskRuleResult.getMsg(), judgeContext)+"("+riskRuleResult.getRuleCode()+")");
            }
        }

        result.setRemark(String.format("原目标金额%s万，经过系统计算，可发目标金额上限为%s万",
                formatDouble((double) judgeContext.getRequestParam().getTargetAmount() / 10000),
                formatDouble((double) calculateProcess.getTargetAmount() / 10000)));
        result.setCalculateProcess(calculateProcess);

        return result;
    }

    public Set<String> promoteRule(ToolJudgeContext judgeContext) {
        final FamilyFinancialSituation infoModel = judgeContext.getRequestParam();
        HashSet<String> hitCodes = Sets.newHashSet();

        Integer targetAmount = judgeContext.getTargetAmount();

        // 真实性数据
        final AuthenticityIndicator authenticityIndicator = infoModel.getAuthenticityIndicator();

        // 是否有真实性信息
        final boolean isAuthentic = authenticityIndicator != null;
        if (!isAuthentic) {
            log.error("没有真实性信息");
            return hitCodes;
        }

        // 上下文计算
        processContext(judgeContext, authenticityIndicator, infoModel);

        // 策略计算 -----------------------------------
        /*
        任意一套房产净值大于等于房产阈值
And
疾病花费<(该房产净值-该房产阈值*30%）
         */
        final boolean isAnyOneHouseOverThresholdX = judgeContext.isCoreHouse_hasAnyOneOverThresholdX();
        if (isAnyOneHouseOverThresholdX) {
            hit(hitCodes, "HRM1029");
        }


        /*
        "闲置房产总净值>=疾病花费
         */
        if (judgeContext.getCoreHouse_idleHousingNetAssetsTotal() >= targetAmount) {
            hit(hitCodes, "HRM1030");
        }

        /*
        "非营生车辆总净值>=疾病花费
         */
        if (judgeContext.getCoreCar_nonBusiness_totalNetValue() >= targetAmount) {
            hit(hitCodes, "HRM1031");
        }

        /*
        "年净收入>=疾病花费
         */
        if (judgeContext.getCoreIncomeYearNet() >= targetAmount) {
            hit(hitCodes, "HRM1032");
        }

        /*
        "（高价值房产扣减+闲置房产总净值+非营生车产总净值+金融资产+事故已赔付金额-人身险已赔付金额+其他渠道救助）>= 疾病花费
         */
        if (judgeContext.getCoreFamilyTotalAssets() >= targetAmount) {
            hit(hitCodes, "HRM1033");
        }


        /*
        "疾病花费-（高价值房产扣减-闲置房产总净值-金融资产-事故&保险赔付-其他渠道救助-非营生车产总净值）<5000元
         */
        if (targetAmount - judgeContext.getCoreFamilyTotalAssets() < 5000) {
            hit(hitCodes, "HRM1042");
        }

        /*
        高价值房产扣减项>0
And
高价值房产扣减项<=疾病花费
         */
        if (judgeContext.getCoreHouse_highValueHousingDeduction() > 0
                && judgeContext.getCoreHouse_highValueHousingDeduction() <= targetAmount) {
            hit(hitCodes, "HRM1043");
        }

        /*
        闲置房产总净值>0
         */
        if (judgeContext.getCoreHouse_idleHousingNetAssetsTotal() > 0) {
            hit(hitCodes, "HRM1044");
        }

        /*
        非营生车总净值>0
         */
        if (judgeContext.getCoreCar_nonBusiness_totalNetValue() > 0) {
            hit(hitCodes, "HRM1045");
        }

        /*
        金融资产>0
         */
        if(judgeContext.getFinancialAssetAmount() > 0) {
            hit(hitCodes, "HRM1046");
        }

        /*
        事故已赔付金额>0
         */
        if (judgeContext.getAccidentCompensationAmountPaid() > 0) {
            hit(hitCodes, "HRM1047");
        }

        /*
        其他渠道救助金额>0
         */
        if (judgeContext.getOtherChannelReliefAmount() > 0) {
            hit(hitCodes, "HRM1048");
        }

        /*
        人身险已赔付金额>0
         */
        if (judgeContext.getPersonalInsuranceAmountPaid() > 0) {
            hit(hitCodes, "HRM1049");
        }


        return hitCodes;
    }

    private void processContext(ToolJudgeContext judgeContext, AuthenticityIndicator authenticityIndicator, FamilyFinancialSituation infoModel) {

        // 核心家庭经济情况
        final PatientFinancialCondition coreHomeFinancial = authenticityIndicator.getPatientFinancialCondition();
        // 患者保险保障状况
        final InsuranceSituation insuranceSituation = authenticityIndicator.getInsuranceSituation();
        // 患者家庭经济状况
        processPatientFinancialCondition(judgeContext, coreHomeFinancial);

        // 非营生车
        processNonBusinessCar(judgeContext, coreHomeFinancial);

        // 家庭年收入
        final int familyIncome = intDef(coreHomeFinancial.getFamilyIncome());
        // 家庭年支出
        final int familyCost = intDef(coreHomeFinancial.getFamilyCost());
        // 家庭净收入=家庭年收入-家庭年支出
        judgeContext.setCoreIncomeYearNet(familyIncome - familyCost);

        // 金融资产金额
        judgeContext.setFinancialAssetAmount(intDef(coreHomeFinancial.getFinancialAssetsSum()));
        // 其他渠道救助金额
        judgeContext.setOtherChannelReliefAmount(str2Double(insuranceSituation.getOtherAssistanceAmount()));
        // 事故赔付金额
        judgeContext.setAccidentCompensationAmountPaid(long2Int(infoModel.getAccidentPaidAmount()));
        // 人身险赔付金额
        judgeContext.setPersonalInsuranceAmountPaid(long2Int(infoModel.getPersonalPaidAmount()));
        // 核心家庭综合资产(高价值房产扣减+闲置房产总净值+非营生车产总净值+金融资产+事故已赔付金额+人身险已赔付金额+其他渠道救助)
        judgeContext.setCoreFamilyTotalAssets(judgeContext.getCoreHouse_highValueHousingDeduction()
                + judgeContext.getCoreHouse_idleHousingNetAssetsTotal()
                + judgeContext.getCoreCar_nonBusiness_totalNetValue()
                + judgeContext.getFinancialAssetAmount()
                + judgeContext.getAccidentCompensationAmountPaid()
                + judgeContext.getPersonalInsuranceAmountPaid()
                + (int)judgeContext.getOtherChannelReliefAmount());
    }

    private int long2Int(Long target) {
        return target == null ? 0 : target.intValue();
    }

    /**
     * 非营生车
     */
    private void processNonBusinessCar(ToolJudgeContext judgeContext, PatientFinancialCondition coreHomeFinancial) {
        // 新版车产信息
        AuthenticityCarInfo authenticityCarInfo = coreHomeFinancial.getAuthenticityCarInfo();
        if (authenticityCarInfo == null || authenticityCarInfo.getCarCount() <= 0) {
            return;
        }
        List<AuthenticityCarInfo.CarDetail> carDetails = authenticityCarInfo.getCarDetails();
        if (CollectionUtils.isEmpty(carDetails)) {
            return;
        }
        int index = 1;
        // 非营生车扣减明细
        List<DiscountValueInfo> nonBusinessCar = Lists.newArrayList();
        for(AuthenticityCarInfo.CarDetail carDetail : carDetails) {
            // 忽略车产类型为“营生车”的车产
            Integer carType = carDetail.getIsCommercialVehicle();
            if (carType == null || carType != 0) {
                index++;
                continue;
            }
            // 忽略“车产价值”小于等于5万的车产
            int carPrice = intDef(carDetail.getCarMarketValue());
            if(carPrice <= 5_0000) {
                index++;
                continue;
            }
            // 车产贷款为空时，按照无贷款 0 来计算
            int loanAmount = intDef(carDetail.getLoanAmount());
            int carNetValue = carPrice - loanAmount;
            if(carNetValue < 0){
                index++;
                continue;
            }
            // 总净值计算
            judgeContext.setCoreCar_nonBusiness_totalNetValue(
                    judgeContext.getCoreCar_nonBusiness_totalNetValue() + carNetValue);
            // 非营生车扣减项明细
            DiscountValueInfo discountValueInfo = getDiscountValueInfo(carNetValue,
                    null, null, "非营生车" + index, 0,
                    carPrice, carDetail.getCarBrand(), null, null);
            nonBusinessCar.add(discountValueInfo);
            index++;
        }
        judgeContext.setNonBusinessCar(nonBusinessCar);
    }

    private int intDef(Integer target) {
        return intDef(target, 0);
    }

    private int intDef(Integer target, int def) {
        return target == null ? def : target;
    }

    private void processPatientFinancialCondition(ToolJudgeContext judgeContext,
                                                  PatientFinancialCondition patientFinancialCondition) {
        if (patientFinancialCondition == null) {
            return;
        }

        // 自建房信息
        final SelfBuiltHouseInfo selfBuiltHouseInfo = patientFinancialCondition.getSelfBuiltHouseInfo();
        processSelfBuiltHouseInfo(judgeContext, selfBuiltHouseInfo);

        // 商品房信息
        final CommodityHouseInfo commodityHouseInfo = patientFinancialCondition.getCommodityHouseInfo();
        processCommodityHouseInfo(judgeContext, commodityHouseInfo);

        // 其他房产信息
        final OtherHouseInfo otherHouseInfo = patientFinancialCondition.getOtherHouseInfo();
        processOtherHouseInfo(judgeContext, otherHouseInfo);

    }

    /**
     * 其他房产
     */
    private void processOtherHouseInfo(ToolJudgeContext judgeContext, OtherHouseInfo otherHouseInfo) {
        if (otherHouseInfo == null) {
            return;
        }
        // 累加其他房产数量
        final int houseCount = intDef(otherHouseInfo.getHouseCount());
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);
        final List<OtherHouseInfo.OtherHouseDetail> otherHouseDetails = otherHouseInfo.getOtherHouseDetails();
        if (CollectionUtils.isEmpty(otherHouseDetails)) {
            return;
        }
        int index = 0;
        for (OtherHouseInfo.OtherHouseDetail otherHouseDetail : otherHouseDetails) {
            if (otherHouseDetail == null) {
                index++;
                continue;
            }
            final int ownershipRatioType = intDef(otherHouseDetail.getOwnershipRatioType());
            double ownershipRatio = str2Double(otherHouseDetail.getOwnershipRatio());
            /*
          - 其他房产产权比例
          - 全部：按100%算
          - 部分：按输入值算
          - 不清楚占比：按100%算
             */
            if (ownershipRatioType == OwnershipEnum.ALL.getCode() ||
                    ownershipRatioType == OwnershipEnum.UNCLEAR.getCode()) {
                ownershipRatio = 100D;
            }
            final String tag = "其他房产";
            processCoreHouse(judgeContext, otherHouseDetail, ownershipRatio, tag, ++index);
        }
    }

    /**
     * 商品房
     */
    private void processCommodityHouseInfo(ToolJudgeContext judgeContext, CommodityHouseInfo commodityHouseInfo) {
        if (commodityHouseInfo == null) {
            return;
        }
        // 累加商品房数量
        final int houseCount = intDef(commodityHouseInfo.getHouseCount());
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);

        final List<CommodityHouseInfo.CommodityHouseDetail> commodityHouseDetails =
                commodityHouseInfo.getCommodityHouseInfos();
        int index = 0;
        for (CommodityHouseInfo.CommodityHouseDetail commodityHouseDetail : commodityHouseDetails) {
            if (commodityHouseDetail == null) {
                index++;
                continue;
            }
            processCoreHouse(judgeContext, commodityHouseDetail, 100D,
                    "商品房", ++index);
        }
    }

    /**
     * 自建房
     */
    private void processSelfBuiltHouseInfo(ToolJudgeContext judgeContext, SelfBuiltHouseInfo selfBuiltHouseInfo) {
        final Integer houseCount = intDef(selfBuiltHouseInfo.getHouseCount());
        // 累加自建房数量
        judgeContext.setCoreHouseTotal(judgeContext.getCoreHouseTotal() + houseCount);
        final List<SelfBuiltHouseInfo.SelfBuiltHouseDetail> selfBuiltHouseDetails =
                selfBuiltHouseInfo.getSelfBuiltHouseDetails();

        if(CollectionUtils.isEmpty(selfBuiltHouseDetails) || houseCount <= 0){
            return;
        }
        int index = 1;
        for (SelfBuiltHouseInfo.SelfBuiltHouseDetail selfBuiltHouseDetail : selfBuiltHouseDetails) {
            if (selfBuiltHouseDetail == null) {
                index++;
                continue;
            }
            int houseMarkerValue = intDef(selfBuiltHouseDetail.getHouseMarketValue());
            int houseNetValue = 0;
            int cityAmountThreshold = 0;
            if (!checkNumNotNullNotZero(houseMarkerValue)) {
                index++;
                continue;
            }
            // 自建房房产价值低于50万时，房产阈值按50万计算
            ExtraInfo extraInfo = selfBuiltHouseDetail.getExtraInfo();
            HouseLocation houseLocation = extraInfo.getHouseLocation();
            if(houseMarkerValue < 500000){
                cityAmountThreshold = 500000;
            }else{
                // 按照填写的地区计算房产阈值
                cityAmountThreshold = processSelfCityThreshold(selfBuiltHouseDetail.getExtraInfo().getHouseLocation());
            }

            // - 0套或房产价值<=20万：房产净值为空
            if (houseCount == 0 || houseMarkerValue <= 200000) {
                houseNetValue = 0;
            } else {
            //  - >=1套且房产价值>20万：房产净值=当前市场价值
            houseNetValue = houseMarkerValue;
        }
            processCoreHouse(judgeContext, houseNetValue, cityAmountThreshold, "自建房", index, extraInfo,
                    houseNetValue, houseLocation, houseMarkerValue);
        }
    }

    private void processCoreHouse(ToolJudgeContext judgeContext,
                                  GeneralHouseDetail otherHouseDetail,
                                  double ownershipRatio,
                                  String tag,
                                  int index) {
        //   - 当“市场价值”为空时，不参与计算，该套房产的房产净值为空；
        if(otherHouseDetail.getHouseValue() == null){
            return;
        }
        final int houseValue = intDef(otherHouseDetail.getHouseValue());
        final HouseLoanDetail houseLoanDetail = otherHouseDetail.getHouseLoanDetail();
        // 当“是否有房贷”为“无”，首付金额、贷款年限、已还贷年限、每月还款金额为空时，房贷计为0
        if(houseLoanDetail == null || houseLoanDetail.getHouseLoanTag() == null || houseLoanDetail.getHouseLoanTag() == 0){
            houseLoanDetail.setLoanYear("0");
            houseLoanDetail.setLoanPaidYear("0");
            houseLoanDetail.setLoanMonthRepayment("0");
            houseLoanDetail.setDownPaymentAmount(0);
        }

        // 兜底
        if (ownershipRatio == 0) {
            ownershipRatio = 100D;
        }

        // 根据贷款年限获取贷款年限系数
        final double loanYear = str2Double(houseLoanDetail.getLoanYear());
        // 计算房产净值 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
        final double loanPaidYear = str2Double(houseLoanDetail.getLoanPaidYear());

        final double loanMonthRepayment = str2Double(houseLoanDetail.getLoanMonthRepayment());
        final int houseNetValue = countHouseNetValue(houseValue, loanMonthRepayment,
                ownershipRatio, loanYear, loanPaidYear);

        final Integer cityAmountThreshold = getHouseThreshold(otherHouseDetail.getHouseLocation());

        processCoreHouse(judgeContext, houseNetValue, cityAmountThreshold, tag, index,
                otherHouseDetail.getExtraInfo(), houseValue, otherHouseDetail.getHouseLocation(), null);
    }

    private void processCoreHouse(ToolJudgeContext judgeContext,
                                  int houseNetValue,
                                  Integer cityAmountThreshold,
                                  String tag,
                                  int index,
                                  ExtraInfo extraInfo,
                                  int houseValue,
                                  HouseLocation houseLocation,
                                  Integer selfBuiltHouseMarketValue) {
        String houseName = tag + index;
        if(extraInfo != null && extraInfo.getHouseNature() != null && extraInfo.getHouseNature() == 0 && houseNetValue >= 0){
            // 闲置房产净值
            judgeContext.setCoreHouse_idleHousingNetAssetsTotal(judgeContext.getCoreHouse_idleHousingNetAssetsTotal() + houseNetValue);
            // 闲置房产-扣减项明细
            DiscountValueInfo discountValueInfo = getDiscountValueInfo(houseNetValue, cityAmountThreshold, houseLocation,
                    houseName, 0, houseValue, null, "闲置", selfBuiltHouseMarketValue);
            judgeContext.getIdleHouse().add(discountValueInfo);
        }

        if(extraInfo == null || extraInfo.getHouseNature() == null || extraInfo.getHouseNature() == 0){
            return;
        }
        // 高价值房产-刚需-扣减项
        // 没有阈值 或 房产净值小于阈值，不计算
        if(cityAmountThreshold == null || houseNetValue < cityAmountThreshold){
            return;
        }
        // 任意刚需房产净值>=当地房产阈值的房产时，取【房产净值-房产阈值的30%】
        int needDeduction = (int) (houseNetValue - cityAmountThreshold * 0.3);
        judgeContext.setCoreHouse_highValueHousingDeduction(judgeContext.getCoreHouse_highValueHousingDeduction() + needDeduction);
        // 高价值房产-刚需-扣减项明细
        DiscountValueInfo discountValueInfo = getDiscountValueInfo(houseNetValue, cityAmountThreshold, houseLocation,
                houseName, needDeduction, houseValue, null, "刚需", null);
        judgeContext.getHighValueHouse().add(discountValueInfo);
        // 是否有任意一套刚需房产净值大于等于房产阈值 且 疾病花费小于(该房产净值-该房产阈值*30%）
        if (judgeContext.getTargetAmount() < needDeduction) {
            judgeContext.setCoreHouse_hasAnyOneOverThresholdX(true);
        }
    }

    private DiscountValueInfo getDiscountValueInfo(int houseNetValue, Integer cityAmountThreshold, HouseLocation houseLocation,
                                                   String houseName, int needDeduction, int value, String carBrand,
                                                   String houseNature, Integer selfBuiltHouseMarketValue) {
        DiscountValueInfo discountValueInfo = new DiscountValueInfo();
        discountValueInfo.setBelong("核心家庭");
        discountValueInfo.setName(houseName);
        discountValueInfo.setValue(value);
        discountValueInfo.setNetValue(houseNetValue);
        if(cityAmountThreshold != null){
            discountValueInfo.setHouseThreshold(cityAmountThreshold);
        }
        if(houseLocation != null){
            discountValueInfo.setHouseLocation(getLocationName(houseLocation));
        }
        if(selfBuiltHouseMarketValue != null){
            discountValueInfo.setValue(selfBuiltHouseMarketValue);
        }
        if(needDeduction > 0){
            discountValueInfo.setDiscountAmount(needDeduction);
        }
        if(StringUtils.isNotBlank(carBrand)){
            discountValueInfo.setCarBrand(carBrand);
        }
        if (StringUtils.isNotBlank(houseNature)) {
            discountValueInfo.setHouseNature(houseNature);
        }
        return discountValueInfo;
    }

    private double str2Double(String doubleStr) {
        if (StringUtils.isBlank(doubleStr)) {
            return 0;
        }
        return Double.parseDouble(doubleStr);
    }

    private String getLocationName(HouseLocation houseLocation) {
        String province = houseLocation.getProvince();
        String city = houseLocation.getCity();
        String district = houseLocation.getDistrict();
        if(StringUtils.isBlank(province) || StringUtils.isBlank(city) || StringUtils.isBlank(district)){
            return null;
        }
        return province + "/" + city + "/" + district;
    }


    private Integer getHouseThreshold(HouseLocation houseLocation) {
        final CityParam cityParam = new CityParam();
        cityParam.setProvince(houseLocation.getProvince());
        cityParam.setCity(houseLocation.getCity());
        cityParam.setCounty(houseLocation.getDistrict());
        return riskStrategyService.getCityAmonutThreshold(cityParam) * 10000;
    }

    private boolean checkNumNotNullNotZero(Integer houseAmountInYuan) {
        return houseAmountInYuan != null && houseAmountInYuan != 0;
    }

    private void hit(HashSet<String> hitCodes, String code) {
        hitCodes.add(code);
    }

    /**
     * 计算房产净值 房产净值=市场价值*产权比例-(月供*12*（贷款年限-已还款年限)*贷款年限系数)
     * @param houseValue 市场价值
     * @param loanMonthRepayment 月供
     * @param ownershipRatio 产权比例
     * @param loanYear 贷款年限
     * @param loanPaidYear 已还款年限
     * @return 房产净值 单位元
     */
    private int countHouseNetValue(int houseValue,
                                   double loanMonthRepayment,
                                   double ownershipRatio,
                                   double loanYear,
                                   double loanPaidYear) {
        double loanRatio = getLoanRatio(loanYear);
        int res = (int) (houseValue * ownershipRatio / 100 -
                (loanMonthRepayment * 12 * (loanYear - loanPaidYear) * loanRatio));
        log.info("计算房产净值：市场价值={},月供={},产权比例={},贷款年限={},已还款年限={},房产净值={}",
                houseValue, loanMonthRepayment, ownershipRatio, loanYear, loanPaidYear, res);
        return res;
    }

    /**
     * 贷款年限系数
     * @param loanYear
     * @return
     */
    private double getLoanRatio(Double loanYear) {
        if (loanYear == null) {
            return 0;
        }
        if (loanYear > 0 && loanYear <= 6) {
            return 0.9;
        }
        if (loanYear > 6 && loanYear <= 14) {
            return 0.8;
        }
        if (loanYear > 14 && loanYear <= 21) {
            return 0.7;
        }
        if (loanYear > 21 && loanYear <= 30) {
            return 0.6;
        }
        return 0;
    }

    private String handleMsg(String msg, ToolJudgeContext judgeContext){
        if(StringUtils.isBlank(msg)){
            return "";
        }
        return msg.replaceAll("##coreHouse_highValueHousingDeduction##", formatDouble((double) judgeContext.getCoreHouse_highValueHousingDeduction()/10000))
                .replaceAll("##coreHouse_idleHousingNetAssetsTotal##", formatDouble((double) judgeContext.getCoreHouse_idleHousingNetAssetsTotal()/10000))
                .replaceAll("##coreCar_nonBusiness_totalNetValue##", formatDouble((double) judgeContext.getCoreCar_nonBusiness_totalNetValue()/10000))
                .replaceAll("##coreIncomeYearNet##", formatDouble((double) judgeContext.getCoreIncomeYearNet() / 10000))
                .replaceAll("##coreFamilyTotalAssets##", formatDouble((double) judgeContext.getCoreFamilyTotalAssets() / 10000))
                .replaceAll("##financialAssetAmount##", formatDouble((double) judgeContext.getFinancialAssetAmount()/10000))
                .replaceAll("##accidentCompensationAmountPaid##", formatDouble((double) judgeContext.getAccidentCompensationAmountPaid()/10000))
                .replaceAll("##personalInsuranceAmountPaid##", formatDouble((double) judgeContext.getPersonalInsuranceAmountPaid()/10000))
                .replaceAll("##otherChannelReliefAmount##", formatDouble(judgeContext.getOtherChannelReliefAmount()/10000));
    }

    private int processSelfCityThreshold(HouseLocation houseLocation) {
        if(houseLocation == null){
            log.info("processSelfCityThreshold houseLocation is null");
            return 0;
        }
        CityParam cityParam = new CityParam();
        cityParam.setProvince(houseLocation.getProvince());
        cityParam.setCity(houseLocation.getCity());
        cityParam.setCounty(houseLocation.getDistrict());
        final Integer cityAmountThreshold = riskStrategyService.getCityAmonutThreshold(cityParam);
        return cityAmountThreshold * 10000;
    }

    public String formatDouble(double d){
        BigDecimal bd = BigDecimal.valueOf(d);
        //保留两位数字，并且是截断不进行四舍五入
        return bd.setScale(2, RoundingMode.DOWN).toString();
    }
}

