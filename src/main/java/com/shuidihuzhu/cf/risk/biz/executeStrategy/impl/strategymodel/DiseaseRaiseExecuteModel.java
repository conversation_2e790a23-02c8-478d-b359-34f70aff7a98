package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 判断疾病是否可发起策略
 *
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
public class DiseaseRaiseExecuteModel extends ExecuteStrategyModelTemplate {

    @Autowired
    private DiseaseRpcService diseaseRpcService;
    @Autowired
    private RiskDiseaseDataRpcBiz riskDiseaseDataRpcBiz;
    @Autowired
    private RiskDiseaseTreatmentProjectRpcBiz treatmentProjectRpcBiz;


    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> noRaiseResultMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE);
        }
    };
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> hasSpecialResultMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.NEED_WRITE_SPECIAL_DISEASE);
        }
    };
    private static final Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> canRaiseResultMap = new HashMap<>(){
        {
            put(DiseaseStrategyEnum.USER_WRITE, DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE);
            put(DiseaseStrategyEnum.MANUAL_WRITE, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE);
            put(DiseaseStrategyEnum.OCR, DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE);
        }
    };

    @Override
    public String getCode() {
        return "disease_judge_raise";
    }


    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        //判断是否选择过
        if (StringUtils.isNotBlank(diseaseStrategyRequest.getSpecialRaiseChoiceInfo())){
            return;
        }
        //1. 查询所有疾病的类型
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataRpcBiz.getByClassNameList(transit.getMergeResult());
        //判断是否所有的疾病都不可发
        boolean isAllNotRaise = CollectionUtils.isEmpty(riskDiseaseDataList.stream().filter( v -> v.getRaiseType()
                != RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode()).collect(Collectors.toList()));
        diseaseStrategyResponse.setDiseaseInfoList(buildList(riskDiseaseDataList, transit.getDiseaseClassifyVOV2List()));
        transit.setDiseaseInfoList(diseaseRpcService.buildVoList(riskDiseaseDataList));
        if (isAllNotRaise) {
            transit.setCaseRaiseType(RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE);
            diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                    resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), noRaiseResultMap)));
            if (diseaseStrategyRequest.getExecuteStrategyEnum() == DiseaseStrategyEnum.USER_WRITE.getCode()){
                transit.setHaNext(false);
            }
            return;
        }

        List<RiskDiseaseData> canRaiseList = riskDiseaseDataList.stream().filter( v -> v.getRaiseType()
                == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canRaiseList)){
            transit.setCaseRaiseType(RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE);
            diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                    resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), hasSpecialResultMap)));

            //写入特殊选择项
            diseaseStrategyResponse.getDiseaseRaiseStrategyResult()
                    .setSpecialDiseaseChoiceInfoList(buildChoiceInfo(riskDiseaseDataList.stream().filter( v -> v.getRaiseType()
                            == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()).collect(Collectors.toList())));
            transit.setHaNext(false);
            return;
        }
        transit.setCaseRaiseType(RiskDiseaseData.RaiseTypeEnum.CAN_RAISE);
        //处理特殊疾病
        dealSpecialDisease(diseaseStrategyResponse.getDiseaseInfoList(), riskDiseaseDataList.stream().filter(v -> v.getRaiseType()
                == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()).collect(Collectors.toList()));
        //判断是否包含特殊疾病
        diseaseStrategyResponse.setDiseaseRaiseStrategyResult(
                resultInfo(getMupliteResultEnum(diseaseStrategyRequest.getExecuteStrategyEnum(), canRaiseResultMap)));
    }

    private void dealSpecialDisease(List<DiseaseInfoVo> diseaseInfoList, List<RiskDiseaseData>  riskDiseaseDataList) {
        if (CollectionUtils.isEmpty(riskDiseaseDataList) || CollectionUtils.isEmpty(diseaseInfoList)) {
            return;
        }
        //获取所有特殊可发的疾病的治疗方案
        List<RiskDiseaseTreatmentProject>  treatmentProjects =
                treatmentProjectRpcBiz.findByDiseaseId(riskDiseaseDataList.stream()
                        .map(RiskDiseaseData::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(treatmentProjects)) {
            return;
        }
        Map<Long, List<RiskDiseaseTreatmentProject>> treatmentProjectMap = treatmentProjects.stream().filter(v -> v.getRaiseType() == 1)
                .collect(Collectors.groupingBy(RiskDiseaseTreatmentProject::getDiseaseId));
        Map<String, DiseaseInfoVo> diseaseInfoVoMap = diseaseInfoList.stream().collect(Collectors.toMap(DiseaseInfoVo::getDiseaseName, Function.identity()));
        //写入治疗方案
        for (RiskDiseaseData riskDiseaseData : riskDiseaseDataList){
            List<RiskDiseaseTreatmentProject> currentTreatment = treatmentProjectMap.get(riskDiseaseData.getId());
            if (CollectionUtils.isEmpty(currentTreatment)){
                continue;
            }
            DiseaseInfoVo diseaseInfoVo = diseaseInfoVoMap.get(StringUtils.trimToEmpty(riskDiseaseData.getDiseaseClassName()));
            if (diseaseInfoVo == null){
                continue;
            }
            diseaseInfoVo.setDiseaseType(riskDiseaseData.getRaiseType());
            diseaseInfoVo.setTreatmentInfo(treatmentProjects.stream()
                    .map(v -> v.getProjectName() + "(" + (v.getRaiseType() == 1 ? "可发起":"不可发起") + ")")
                    .collect(Collectors.joining(",")));
        }
    }




    private List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> buildChoiceInfo(List<RiskDiseaseData> specialRaiseList) {
        SpecialDiseaseChoiceInfoVo diseaseChoiceInfoVo = new SpecialDiseaseChoiceInfoVo();
        diseaseRpcService.generateChoiceInfo(diseaseChoiceInfoVo,specialRaiseList);
        return diseaseChoiceInfoVo.getSpecialDiseaseChoiceInfoList();
    }

    /**
     * 生成特殊选项
     * @param riskDiseaseDataList
     * @param diseaseClassifyVOV2List
     * @return
     */
    private List<DiseaseInfoVo> buildList(List<RiskDiseaseData> riskDiseaseDataList, List<DiseaseClassifyVOV2> diseaseClassifyVOV2List) {
        return diseaseClassifyVOV2List.stream().map( v ->{
            DiseaseInfoVo diseaseInfoVo = new DiseaseInfoVo();
            diseaseInfoVo.setDiseaseName(v.getDisease());
            List<RiskDiseaseData> diseaseDataList = riskDiseaseDataList.stream().filter( diseaseData ->
                    v.getNorm().contains(diseaseData.getDiseaseClassName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(diseaseDataList)){
                diseaseInfoVo.setDiseaseType(RiskDiseaseData.RaiseTypeEnum.DEFAULT.getCode());
                return diseaseInfoVo;
            }
            RiskDiseaseData.RaiseTypeEnum currentType = null;
            for (RiskDiseaseData riskDiseaseData : diseaseDataList) {
                if (riskDiseaseData.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()){
                    currentType = RiskDiseaseData.RaiseTypeEnum.CAN_RAISE;
                    break;
                } else if (riskDiseaseData.getRaiseType() == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()) {
                    currentType = RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE;
                }
            }
            diseaseInfoVo.setDiseaseType(currentType == null ?
                    RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode() : currentType.getCode());
            return diseaseInfoVo;
        }).collect(Collectors.toList());
    }


    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }



    @Override
    public boolean preDecide() {
        return true;
    }
}
