package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.diseaseAmountModel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.subject.env.EnvHelper;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.biz.disease.DiseaseAmountCallLogBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.delegate.PrePoseMaterialDelegate;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseAmountReasonableLimitConfig;
import com.shuidihuzhu.cf.risk.model.disease.DiseaseDecideContext;
import com.shuidihuzhu.cf.risk.model.disease.PrePoseSpecialDiseaseChoiceInfo;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.DiseaseDecideAmountFacade;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import com.shuidihuzhu.cf.risk.service.diseasefix.DiseaseFixService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: wangpeng
 * @Date: 2021/12/29 11:01
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class DiseaseAmountReasonableHighModel extends ExecuteStrategyModelTemplate {

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Resource
    private PreposeMaterialClient preposeMaterialClient;
    @Resource
    private DiseaseRpcService diseaseRpcService;
    @Resource
    private DiseaseAmountCallLogBiz diseaseAmountCallLogBiz;
    @Resource
    private CfChannelFeignClient cfChannelFeignClient;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Autowired
    private DiseaseDecideAmountFacade diseaseDecideAmountFacade;

    @Autowired
    private DiseaseFixService diseaseFixService;

    @Resource
    private PrePoseMaterialDelegate prePoseMaterialDelegate;

    @Value("${disease.amount.reasonable-limit.config:{}}")
    private String diseaseAmountReasonableLimitConfig;

    @Override
    public String getCode() {
        return "disease_judge_amount_high_risk_model";
    }

    @Override
    public boolean preDecide() {
        return false;
    }

    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest, DiseaseStrategyResponse diseaseStrategyResponse, RiskExecuteStrategyTransit transit) {
        log.info("diseaseAmountReasonableHighModel is begin req : {}, {}, {}", diseaseStrategyRequest, diseaseStrategyResponse, transit);
        if (diseaseStrategyRequest.getReasonAmountType() == 0) {
            log.info("diseaseAmountReasonableHighModel reasonAmountType is null : {}, {}", diseaseStrategyRequest, transit);
            return;
        }
        if (diseaseStrategyRequest.getExecuteStrategyEnum() == DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()) {
            List<DiseaseClassifyVOV2> diseaseClassifyVOV2s = diseaseRpcService.diseaseNorm(diseaseStrategyRequest.getDiseaseNameList());
            transit.setDiseaseClassifyVOV2List(diseaseClassifyVOV2s);
        }
        if (CollectionUtils.isEmpty(diseaseStrategyRequest.getDiseaseNameList()) || CollectionUtils.isEmpty(transit.getMergeResult())) {
            log.info("diseaseAmountReasonableHighModel diseaseNameList or norm is null:{}", JSON.toJSONString(diseaseStrategyRequest));
            return;
        }
        FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getCaseInfoById(diseaseStrategyRequest.getCaseId());
        if (response.notOk() || Objects.isNull(response.getData())) {
            log.info("diseaseAmountReasonableHighModel crowdfundingInfo is null:{}", JSON.toJSONString(diseaseStrategyRequest));
            return;
        }
        CrowdfundingInfo crowdfundingInfo = response.getData();
        int targetAmount = crowdfundingInfo.getTargetAmount();

        // 构建修正前入参
        String specialDiseaseInfo = getSpecialDiseaseInfo(diseaseStrategyRequest, transit);
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo();
        decideReasonableInfo.setDiseaseNameList(transit.getMergeResult());
        decideReasonableInfo.setExtContent("");
        decideReasonableInfo.setCaseId(diseaseStrategyRequest.getCaseId());
        decideReasonableInfo.setSpecialDiseaseInfo(specialDiseaseInfo);
        decideReasonableInfo.setSpecialRaiseChoiceInfo("");

        final DiseaseDecideContext preFixContext = diseaseDecideAmountFacade.decideInfoByContext(targetAmount, decideReasonableInfo);
        DiseaseDecideContext fixedContext = diseaseFixService.decideByFix(preFixContext, crowdfundingInfo);

        // 插入修正前记录
        final InfoReasonableAmountResultVo preFixContextResult = preFixContext.getResult();
        DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum preFixAmountReasonableEnum = executeRule(preFixContextResult, crowdfundingInfo);
        diseaseAmountCallLogBiz.insertAmountRecord(diseaseStrategyRequest, transit, preFixContextResult,
                decideReasonableInfo, preFixAmountReasonableEnum, targetAmount);

        // 插入修正后记录
        final InfoReasonableAmountResultVo fixedContextResult = fixedContext.getResult();
        DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum fixedAmountReasonableEnum = executeRule(fixedContextResult, crowdfundingInfo);
        diseaseAmountCallLogBiz.insertAmountRecord(diseaseStrategyRequest, transit, fixedContextResult,
                fixedContext.getDecideReasonableInfo(), fixedAmountReasonableEnum, targetAmount, 2);

        // 设置结果
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(resultInfo(fixedAmountReasonableEnum,
                fixedAmountReasonableEnum.getHint() + buildAmountInfo(fixedContextResult), Lists.newArrayList()));
    }

    /**
     * 判断治疗方案
     *
     * @return
     */
    private String getSpecialDiseaseInfo(DiseaseStrategyRequest diseaseStrategyRequest, RiskExecuteStrategyTransit transit) {
        // 代录入的治疗方案
        Map<String, List<SpecialDiseaseChoiceInfoVo.ChoiceInfo>> prePoseCaseChoiceMap = handlePrePoseCaseChoice(diseaseStrategyRequest.getCaseId(), transit);
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> caseChoiceInfo = diseaseRpcService.specialChoiceInfo(transit.getMergeResult(), "").getData().getSpecialDiseaseChoiceInfoList();
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> result = new ArrayList<>();

        for (SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo specialDiseaseChoiceInfo : caseChoiceInfo) {

            List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList = prePoseCaseChoiceMap.get(specialDiseaseChoiceInfo.getSpecialDiseaseName());
            if (CollectionUtils.isNotEmpty(choiceInfoList)) {
                specialDiseaseChoiceInfo.setChoiceInfoList(choiceInfoList);
                result.add(specialDiseaseChoiceInfo);
                continue;
            }
            SpecialDiseaseChoiceInfoVo.ChoiceInfo dontKnow = specialDiseaseChoiceInfo.getChoiceInfoList()
                    .stream()
                    .filter(f -> StringUtils.contains(f.getChoiceName(), "不知道"))
                    .findAny()
                    .orElse(null);
            if (Objects.nonNull(dontKnow)) {
                specialDiseaseChoiceInfo.setChoiceInfoList(Collections.singletonList(dontKnow));
                result.add(specialDiseaseChoiceInfo);
                continue;
            }
            SpecialDiseaseChoiceInfoVo.ChoiceInfo nothing = specialDiseaseChoiceInfo.getChoiceInfoList()
                    .stream()
                    .filter(f -> StringUtils.contains(f.getChoiceName(), "无分期"))
                    .findAny()
                    .orElse(null);
            if (Objects.nonNull(nothing)) {
                specialDiseaseChoiceInfo.setChoiceInfoList(Collections.singletonList(nothing));
                result.add(specialDiseaseChoiceInfo);
            }
        }
        return JSONObject.toJSONString(result);
    }

    private DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum executeRule(InfoReasonableAmountResultVo infoReasonableAmountResultVo, CrowdfundingInfo crowdfundingInfo) {
        double adviseMaxAmount = infoReasonableAmountResultVo.getAdviseMaxAmount();
        int targetAmount = crowdfundingInfo.getTargetAmount();
        if (StringUtils.isEmpty(diseaseAmountReasonableLimitConfig)) {
            return DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.AMOUNT_REASONABLE;
        }
        List<DiseaseAmountReasonableLimitConfig> limitConfigList = JSONObject.parseArray(diseaseAmountReasonableLimitConfig, DiseaseAmountReasonableLimitConfig.class);
        if (CollectionUtils.isEmpty(limitConfigList)) {
            return DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.AMOUNT_REASONABLE;
        }
        long count = limitConfigList.stream()
                .filter(f -> f.getMinAmount() <= targetAmount && f.getMaxAmount() > targetAmount && adviseMaxAmount * 1000000 * f.getMultiple() <= targetAmount)
                .count();
        return count > 0 ? DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.NOT_REASONABLE : DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.AMOUNT_REASONABLE;
    }

    /**
     * 是否代录入发起的案例,并且是否有治疗方案
     *
     * @return
     */
    private Map<String, List<SpecialDiseaseChoiceInfoVo.ChoiceInfo>> handlePrePoseCaseChoice(int caseId, RiskExecuteStrategyTransit riskExecuteStrategyTransit) {
        Map<String, List<SpecialDiseaseChoiceInfoVo.ChoiceInfo>> map = new HashMap<>();
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = prePoseMaterialDelegate.selectLatelyByCaseId(caseId);
        if (Objects.isNull(materialInfoVo)) {
            return map;
        }
        riskExecuteStrategyTransit.setPrePoseId(materialInfoVo.getId());
        String treatmentInfo = StringUtils.isEmpty(materialInfoVo.getTreatmentInfo()) ? materialInfoVo.getRpTreatmentInfo() : materialInfoVo.getTreatmentInfo();
        if (StringUtils.isEmpty(treatmentInfo)) {
            return map;
        }
        JSONObject jsonObject = JSONObject.parseObject(treatmentInfo);
        if (Objects.isNull(jsonObject)) {
            return map;
        }
        List<PrePoseSpecialDiseaseChoiceInfo> prePoseChoiceInfo = JSONObject.parseArray(JSONObject.toJSONString(jsonObject.get("info")), PrePoseSpecialDiseaseChoiceInfo.class);
        if (CollectionUtils.isEmpty(prePoseChoiceInfo)) {
            return map;
        }
        for (PrePoseSpecialDiseaseChoiceInfo preChoice : prePoseChoiceInfo) {
            if (StringUtils.equals(preChoice.getSpecialDiseaseName(), "烧伤")) {
                map.put(preChoice.getSpecialDiseaseName(), preChoice.getChoiceInfoList());
            }
            List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoList = preChoice.getChoiceInfoList();

            List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoShowValueList = choiceInfoList.stream()
                    .filter(f -> StringUtils.isNotEmpty(preChoice.getShowValue()) && Splitter.on(",").splitToList(preChoice.getShowValue()).contains(f.getChoiceName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(choiceInfoShowValueList)) {
                map.put(preChoice.getSpecialDiseaseName(), choiceInfoShowValueList);
                continue;
            }
            Long radioVal = preChoice.getRadioVal();
            if (Objects.nonNull(radioVal)) {
                List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoRadioValList = choiceInfoList.stream()
                        .filter(item -> Objects.equals(item.getChoiceId(), radioVal))
                        .collect(Collectors.toList());
                map.put(preChoice.getSpecialDiseaseName(), choiceInfoRadioValList);
                continue;
            }
            List<String> checkboxVal = preChoice.getCheckboxVal();
            if (CollectionUtils.isNotEmpty(checkboxVal)) {
                List<SpecialDiseaseChoiceInfoVo.ChoiceInfo> choiceInfoCheckboxValList = checkboxVal.stream()
                        .filter(StringUtils::isNotEmpty)
                        .map(m -> choiceInfoList.stream()
                                .filter(item -> StringUtils.isNumeric(m) ? Objects.equals(item.getChoiceId(), Long.valueOf(m)) : StringUtils.equals(item.getChoiceName(), m))
                                .findAny()
                                .orElse(null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                map.put(preChoice.getSpecialDiseaseName(), choiceInfoCheckboxValList);
            }

        }
        return map;
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }

    private String buildAmountInfo(InfoReasonableAmountResultVo reasonableAmountResultVo) {
        return "（"+ "未来花费建议为"+reasonableAmountResultVo.getAdviseMinAmount() + "-"
                +  reasonableAmountResultVo.getAdviseMaxAmount()+ "w）";
    }
}
