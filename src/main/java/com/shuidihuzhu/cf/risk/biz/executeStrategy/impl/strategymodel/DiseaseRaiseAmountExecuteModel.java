package com.shuidihuzhu.cf.risk.biz.executeStrategy.impl.strategymodel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseDataRpcBiz;
import com.shuidihuzhu.cf.risk.biz.disease.RiskDiseaseTreatmentProjectRpcBiz;
import com.shuidihuzhu.cf.risk.biz.executeStrategy.ExecuteStrategyModelTemplate;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.risk.service.DiseaseRpcService;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 判断疾病是否可发起策略
 *
 * <AUTHOR>
 * @date 2020/9/23
 */
@Service
public class DiseaseRaiseAmountExecuteModel extends ExecuteStrategyModelTemplate {

    @Autowired
    private DiseaseRpcService diseaseRpcService;

    @Override
    public String getCode() {
        return "disease_amount_judge_raise";
    }


    @Override
    public void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                           DiseaseStrategyResponse diseaseStrategyResponse,
                           RiskExecuteStrategyTransit transit) {
        List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfoList =
                JSONObject.parseArray(diseaseStrategyRequest.getSpecialRaiseChoiceInfo(), SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo.class );
        if (CollectionUtils.isNotEmpty(specialDiseaseChoiceInfoList)) {
            return;
        }
        //判断新的
        Response<SpecialDiseaseChoiceInfoVo> response =
                    diseaseRpcService.specialRaiseChoiceInfo(transit.getMergeResult());
        SpecialDiseaseChoiceInfoVo diseaseChoiceInfoVo = response.getData();
        if (CollectionUtils.isEmpty(diseaseChoiceInfoVo.getSpecialDiseaseChoiceInfoList())) {
            return;
        }
        transit.setHaNext(false);
        DiseaseStrategyResultInfo diseaseStrategyResultInfo = new DiseaseStrategyResultInfo();
        diseaseStrategyResultInfo.setSpecialDiseaseChoiceInfoList(diseaseChoiceInfoVo.getSpecialDiseaseChoiceInfoList());
        diseaseStrategyResponse.setDiseaseAmountStrategyResult(diseaseStrategyResultInfo);
    }

    @Override
    protected DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum) {
        return null;
    }

    @Override
    public boolean preDecide() {
        return true;
    }
}
