package com.shuidihuzhu.cf.risk.biz.executeStrategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog;
import com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyTransit;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/23
 */
@Data
public abstract class ExecuteStrategyModelTemplate {


    public abstract String getCode();

    public abstract boolean preDecide();

    /**
     * 执行具体的策略
     * @param diseaseStrategyRequest 请求参数
     * @param diseaseStrategyResponse   返回参数
     * @param transit
     * @return
     */
    public abstract void doStrategy(DiseaseStrategyRequest diseaseStrategyRequest,
                                                         DiseaseStrategyResponse diseaseStrategyResponse, RiskExecuteStrategyTransit transit);

    protected DiseaseStrategyResultInfo resultInfo(DiseaseStrategyResultEnum resultEnum) {
        return resultInfo(resultEnum, Lists.newArrayList());

    }

    protected DiseaseStrategyResultInfo resultInfo(DiseaseStrategyResultEnum resultEnum, List<String> ocrDiseseaNameList) {
        if (resultEnum == null){
            return resultInfo(resultEnum, "", ocrDiseseaNameList);
        }
        return resultInfo(resultEnum, resultEnum.getHint(), ocrDiseseaNameList);
    }

    protected DiseaseStrategyResultInfo resultInfo(DiseaseStrategyResultEnum resultEnum, String hit, List<String> ocrDiseseaNameList) {
        if (resultEnum == null){
            return new DiseaseStrategyResultInfo(0 ,"", "", Lists.newArrayList(),
                    ocrDiseseaNameList);
        }
        return new DiseaseStrategyResultInfo(resultEnum.getCode(), resultEnum.getDesc(), hit, Lists.newArrayList(),
                ocrDiseseaNameList);
    }

    /**
     * 单个策略返回时
     * @param diseaseStrategyEnum
     * @return
     */
    protected  abstract DiseaseStrategyResultEnum getResultEnum(DiseaseStrategyEnum diseaseStrategyEnum);

    /**
     * 针对一个策略可能返回多个值  进行处理
     * @param diseaseStrategyEnum
     * @param diseaseStrategyResultEnumMap
     * @return
     */
    protected  DiseaseStrategyResultEnum getMupliteResultEnum(int diseaseStrategyEnum,
                                                              Map<DiseaseStrategyEnum, DiseaseStrategyResultEnum> diseaseStrategyResultEnumMap) {
        return diseaseStrategyResultEnumMap.get(DiseaseStrategyEnum.findByCode(diseaseStrategyEnum));
    }

    protected  DiseaseStrategyResultEnum getResultEnum(int diseaseStrategyEnum){
        return getResultEnum(DiseaseStrategyEnum.findByCode(diseaseStrategyEnum));
    }

    /**
     *
     * @param classifyVoV2List  归一结果
     * @param diseaseInfoList   归一结果对应的发起状态
     * @param treatmentProjectMap   治疗方案
     * @return
     */
    protected List<DiseaseInfoVo> buildList(List<DiseaseClassifyVOV2> classifyVoV2List,
                                          List<DiseaseInfoVo> diseaseInfoList,
                                          Map<String, List<RiskDiseaseTreatmentProject>> treatmentProjectMap) {
        return classifyVoV2List.stream().map( v ->{
            DiseaseInfoVo diseaseInfoVo = new DiseaseInfoVo();
            diseaseInfoVo.setDiseaseName(v.getDisease());

            List<DiseaseInfoVo> diseaseDataList = diseaseInfoList.stream().filter( diseaseData ->
                    v.getNorm().contains(diseaseData.getDiseaseName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(diseaseDataList)){
                diseaseInfoVo.setDiseaseType(RiskDiseaseData.RaiseTypeEnum.DEFAULT.getCode());
                return diseaseInfoVo;
            }
            //写入发起类型
            RiskDiseaseData.RaiseTypeEnum currentType = getCurrentType(diseaseDataList);
            //获取疾病
            for (DiseaseInfoVo vo : diseaseDataList) {
                List<RiskDiseaseTreatmentProject> riskDiseaseTreatmentProjects = treatmentProjectMap.get(vo.getDiseaseName());
                if (CollectionUtils.isNotEmpty(riskDiseaseTreatmentProjects)){
                    diseaseInfoVo.setTreatmentInfo(riskDiseaseTreatmentProjects.stream()
                            .map(RiskDiseaseTreatmentProject::getProjectName).collect(Collectors.joining(",")));
                    currentType = riskDiseaseTreatmentProjects.get(0).getRaiseType() == 1 ?
                            RiskDiseaseData.RaiseTypeEnum.CAN_RAISE :  RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE;
                }
            }
            diseaseInfoVo.setDiseaseType(currentType  == null ?
                    RiskDiseaseData.RaiseTypeEnum.CAN_NOT_RAISE.getCode() : currentType.getCode());
            return diseaseInfoVo;
        }).collect(Collectors.toList());
    }


    private RiskDiseaseData.RaiseTypeEnum getCurrentType(List<DiseaseInfoVo> diseaseDataList) {
        RiskDiseaseData.RaiseTypeEnum   currentType = null;
        for (DiseaseInfoVo riskDiseaseData : diseaseDataList) {
            if (riskDiseaseData.getDiseaseType() == RiskDiseaseData.RaiseTypeEnum.CAN_RAISE.getCode()){
                currentType = RiskDiseaseData.RaiseTypeEnum.CAN_RAISE;
                break;
            } else if (riskDiseaseData.getDiseaseType()
                    == RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE.getCode()) {
                currentType = RiskDiseaseData.RaiseTypeEnum.SPECIAL_RAISE;
            }
        }
        return currentType;
    }


    protected Map<String, List<RiskDiseaseTreatmentProject>> getTreatmentMap(Map<String, List<Long>> specialRaiseChoiceMap,
                                                                           List<RiskDiseaseTreatmentProject> treatmentProjects) {
        Map<String, List<RiskDiseaseTreatmentProject>> map = Maps.newHashMap();
        for (String disease : specialRaiseChoiceMap.keySet()){
            List<RiskDiseaseTreatmentProject> list = Lists.newArrayList();
            List<Long> currentTreatmentIds= specialRaiseChoiceMap.get(disease);
            for (RiskDiseaseTreatmentProject treatmentProject : treatmentProjects){
                if (currentTreatmentIds.contains(treatmentProject.getId())){
                    list.add(treatmentProject);
                }
            }
            map.put(disease, list);
        }
        return map;
    }
}
