<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.CfRiskCommentDao">

    <sql id="table">
        `cf_risk_comment`
    </sql>

    <sql id="selectFields">
        `id`,
        `case_id`,
        `biz_id`,
        `user_id`,
        `content`,
        `sensitive_word`,
        `sensitive_status`,
        `type`,
        `create_time`
    </sql>



    <select id="getByCommentIds" resultType="com.shuidihuzhu.cf.risk.model.CfRiskComment">
        select <include refid="selectFields"/>
        from <include refid="table"/>
        where `id` in
        <foreach collection="commentIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and `is_delete` = 0
    </select>



</mapper>