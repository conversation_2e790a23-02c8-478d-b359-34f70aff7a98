<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="encrypt_mobile_bind" jdbcType="VARCHAR" property="encryptMobileBind" />
    <result column="encrypt_id_card" jdbcType="VARCHAR" property="encryptIdCard" />
    <result column="encrypt_mobile" jdbcType="VARCHAR" property="encryptMobile" />
    <result column="user_id_bind" jdbcType="BIGINT" property="userIdBind" />
    <result column="encrypt_born_card" jdbcType="VARCHAR" property="encryptBornCard" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="operate_reason" jdbcType="VARCHAR" property="operateReason" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, encrypt_mobile_bind, encrypt_id_card, encrypt_mobile, user_id_bind, 
    encrypt_born_card, user_name, operate_reason, operate_id, operate_name, is_delete, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listByIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where id in
  	<foreach collection="collection" open="(" item="id" separator="," close=")">
  	 #{id,jdbcType=BIGINT}
	</foreach>
  </select>
  <select id="getByUserIdOrUserIdBind" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where user_id = #{userId,jdbcType=BIGINT}
  	union all
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where user_id_bind = #{userId,jdbcType=BIGINT}
  </select>

    <select id="listByQuery" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from risk_blacklist_data
        <where>
            is_delete = 0
            <if test="idCard != null and idCard != ''">
                and encrypt_id_card = #{idCard,jdbcType=VARCHAR}
            </if>
            <if test="bornCard != null and bornCard != ''">
                and encrypt_born_card = #{bornCard,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>
</mapper>