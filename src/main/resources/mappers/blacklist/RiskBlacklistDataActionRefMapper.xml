<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataActionRefDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataActionRef">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="action_id" jdbcType="BIGINT" property="actionId" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_id, action_id, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data_action_ref
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="listByLimitOptionalActionIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data_action_ref
  	where id > #{previousId,jdbcType=BIGINT}
  	<if test="actionIds != null and actionIds.size() != 0">
	    and action_id in
	    <foreach collection="actionIds" open="(" item="actionId" separator="," close=")">
		    #{actionId,jdbcType=BIGINT}
		</foreach>
	</if>
  	and is_delete = 0
  	limit #{limit,jdbcType=INTEGER}
  </select>
  <select id="listByDataIdAndActionId" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data_action_ref
  	where data_id in
  	<foreach collection="dataIds" open="(" item="dataId" separator="," close=")">
	    #{dataId,jdbcType=BIGINT}
	</foreach>
  	and action_id = #{actionId,jdbcType=BIGINT}
  	and is_delete = 0
  	limit 1
  </select>
</mapper>