<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.blacklist.RiskBlacklistDataTypeRefDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.model.po.blacklist.RiskBlacklistDataTypeRef">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="action_ids" jdbcType="VARCHAR" property="actionIds" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_id, type_id, action_ids, type_name, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data_type_ref
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listByDataIds" resultMap="BaseResultMap">
  	select data_id, type_id
  	from risk_blacklist_data_type_ref
  	where data_id in
  	<foreach collection="collection" open="(" item="dataId" separator="," close=")">
	    #{dataId,jdbcType=BIGINT}
	</foreach>
	and is_delete = 0
  </select>
  
</mapper>