<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.hit.RiskStrategyHitRecordDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.model.po.hit.RiskStrategyHitRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="INTEGER" property="caseId" />
    <result column="launch_time" jdbcType="TIMESTAMP" property="launchTime" />
    <result column="hit_phase" jdbcType="INTEGER" property="hitPhase" />
    <result column="risk_strategy" jdbcType="INTEGER" property="riskStrategy" />
    <result column="second_strategy" jdbcType="INTEGER" property="secondStrategy" />
    <result column="risk_type" jdbcType="INTEGER" property="riskType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="lifting" jdbcType="TINYINT" property="lifting" />
    <result column="handle_count" jdbcType="INTEGER" property="handleCount" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.shuidihuzhu.cf.risk.model.po.hit.RiskStrategyHitRecord">
    <result column="hit_info" jdbcType="LONGVARCHAR" property="hitInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, launch_time, hit_phase, risk_strategy, second_strategy, risk_type, `status`,
    `result`, `action`, lifting, handle_count, operate_id, operate_name, is_delete, create_time,
    update_time
  </sql>
  <sql id="Blob_Column_List">
    hit_info
  </sql>
  <select id="listByCaseIdPhase" resultMap="BaseResultMap">
    select lifting,hit_info, risk_strategy, second_strategy
    from risk_strategy_hit_record
    where case_id = #{caseId,jdbcType=INTEGER}
    and hit_phase = #{hitPhase,jdbcType=INTEGER}
  </select>
</mapper>