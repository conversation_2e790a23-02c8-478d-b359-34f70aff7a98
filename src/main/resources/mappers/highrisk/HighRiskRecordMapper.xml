<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.highrisk.HighRiskRecordDao">

    <sql id="table">
        high_risk_record
    </sql>

    <select id="getLastByCondition" resultType="com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDO">
        select * from <include refid="table"/>
        <where>
            case_id = #{caseId}
            and order_id = #{workOrderId}
            and trigger_source = #{source}
            and is_delete = 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getListByCaseId" resultType="com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDO">
        select * from <include refid="table"/>
        <where>
            case_id = #{caseId}
            and is_delete = 0
        </where>
    </select>

    <insert id="insert">
        insert into <include refid="table"/>
        (case_id, order_id ,operator_id, hit, trigger_source, hit_codes, req_param, version)
        values
        (#{caseId}, #{orderId}, #{operatorId}, #{hit}, #{triggerSource}, #{hitCodes}, #{reqParam}, #{version})
    </insert>
</mapper>