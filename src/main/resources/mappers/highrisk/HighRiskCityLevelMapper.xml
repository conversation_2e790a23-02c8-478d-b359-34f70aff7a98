<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.highrisk.HighRiskCityLevelDao">

    <sql id="table">
        high_risk_city_level
    </sql>

    <select id="getLevelByCityCode" resultType="java.lang.String">
        select city_level
        from <include refid="table"></include>
        <where>
            city_code = #{code}
        </where>
    </select>
</mapper>