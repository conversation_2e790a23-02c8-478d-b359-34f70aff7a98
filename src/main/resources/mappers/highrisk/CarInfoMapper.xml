<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.highrisk.CarInfoDao">
    <sql id="table">
        car_info
    </sql>

    <sql id="selectFields">
        `id`,
        `car_brand`,
        `luxury`
    </sql>

    <select id="getCarRiskInfo" resultType="com.shuidihuzhu.cf.risk.model.highrisk.CarInfoDO">
        select <include refid="selectFields"/>
        from <include refid="table"></include>
        where is_delete = 0
        and car_brand = #{carInfo}
    </select>


</mapper>