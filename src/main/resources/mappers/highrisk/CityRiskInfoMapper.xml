<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.highrisk.CityRiskInfoDao">

    <sql id="table">
        city_risk_info
    </sql>

    <sql id="selectFields">
        `id`,
        `province`,
        `city`,
        `county`,
        `city_level`,
        `use_community`,
        `use_floor`,
        `city_amount_threshold`
    </sql>

    <select id="getCityRiskInfo" resultType="com.shuidihuzhu.cf.risk.model.highrisk.CityRiskInfoDO">
        select <include refid="selectFields"/>
        from <include refid="table"/>
        where is_delete = 0
        and `province`=#{province}
        and `city`=#{city}
        and `county`=#{county}
    </select>

    <select id="getMinAmountCityRiskInfo" resultType="java.lang.Integer">
        select city_amount_threshold
        from <include refid="table"/>
        where is_delete = 0
        order by city_amount_threshold asc
        limit 1
    </select>

</mapper>