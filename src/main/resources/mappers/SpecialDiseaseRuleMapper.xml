<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.SpecialDiseaseRuleDao">

    <sql id="tableName">
        special_disease_rule
    </sql>

    <sql id="selectFields">
        `id`,
        `disease_class`,
        `disease_class_name`,
        `disease_contain_class_name`,
        `merge_amount`,
        `arise_count`
    </sql>

    <select id="getByClassName" resultType="com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` = #{diseaseClassName}
        AND `is_delete` = 0
    </select>

    <select id="findByClassNameList" resultType="com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` IN
        <foreach collection="diseaseClassNameList" item="diseaseClassName" open="(" close=")" separator=",">
            #{diseaseClassName}
        </foreach>
        AND `is_delete` = 0
    </select>

    <select id="findAllSpecialClassName" resultType="java.lang.String">
        select `disease_class_name`
        from <include refid="tableName"/>
        where `is_delete` = 0
    </select>

    <select id="findByClassNameListAndType"
            resultType="com.shuidihuzhu.cf.risk.model.disease.SpecialDiseaseRule">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` IN
        <foreach collection="diseaseClassNameList" item="diseaseClassName" open="(" close=")" separator=",">
            #{diseaseClassName}
        </foreach>
        AND `type` = #{type}
        AND `is_delete` = 0
    </select>


</mapper>