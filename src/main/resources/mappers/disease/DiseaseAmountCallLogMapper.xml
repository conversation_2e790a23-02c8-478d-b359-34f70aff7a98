<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.DiseaseAmountCallLogDao">

    <sql id="tableName">
        disease_amount_call_log
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.model.disease.DiseaseAmountCallLog">
        insert into <include refid="tableName"/>
        (case_id, work_order_id, target_amount_in_fen, can_raise_status, request_disease,
        advise_min_amount, advise_max_amount, calculate_disease_name,
        treatment_info, channel)
        values (#{caseId}, #{workOrderId}, #{targetAmountInFen}, #{canRaiseStatus},#{requestDisease},
        #{adviseMinAmount}, #{adviseMaxAmount}, #{calculateDiseaseName},
        #{treatmentInfo}, #{channel})
    </insert>

</mapper>