<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.RiskDiseaseDataRpcDao">

    <sql id="tableName">
        risk_disease_data
    </sql>

    <sql id="selectFields">
        `id` ,
        `disease_class_name` ,
        `medical_name` ,
        `normal_name`,
        `raise_type` ,
        `disease_merge_rule`,
        `choice_type`,
        `high_risk`
    </sql>

    <select id="getByClassName" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` = #{diseaseClassName}
        AND `is_delete` = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `id` = #{diseaseId}
        AND `is_delete` = 0
    </select>


    <select id="getByClassNameList" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` IN
        <foreach collection="diseaseNameList" item="diseaseName" open="(" separator="," close=")">
            #{diseaseName}
        </foreach>
        AND `is_delete` = 0
    </select>

    <select id="findAllDiseaseName" resultType="java.lang.String">
        select `disease_class_name`
        from <include refid="tableName"/>
        where `is_delete` = 0
    </select>

    <select id="findByDiseaseName" resultType="java.lang.String">
        select disease_class_name
        from <include refid="tableName"/>
        <where>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(diseasNameKeyWord)">
                disease_class_name like concat('%', #{diseasNameKeyWord,jdbcType=VARCHAR}, '%')
            </if>
            AND `is_delete` = 0
        </where>
    </select>
    <select id="getByIdList" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `id` IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND `is_delete` = 0
    </select>
</mapper>