<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.RiskDiseaseTreatmentProjectRpcDao">

    <sql id="tableName">
        risk_disease_treatment_project
    </sql>

    <sql id="selectFields">
        `id`,
        `project_name`,
        `disease_id`,
        `project_merge_rule`,
        `min_treatment_fee`,
        `max_treatment_fee`,
        `create_time`,
        `no_same_time_id`,
        `raise_type`
    </sql>


    <select id="findByDiseaseIdList" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_id` IN
        <foreach collection="diseaseIdList" item="diseaseId" separator="," open="(" close=")">
            #{diseaseId}
        </foreach>
        and `is_delete` = 0
        order By `id`
    </select>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `id` IN
        <foreach collection="treatmentIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and `is_delete` = 0
    </select>
    <select id="findByTreatmentNameAndDiseaseId"
            resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_id` = #{diseaseId}
        and `project_name` = #{treatmentName}
        and `is_delete` = 0
        order by id desc limit 1
</select>
    <select id="findByTreatmentName"
            resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where  `project_name` = #{treatmentName}
        and `is_delete` = 0
    </select>
    <select id="findByLikeTreatmentName"
            resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where  `project_name` like concat('%',#{likeTreatmentName},'%')
        and `is_delete` = 0
    </select>
    <select id="findByLikeTreatmentNameAndDiseaseId"
            resultType="com.shuidihuzhu.cf.risk.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where  `project_name` like concat('%',#{likeTreatmentName},'%')
        and `disease_id` = #{diseaseId}
        and `is_delete` = 0
        order by id desc limit 1
    </select>


</mapper>