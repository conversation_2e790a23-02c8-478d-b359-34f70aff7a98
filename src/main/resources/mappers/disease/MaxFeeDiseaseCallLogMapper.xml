<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.MaxFeeDiseaseCallLogDao">

    <sql id="tableName">
        max_fee_disease_call_log
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.risk.model.disease.MaxFeeDiseaseCallLog">
        insert into <include refid="tableName"/>
        (case_id, diseases, norm_disease, max_fee_str, response_str)
        values (#{caseId}, #{diseases}, #{normDisease}, #{maxFeeStr},#{responseStr})
    </insert>

</mapper>