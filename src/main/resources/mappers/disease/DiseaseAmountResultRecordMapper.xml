<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.DiseaseAmountResultRecordDao">

    <sql id="tableName">
        disease_amount_result_record
    </sql>

    <sql id="selectFields">
      `case_id`,
      `work_order_id`,
      `work_order_type_code`,
      `pre_pose_id`,
      `reason_amount_type`,
      `target_amount_in_fen`,
      `amount_reasonable`,
      `amount_reasonable_type`,
      `disease_name`,
      `disease_norm_name`,
      `advise_min_amount`,
      `advise_max_amount`,
      `calculate_disease_name`,
      `treatment_info`,
      `operator_id`,
      `operator`,
      `version`,
      `create_time`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord">
        insert into <include refid="tableName"/>
        (case_id, work_order_id, work_order_type_code, pre_pose_id, reason_amount_type, target_amount_in_fen, amount_reasonable, amount_reasonable_type,
        disease_name, disease_norm_name, advise_min_amount, advise_max_amount, calculate_disease_name, treatment_info, operator_id, operator, version)
        values (#{caseId}, #{workOrderId}, #{workOrderTypeCode}, #{prePoseId}, #{reasonAmountType}, #{targetAmountInFen},
            #{amountReasonable}, #{amountReasonableType}, #{diseaseName}, #{diseaseNormName}, #{adviseMinAmount},
            #{adviseMaxAmount}, #{calculateDiseaseName}, #{treatmentInfo}, #{operatorId}, #{operator}, #{version})
    </insert>

    <select id="getRecordByCaseIdAndReasonTypeList"
            resultType="com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `case_id` = #{caseId}
        AND `reason_amount_type` in
        <foreach collection="reasonTypeList" open="(" separator="," close=")" item="reasonType">
            #{reasonType}
        </foreach>
        AND `is_delete` = 0
    </select>

</mapper>