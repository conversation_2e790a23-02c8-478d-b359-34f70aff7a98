<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.UserIncomeThirdDao">

    <sql id="tableName">
        `user_income_third`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.model.UserIncomeThird" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
            (id_card,real_name,mobile,third_type,third_status,third_data,model_type,order_no,seq_no,third_no)
        values
            (#{idCard},#{realName},#{mobile},#{thirdType},#{thirdStatus},#{thirdData},#{modelType},#{orderNo},#{seqNo},#{thirdNo})
    </insert>

    <select id="getByIdCard" resultType="com.shuidihuzhu.cf.risk.model.UserIncomeThird">
        select *
        from <include refid="tableName" />
        where `id_card`=#{idCard} AND `real_name`=#{realName} AND `third_type`=#{thirdType} AND `third_status`=#{thirdStatus}
            <if test="modelType != null">
                AND `model_type`=#{modelType}
            </if>
        order by id desc
        limit 1
    </select>

</mapper>