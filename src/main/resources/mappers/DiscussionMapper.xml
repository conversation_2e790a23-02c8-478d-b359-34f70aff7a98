<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.DiscussionDao">

    <sql id="TABLE">
        `discussion`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.model.Discussion">
        insert into <include refid="TABLE"/>
        (case_id, info_uuid, reason, other_reason, other_fund_reason,
        status, close_hour)
        values (#{caseId}, #{infoUuid},#{reason}, #{otherReason},#{otherFundReason},
        #{status},#{closeHour})
    </insert>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.risk.model.Discussion">
        select * from <include refid="TABLE"/>
        where case_id = #{caseId}
        and `status` > 0
        and is_delete = 0
        order by `id` desc limit 1
    </select>

    <update id="updateStatus">
        update <include refid="TABLE"/>
        set status = #{newStatus}
        where id = #{id} and status = #{oldStatus}
    </update>

    <update id="updateCheckStatus">
        update <include refid="TABLE"/>
        set check_status = #{newStatus}
        where id = #{discussionId} and check_status = #{oldStatus}
    </update>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.model.Discussion">
        select * from <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>

    <update id="updateDelById">
        update <include refid="TABLE"/>
        set is_delete = 1
        where id = #{id} and is_delete = 0
    </update>

    <update id="submit">
        update <include refid="TABLE"/>
        set `check_status` = #{checkStatus},
        `title` = #{discussionTitle},
        `description` = #{discussionDesc},
        `images` = #{discussionImgList}
        where `case_id` = #{caseId}
        and is_delete = 0
        order By `id` desc limit 1
    </update>

    <select id="findByCaseId" resultType="com.shuidihuzhu.cf.risk.model.Discussion">
        select * from <include refid="TABLE"/>
        where `case_id` in
        <foreach collection="caseIdList" open="(" separator="," close=")" item="caseId">
            #{caseId}
        </foreach>
        AND `status` > 0
        AND `is_delete` = 0
    </select>

    <select id="countByCaseId" resultType="java.lang.Integer">
        select count(*) from <include refid="TABLE"/>
        where case_id = #{caseId}
    </select>



</mapper>