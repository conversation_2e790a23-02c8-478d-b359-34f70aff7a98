<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.CfRiskAccidentCaseRecordDao">

    <sql id="table">
        cf_risk_accident_case_record
    </sql>

    <insert id="save">
        insert into <include refid="table"/>
        (trigger_timing, case_id, operator_id, work_order_id, call_result,
        case_title, case_content)
        values (#{triggerTiming}, #{caseId},#{operatorId}, #{workOrderId},#{callResult},
        #{caseTitle},#{caseContent})
    </insert>

</mapper>