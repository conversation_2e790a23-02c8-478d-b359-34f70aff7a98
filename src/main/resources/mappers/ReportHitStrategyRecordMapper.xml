<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.ReportHitStrategyRecordDao">

    <sql id="tableName">
        report_hit_strategy_record
    </sql>


    <insert id="batchInsert" parameterType="com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord">
        insert into
        <include refid="tableName"/>
        (case_id,report_id,user_id,nick_name,real_name,strategy,hit_type,type,type_code,
            encrypt_mobile, encrypt_report_mobile, encrypt_id_card)
        values
        <foreach collection="reportHitStrategyRecordList" item="reportHitStrategyRecord" separator=",">
            (#{reportHitStrategyRecord.caseId},#{reportHitStrategyRecord.reportId},#{reportHitStrategyRecord.userId},
            #{reportHitStrategyRecord.nickName},#{reportHitStrategyRecord.realName},#{reportHitStrategyRecord.strategy},
            #{reportHitStrategyRecord.hitType},#{reportHitStrategyRecord.type},#{reportHitStrategyRecord.typeCode},
            #{reportHitStrategyRecord.encryptMobile},#{reportHitStrategyRecord.encryptReportMobile},#{reportHitStrategyRecord.encryptIdCard})
        </foreach>
    </insert>
    <update id="updateEncryptById">
        update
        <include refid="tableName"/>
        set encrypt_mobile = #{encryptMobile}, encrypt_report_mobile = #{encryptReportMobile}, encrypt_id_card = #{encryptIdCard}
        where id = #{id}
    </update>
    <update id="updateMobileById">
        update
        <include refid="tableName"/>
        set mobile = #{mobile}, report_mobile = #{reportMobile}, id_card = #{idCard}
        where id = #{id}
    </update>

    <select id="getByReportIds" resultType="com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord">
        select * from
        <include refid="tableName"/>
        where report_id in
        <foreach collection="reportIds" separator="," open="(" close=")" item="reportId">
            #{reportId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="listByCursor" resultType="com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord">
        select * from
        <include refid="tableName"/>
        where id > #{lastId}
        limit #{limit}
    </select>


</mapper>