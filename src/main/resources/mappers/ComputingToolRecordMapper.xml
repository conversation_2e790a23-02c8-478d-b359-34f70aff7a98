<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.ComputingToolRecordDao">

    <sql id="table">
        `computing_tool_record`
    </sql>

    <sql id="selectFields">
        `id`,
        `ext_id`,
        `ext_name`,
        `source_json`,
        `result_json`,
        `operator`,
        `operator_org`,
        `create_time`
    </sql>
    <insert id="insert">
        insert into <include refid="table"/>
        (`ext_id`, `ext_name`, `source_json`, `result_json`, `operator`, `operator_org`)
        values
        (#{extId}, #{extName}, #{sourceJson}, #{resultJson}, #{operator}, #{operatorOrg})
    </insert>
    <select id="selectByExtNameAndExtId" resultType="com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO">
        select <include refid="selectFields"/>
        from <include refid="table"/>
        where ext_name = #{extName} and ext_id = #{extId} and is_delete = 0
        order by id desc
    </select>
    <select id="selectById" resultType="com.shuidihuzhu.cf.risk.model.ComputingToolRecordDO">
        select <include refid="selectFields"/>
        from <include refid="table"/>
        where id = #{id} and is_delete = 0
    </select>


</mapper>