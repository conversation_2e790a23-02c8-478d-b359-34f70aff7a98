<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyRelDao">

    <sql id="table">
        `risk_execute_strategy_rel`
    </sql>

    <select id="getByStrategyId"
            resultType="com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyRel">
        select *
        from  <include refid="table"/>
        where strategy_id = #{strategyId}
        AND is_delete = 0
    </select>


</mapper>