<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyLogDao">

    <sql id="table">
            `risk_execute_strategy_log`
    </sql>

    <insert id="saveList" parameterType="com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog">
        insert into <include refid="table"/>
        (case_id, work_order_id, execute_time, strategy_name, strategy_result, strategy_result_desc, operator, other_info)
        values
        <foreach collection="riskExecuteStrategyLogs" separator="," item="log">
            (#{log.caseId}, #{log.workOrderId}, #{log.executeTime}, #{log.strategyName},
            #{log.strategyResult}, #{log.strategyResultDesc}, #{log.operator}, #{log.otherInfo})
        </foreach>
    </insert>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategyLog">
        select *
        from  <include refid="table"/>
        where case_id  =#{caseId}
        AND `is_delete` = 0
    </select>


</mapper>