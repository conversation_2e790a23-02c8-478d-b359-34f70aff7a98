<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.executestrategy.RiskExecuteStrategyDao">

    <sql id="table">
        `risk_execute_strategy`
    </sql>

    <select id="getByType" resultType="com.shuidihuzhu.cf.risk.model.executestrategy.RiskExecuteStrategy">
        select  *
        from <include refid="table"/>
        where type = #{type}
        AND `is_delete` = 0
        order by id desc limit 1
    </select>


</mapper>