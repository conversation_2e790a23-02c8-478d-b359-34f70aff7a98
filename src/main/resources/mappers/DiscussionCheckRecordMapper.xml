<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.DiscussionCheckRecordDao">

    <sql id="TABLE">
        `discussion_check_record`
    </sql>


    <select id="findLastByDiscussId" resultType="com.shuidihuzhu.cf.risk.model.DiscussionCheckRecord">
        select * from <include refid="TABLE"/>
        where discussion_id = #{discussionId} and is_delete = 0
        order by id desc limit 1
    </select>


</mapper>