<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.dao.TrustPersonelInfoDao">

    <sql id="tableName">
        `trust_personel_info`
    </sql>

    <insert id="addInfo">
        insert ignore
        <include refid="tableName"/>
        (`name`, `id_card`, `trust_msg`,`third_type`)
        values
        (#{name}, #{idCard}, #{trustMsg}, #{thirdType})
    </insert>

    <select id="getByNameAndCard" resultType="com.shuidihuzhu.cf.risk.model.TrustPersonelInfo">
        select `name`, `id_card`,`trust_msg` from
        <include refid="tableName"/>
        where `name` = #{name} and `id_card` = #{idCard}
    </select>

    <update id="updateInfo">
        update <include refid="tableName"/>
        set trust_msg = #{trustMsg}
        where `name` = #{name} and `id_card` = #{idCard}
    </update>



</mapper>