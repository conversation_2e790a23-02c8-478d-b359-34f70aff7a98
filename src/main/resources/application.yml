server:
  port: 18240

spring:
  http:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
    allow-circular-references: true
  config:
    use-legacy-processing: true


auth:
  saas:
    appCode: sl56s1sw
    login:
      interceptorEnable: false
    permission:
      interceptorEnable: false

rocketmq:
  consumer:
    consumers:
      BLACK_LIST_CACHE_MULTI_POD_UPDATE_cf-risk-rpc-api:
        message-model: BROADCASTING

