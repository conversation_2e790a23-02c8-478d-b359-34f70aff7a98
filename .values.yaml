app:
  group: cf-risk  #项目的group名字
  name: cf-risk-rpc-api   #项目的application.name
service:
  port: 18240  #http端口号
#  grpcPort:    #grpc端口号
ingress:   #如果是需要对外暴露api的服务，则需要加入ingress段，否则可以删除
  enabled: false
  paths:
healthPath: /actuator/health
            #指定需要映射到本服务的path列表。可以是多个 - /api/ad - /admin/ad
#secrets:   #如果项目需要引用支付证书，则引入该段
#  - name: pay-certs
#    path: /data/cert
#    secretName: pay-certs
