<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.42</version>
    </parent>

    <groupId>com.shuidihuzhu.cf-risk</groupId>
    <artifactId>cf-risk-rpc-api</artifactId>
    <version>1.0.158-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>cf-risk-rpc-api</name>
    <description>水滴筹风控B端项目</description>

    <scm>
        <connection>scm:git:http://git.shuiditech.com/cf-risk/cf-risk-rpc-api.git</connection>
        <developerConnection>scm:git:**********************:cf-risk/cf-risk-rpc-api.git</developerConnection>
        <url>http://git.shuiditech.com/cf-risk/cf-risk-rpc-api.git</url>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>
        <cf-risk-client.version>1.0.132</cf-risk-client.version>
        <account-grpc-client.version>2.2.125</account-grpc-client.version>
        <wx-grpc-client.version>1.0.100</wx-grpc-client.version>
        <cf-finance-client.version>3.0.35</cf-finance-client.version>
        <cf-api-client.version>3.6.500</cf-api-client.version>
        <cf-model.version>3.6.500</cf-model.version>
        <apollo-client.version>1.1.0</apollo-client.version>
        <cf-client.version>1.2.391</cf-client.version>
        <servicelog-sdk-java.version>2.0.93</servicelog-sdk-java.version>
        <servicelog-sdk-meta.java.version>1.0.8</servicelog-sdk-meta.java.version>
        <pf-commen-v2.version>1.0.37</pf-commen-v2.version>
        <cf-material-client-version>9.0.92</cf-material-client-version>
        <shuidi-wx-provider.version>2.0.95</shuidi-wx-provider.version>
        <weixin-java-mp.version>1.3.68</weixin-java-mp.version>
        <baidu-aip.version>4.4.0</baidu-aip.version>
        <cf-ugc-client.version>9.0.99</cf-ugc-client.version>
        <cf-client-base.version>9.0.81</cf-client-base.version>
        <shuidi-auth-saas-client.version>0.0.15</shuidi-auth-saas-client.version>

        <cf-api-pure-client.version>1.0.20</cf-api-pure-client.version>
        <cf-admin-api-pure-client.version>9.0.102</cf-admin-api-pure-client.version>
        <cf-enhancer-starter.version>1.0.92</cf-enhancer-starter.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-ocean-client</artifactId>
            <version>0.0.27</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-pure-client</artifactId>
            <version>${cf-api-pure-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>msg-send-common</artifactId>
                    <groupId>com.shuidihuzhu.msg</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
            <version>${cf-admin-api-pure-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
            <version>${cf-ugc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client-base</artifactId>
            <version>${cf-client-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
            <version>${cf-enhancer-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-grpc-client</artifactId>
            <version>${account-grpc-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                    <groupId>com.shuidihuzhu.infra</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.wx</groupId>
            <artifactId>wx-grpc-client</artifactId>
            <version>${wx-grpc-client.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf-risk</groupId>
            <artifactId>cf-risk-rpc-client</artifactId>
            <version>${cf-risk-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>web-core-v2</artifactId>
                    <groupId>com.shuidihuzhu.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-client</artifactId>
            <version>${cf-finance-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shuidi-cipher</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-ugc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-okhttp3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
            <version>${cf-api-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-model</artifactId>
            <version>${cf-model.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-core-v2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-ugc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-risk-rpc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf-risk</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <version>${cf-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.infra</groupId>
                    <artifactId>feign-enhance-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>shuidi-cipher</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
            <version>${servicelog-sdk-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
            <version>${servicelog-sdk-meta.java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-common-v2</artifactId>
            <version>${pf-commen-v2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
            <version>${cf-material-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-wx-provider</artifactId>
            <version>${shuidi-wx-provider.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${weixin-java-mp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.client</groupId>
            <artifactId>ai-push-client</artifactId>
            <version>0.0.20</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>${baidu-aip.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.coomon</groupId>
            <artifactId>cf-common-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
            <version>${shuidi-auth-saas-client.version}</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-common-dependencies</artifactId>
                <version>RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <valueRule implementation="com.shuidihuzhu.customrule.ValueRule">
                                    <excludePath>/src/main/java</excludePath>
                                </valueRule>
                                <loggerRule implementation="com.shuidihuzhu.customrule.LoggerRule">
                                    <excludePath>/src/main/java</excludePath>
                                </loggerRule>
                                <!--强制项目没有重复声明的依赖项-->
                                <banDuplicatePomDependencyVersions />
                                <!--其他rule，省略-->
                                <cipherRule implementation="com.shuidihuzhu.rule.CipherRule" />
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
<!--                    <execution>-->
<!--                        <id>enforce-ban-duplicate-classes</id>-->
<!--                        <goals>-->
<!--                            <goal>enforce</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <rules>-->
<!--                                &lt;!&ndash;针对jar检测不出来的class&ndash;&gt;-->
<!--                                <banDuplicateClasses>-->
<!--                                    <ignoreClasses>-->
<!--                                        <ignoreClass>javax.*</ignoreClass>-->
<!--                                        <ignoreClass>org.junit.*</ignoreClass>-->
<!--                                        <ingoreClass>org.aspectj.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.juli.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.commons.logging.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.log4j.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.tomcat.*</ingoreClass>-->
<!--                                        <ingoreClass>io.netty.*</ingoreClass>-->
<!--                                        <ingoreClass>net.jcip.annotations.*</ingoreClass>-->
<!--                                        <ingoreClass>aj.org.objectweb.asm.*</ingoreClass>-->
<!--                                        <ingoreClass>org.xmlpull.v1.*</ingoreClass>-->
<!--                                        <ingoreClass>org.hibernate.validator.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.commons.collections.*</ingoreClass>-->
<!--                                        <ingoreClass>com.shuidihuzhu.cf.service.notice.workwx.*</ingoreClass>-->
<!--                                        <ingoreClass>org.apache.xmlbeans.*</ingoreClass>-->
<!--                                    </ignoreClasses>-->
<!--                                    <findAllDuplicates>true</findAllDuplicates>-->
<!--                                </banDuplicateClasses>-->
<!--                            </rules>-->
<!--                            <fail>true</fail>-->
<!--                        </configuration>-->
<!--                    </execution>-->
                </executions>
                <dependencies>
                    <!-- 这个也需要加上-->
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>1.0.23</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
